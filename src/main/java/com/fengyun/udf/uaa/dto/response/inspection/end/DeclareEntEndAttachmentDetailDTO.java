package com.fengyun.udf.uaa.dto.response.inspection.end;


import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntEndAttachmentDetailDTO", description = "DeclareEntEndAttachmentDetailDTO")
public class DeclareEntEndAttachmentDetailDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 承诺书
     */
    @ApiModelProperty("承诺书")
    private List<AttachmentDTO> commitment;

    /**
     * 推荐表
     */
    @ApiModelProperty("推荐表")
    private List<AttachmentDTO> recommendation;

    /**
     * 申报书
     */
    @ApiModelProperty("申报书")
    private List<AttachmentDTO> applyForm;

    /**
     * 执照
     */
    @ApiModelProperty("执照")
    private List<AttachmentDTO> license;

    /**
     * 审计报告
     */
    @ApiModelProperty("审计报告")
    private List<AttachmentDTO> auditReport;

    /**
     * 知识产权
     */
    @ApiModelProperty("知识产权")
    private List<AttachmentDTO> intellectualProperty;

    /**
     * 支持情况
     */
    @ApiModelProperty("支持情况")
    private List<AttachmentDTO> supportSituation;

    /**
     * 其他附件
     */
    @ApiModelProperty("其他附件")
    private List<AttachmentDTO> other;

    /**
     * 经费的支出明细账或流水
     */
    @ApiModelProperty("经费的支出明细账或流水")
    private List<AttachmentDTO> expendituresDetail;

    /**
     * 考核指标完成情况及阶段性成果的证明材料
     */
    @ApiModelProperty("考核指标完成情况及阶段性成果的证明材料")
    private List<AttachmentDTO> completionMaterials;

}
