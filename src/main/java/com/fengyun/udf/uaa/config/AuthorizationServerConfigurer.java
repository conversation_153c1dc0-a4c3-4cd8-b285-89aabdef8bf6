package com.fengyun.udf.uaa.config;

import com.fengyun.udf.security.config.OAuth2Properties;
import com.fengyun.udf.security.config.UdfWebResponseExceptionTranslator;
import com.fengyun.udf.uaa.config.security.service.BaseUserDetailsService;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.config.annotation.builders.InMemoryClientDetailsServiceBuilder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.TokenStore;

import java.util.List;

/**
 * Created by wangg on 2019/7/4.
 */
@Configuration
@AllArgsConstructor
@EnableAuthorizationServer
public class AuthorizationServerConfigurer extends AuthorizationServerConfigurerAdapter {

    private final OAuth2Properties oAuth2Properties;
    private final AuthenticationManager authenticationManager;
    private final BaseUserDetailsService baseUserDetailsService;
    private TokenStore tokenStore;

    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        InMemoryClientDetailsServiceBuilder builder = clients.inMemory();
        List<OAuth2Properties.WebClientConfiguration> configurations =  oAuth2Properties.getWebClientConfigurations();

        for(OAuth2Properties.WebClientConfiguration configuration:configurations){
            builder.withClient(configuration.getClientId())
                    .secret(new BCryptPasswordEncoder().encode(configuration.getSecret()))
                    .authorizedGrantTypes("implicit", "refresh_token", "password", "authorization_code")
                    .scopes("openid")
                    .accessTokenValiditySeconds(configuration.getAccessTokenValidityInSeconds())
                    .refreshTokenValiditySeconds(configuration.getRefreshTokenValidityInSecondsForRememberMe());
        }
    }

    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints)
            throws Exception {
        endpoints//.reuseRefreshTokens(false)
                .allowedTokenEndpointRequestMethods(HttpMethod.GET, HttpMethod.POST)
                .tokenStore(tokenStore)
                .authenticationManager(authenticationManager)
                .userDetailsService(baseUserDetailsService)
                .exceptionTranslator(new UdfWebResponseExceptionTranslator());
    }

    @Override
    public void configure(AuthorizationServerSecurityConfigurer oauthServer)
            throws Exception {
        oauthServer
                .allowFormAuthenticationForClients()
                .tokenKeyAccess("permitAll()")
                .checkTokenAccess("permitAll()");
    }

}
