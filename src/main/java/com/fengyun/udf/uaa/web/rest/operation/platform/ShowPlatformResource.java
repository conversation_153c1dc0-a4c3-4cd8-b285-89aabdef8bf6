package com.fengyun.udf.uaa.web.rest.operation.platform;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.service.system.PlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/ua/platform")
@Api(value = "平台信息", tags = "平台信息")
public class ShowPlatformResource extends BaseResource {
    @Autowired
    private PlatformService platformService;

    @GetMapping("/info")
    @CustomBaseException
    @AuditLog(method = "/api/ua/platform/info", methodDescription = "平台信息查询", httpMethod = "GET")
    @ApiOperation(value = "平台信息查询")
    public ResponseEntity<ReturnResultDTO<Map<String, String>>> getPlatformInfo() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), platformService.getPlatformInfo(false));
    }

    @GetMapping("/info/name")
    @CustomBaseException
    @AuditLog(method = "/api/ua/platform/info/name", methodDescription = "根据name查询某个信息", httpMethod = "GET")
    @ApiOperation(value = "根据name查询某个信息")
    public ResponseEntity<ReturnResultDTO<Map<String, String>>> getPlatformInfo(@RequestParam("name") String name) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), platformService.getAttributeByName(name));
    }
}
