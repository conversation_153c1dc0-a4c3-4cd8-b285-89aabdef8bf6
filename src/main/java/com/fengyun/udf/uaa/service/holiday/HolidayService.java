package com.fengyun.udf.uaa.service.holiday;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fengyun.udf.cache.redis.client.DictCacheClient;
import com.fengyun.udf.uaa.domain.holiday.Holiday;
import com.fengyun.udf.uaa.mapper.holiday.HolidayMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class HolidayService {

    private final HolidayMapper holidayMapper;
    private static final String url = "http://api.tianapi.com/jiejiari/index?key=0b7ebb9f598cddfdc067da202c84a5b2&type=1&date=";
    private final Map<String, String> holidayMap = new HashMap<>();

    private final DictCacheClient dictCacheClient;

    /**
     * 是否为节假日
     *
     * @param date
     * @return
     */
//    public boolean isHoliday(String date) {
//        LocalDate localDate = LocalDate.parse(date);
//        String str = getConfig(String.valueOf(localDate.getYear()));
//
//        if (StringUtils.isNotBlank(str)) {
//            return str.split(",")[0].contains(date);
//        }
//        return false;
//    }

    public boolean isHoliday(String date) {
        LocalDate localDate = LocalDate.parse(date);

        String value = dictCacheClient.getDict("INSTRUMENT_ORDER_HOLIDAY",String.valueOf(localDate.getYear()));

        if (StringUtils.isNotBlank(value)) {
            return value.contains(date);
        }
        return false;
    }

    /**
     * 是否为调休
     *
     * @param date
     * @return
     */
    public boolean isMakeUp(String date) {
        LocalDate localDate = LocalDate.parse(date);
        String str = getConfig(String.valueOf(localDate.getYear()));

        if (StringUtils.isNotBlank(str)) {
            return str.split(",")[1].contains(date);
        }
        return false;
    }

    public void init() {
        List<Holiday> list = holidayMapper.selectList(null);
        for (Holiday holiday : list) {
            String s = trans(holiday.getConfig());
            holidayMap.put(holiday.getYear(), holiday.getConfig());
        }
    }

    private String getConfig(String year) {
        if (holidayMap.containsKey(year)) {
            return holidayMap.get(year);
        } else {
            String config = null;
            Holiday holiday = holidayMapper.selectById(year);
            if (holiday == null) {
                config = getConfigFromApi(year);
                if (StringUtils.isNotBlank(config)) {
                    holiday = new Holiday();
                    holiday.setYear(year);
                    holiday.setConfig(config);
                    holidayMapper.insert(holiday);
                }
            }else{
                config = holiday.getConfig();
            }
            if (StringUtils.isNotBlank(config)) {
                String s = trans(config);
                holidayMap.put(year, s);
                return s;
            }
        }
        return null;
    }

    private String getConfigFromApi(String year) {
        try {
            String body = HttpUtil.get(url + year);
            log.info("节假日返回：",body);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject.containsKey("code") && jsonObject.getInteger("code").intValue() == 200) {
                return jsonObject.getString("newslist");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private String trans(String config) {
        //节假日
        Set<String> vacationSet = new HashSet<>();
        //周末调休上班
        Set<String> remarkSet = new HashSet<>();

        JSONArray jsonArray = JSONObject.parseArray(config);
        for (Object o : jsonArray) {
            JSONObject object = (JSONObject) o;
            String vacation = object.getString("vacation");
            String remark = object.getString("remark");

            if (StringUtils.isNotBlank(vacation)) {
                vacationSet.add(vacation);
            }

            if (StringUtils.isNotBlank(remark)) {
                remarkSet.add(remark);
            }
        }
        return String.join("|", vacationSet) + "," + String.join("|", remarkSet);
    }
}
