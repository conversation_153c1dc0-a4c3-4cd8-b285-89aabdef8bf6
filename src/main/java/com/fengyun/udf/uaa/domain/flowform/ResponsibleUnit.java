package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 承担单位情况
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_responsible_unit")
@Api(hidden = true)
public class ResponsibleUnit extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "承担单位名称")
    @TableField("responsible_name")
    private String responsibleName;

    @ApiModelProperty(value = "承担单位性质(RESPONSIBLE_UNIT_NATURE)")
    @TableField("unit_nature")
    private String unitNature;

    @ApiModelProperty(value = "承担单位概述")
    @TableField("responsible_overview")
    private String responsibleOverview;

    @ApiModelProperty(value = "参与单位名称")
    @TableField("participate_name")
    private String participateName;

    @ApiModelProperty(value = "项目列入计划情况")
    @TableField("plan_situation")
    private String planSituation;

    @ApiModelProperty(value = "关联承担单位情况表id")
    @TableField("responsible_info_id")
    private String responsibleInfoId;

}
