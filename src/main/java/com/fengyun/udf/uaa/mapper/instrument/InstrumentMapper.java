package com.fengyun.udf.uaa.mapper.instrument;

import com.fengyun.udf.uaa.domain.instrument.Instrument;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentListDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentShowListDTO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 仪器 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface InstrumentMapper extends BaseMapper<Instrument> {

    List<InstrumentListDTO> selectPage(Page page, @Param("condition")InstrumentQueryDTO dto);

    List<InstrumentShowListDTO> selectShowPage(Page page, @Param("condition")InstrumentQueryDTO dto);

}
