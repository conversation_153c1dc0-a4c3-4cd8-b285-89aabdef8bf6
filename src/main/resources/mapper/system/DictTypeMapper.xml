<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.system.DictTypeMapper">
    <select id="searchDictType" resultType="com.fengyun.udf.uaa.dto.response.system.DictTypeDTO">
        SELECT ID AS id, NAME AS name, CODE AS code, DESCRIPTION AS description, STATUS AS status, UPDATED_DATE AS updatedDate
        FROM t_code_type
        WHERE 1 = 1
        <if test="param.name != null and param.name.length()>0">
            AND NAME like "%"#{param.name}"%"
        </if>
        <if test="param.code != null and param.code.length()>0">
            AND CODE like "%"#{param.code}"%"
        </if>
        <if test="param.status != null and param.status.length()>0">
            AND STATUS = #{param.status}
        </if>
        order by CREATED_DATE desc
    </select>
</mapper>