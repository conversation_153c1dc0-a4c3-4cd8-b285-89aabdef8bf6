package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Created by Administrator on 2021/3/11.
 */
@Data
public class DeclareProjectShowDTO extends AbstractDTO {

    private String id;

    @ApiModelProperty("项目名称")
    private String declareName;

    @ApiModelProperty(value = "申报开始时间")
    private String declareBeginTime;

    @ApiModelProperty(value = "申报结束时间")
    private String declareEndTime;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty(value = "申报状态")
    @DictData(target = "declareStatusValue", codeType = "DECLARE_STATUS")
    private String declareStatus;
    private String declareStatusValue;

    @ApiModelProperty(hidden = true)
    private String submitStatus;

    @ApiModelProperty(hidden = true)
    private String publicFlag;

    @ApiModelProperty(value = "项目状态")
    @DictData(target = "projectStatusValue", codeType = "NEW_PROJECT_FLOW")
    private String projectStatus;
    private String projectStatusValue;

    @ApiModelProperty(value = "发布公示按钮标识，1-展示按钮")
    private String publishButtonFlag;

    @ApiModelProperty(value = "附件")
    private List<AttachmentDTO> attachments;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> reviewTemplate;

    private String reviewTemplates;

    @ApiModelProperty(value = "状态，1-上架，2-下架")
    private String status;

    @ApiModelProperty(value = "是否置顶，Y-是，N-否")
    private String top;

    @ApiModelProperty("是否可申报，true-未申报，false-已申报")
    private boolean declareFlag;

    @ApiModelProperty("是否可继续填写")
    private boolean declareContinue;

    @ApiModelProperty(value = "评分模板主键")
    private String expertScoreTemplateId;

    @ApiModelProperty(value = "评分模板名称")
    private String expertScoreTemplateName;

    @ApiModelProperty(value="创建日期")
    private ZonedDateTime createdDate;
    @ApiModelProperty(value="创建日期")
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createdDateStr;

    @ApiModelProperty(value = "项目编号前缀")
    private String prefix;

    @ApiModelProperty(value = "第二次评分模板主键")
    private String secondExpertScoreTemplateId;

    @ApiModelProperty(value = "第二次评分模板名称")
    private String secondExpertScoreTemplateName;

    @ApiModelProperty(value = "申报企业数量")
    private Long declareEntNums;

    @ApiModelProperty(value = "是否下发年度总结")
    private Boolean yearlySummary;

}
