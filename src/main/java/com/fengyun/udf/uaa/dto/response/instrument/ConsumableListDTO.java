package com.fengyun.udf.uaa.dto.response.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
public class ConsumableListDTO extends AbstractDTO{

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("耗材名称")
    private String name;

    @ApiModelProperty("耗材数量(个）")
    private Integer num;

    @ApiModelProperty("耗材剩余数量（个）")
    private Integer numResidue;

    @ApiModelProperty("单价")
    private Double unitPrice;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("状态")
    @DictData(target = "statusStr",codeType = "CONSUMABLE_STATUS")
    private String status;
    private String statusStr;

    
}