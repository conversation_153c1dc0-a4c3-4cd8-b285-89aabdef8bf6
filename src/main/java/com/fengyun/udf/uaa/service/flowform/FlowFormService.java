package com.fengyun.udf.uaa.service.flowform;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.declareproject.DeclareEnt;
import com.fengyun.udf.uaa.domain.declareproject.DeclareProject;
import com.fengyun.udf.uaa.domain.flowform.*;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.flowform.*;
import com.fengyun.udf.uaa.dto.request.flowform.json.MainWorkEducationDto;
import com.fengyun.udf.uaa.dto.request.flowform.json.PatentSituationDto;
import com.fengyun.udf.uaa.dto.request.flowform.json.PlanSituationDto;
import com.fengyun.udf.uaa.dto.request.flowform.json.ProjectParticipantsDto;
import com.fengyun.udf.uaa.dto.response.flowform.*;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareEntMapper;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareProjectMapper;
import com.fengyun.udf.uaa.mapper.flowform.*;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.util.ProjectNoUtils;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

@Service
@Transactional
public class FlowFormService extends BaseService {

    @Autowired
    private ConditionMapper conditionMapper;

    @Autowired
    private PersonnelSituationMapper situationMapper;

    @Autowired
    private ResponsibleUnitMapper unitMapper;

    @Autowired
    private ResponsibleUnitInfoMapper unitInfoMapper;

    @Autowired
    private ProjectScheduleMapper scheduleMapper;

    @Autowired
    private SourceFundsMapper fundsMapper;

    @Autowired
    private SpendingBudgetMapper budgetMapper;

    @Autowired
    private AttachmentFormMapper attachmentMapper;

    @Autowired
    private DeclareEntMapper entMapper;

    @Autowired
    private DeclareProjectMapper declareProjectMapper;
    /**
     * 项目情况介绍
     * @param dto
     */
    public void saveCondition(CreateConditionDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreated = false;
        Condition condition = conditionMapper.selectById(dto.getId());
        if (condition == null) {
            condition = new Condition();
            isCreated = true;
        }

        if (isCreated) {
            checkIsCreated(dto.getProjectId(), userId, Condition.class);
            BeanUtils.copyProperties(dto, condition);
            condition.setPatentSituation(toJSONString(dto.getPatentSituation()));
            condition.setAttachments(toJSONString(dto.getAttachments()));

            condition.setId(UUIDGenerator.getUUID());
            condition.create(userId);
            conditionMapper.insert(condition);
            processEnd(dto.getProjectId(), userId);
        } else {
            BeanUtils.copyProperties(dto, condition);
            condition.setPatentSituation(toJSONString(dto.getPatentSituation()));
            condition.setAttachments(toJSONString(dto.getAttachments()));

            condition.update(userId);
            conditionMapper.updateById(condition);

        }
        entMapper.update(null, new LambdaUpdateWrapper<DeclareEnt>()
                .set(DeclareEnt::getDeclareType, dto.getDeclareType())
                .eq(DeclareEnt::getDeclareId,dto.getProjectId())
                .eq(BaseDomain::getCreatedId, userId));
    }

    public ConditionDetailDto conditionDetail(String projectId, String userId) {
        Condition condition = conditionMapper.selectOne(new LambdaQueryWrapper<Condition>()
                .eq(Condition::getProjectId, projectId)
                .eq(BaseDomain::getCreatedId, userId));
        if (condition == null) return null;
        ConditionDetailDto result = new ConditionDetailDto();
        BeanUtils.copyProperties(condition, result);
        if (StringUtils.isNotBlank(condition.getPatentSituation())) {
            List<PatentSituationDto> patentSituationList = parseArray(condition.getPatentSituation(), PatentSituationDto.class);
            if (Constants.ZJPS_ROLE.equals(getCurrentRole())) {
                for (PatentSituationDto dto : patentSituationList) {
                    dto.setPatentHolder(Constants.DESENSITIZATION);
                }
            }
            result.setPatentSituation(patentSituationList);
        }
        result.setAttachments(parseArray(condition.getAttachments(), AttachmentDTO.class));
        DeclareEnt declareEnt = entMapper.selectOne(new LambdaQueryWrapper<DeclareEnt>()
                .eq(DeclareEnt::getDeclareId, condition.getProjectId())
                .eq(BaseDomain::getCreatedId, userId));
        result.setDeclareType(declareEnt.getDeclareType());
        result.setSubmitStatus(declareEnt.getSubmitStatus());
        return  result;
    }

    /**
     * 项目人员情况
     * @param dto
     */
    public void saveSituation(CreatedSituationDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreate = false;
        PersonnelSituation situation = situationMapper.selectById(dto.getId());
        if (situation == null) {
            situation = new PersonnelSituation();
            isCreate = true;
        }

        if (isCreate) {
            checkIsCreated(dto.getProjectId(), userId, PersonnelSituation.class);
            BeanUtils.copyProperties(dto, situation);
            situation.setMainWorkEducation(toJSONString(dto.getMainWorkEducation()));
            situation.setProjectParticipants(toJSONString(dto.getProjectParticipants()));

            situation.setId(UUIDGenerator.getUUID());
            situation.create(userId);
            situationMapper.insert(situation);
            processEnd(dto.getProjectId(), userId);
        } else {
            BeanUtils.copyProperties(dto, situation);
            situation.setMainWorkEducation(toJSONString(dto.getMainWorkEducation()));
            situation.setProjectParticipants(toJSONString(dto.getProjectParticipants()));

            situation.update(userId);
            situationMapper.updateById(situation);
        }
    }

    public SituationDetailDto situationDetail(String projectId, String userId) {
        PersonnelSituation situation = situationMapper.selectOne(new LambdaQueryWrapper<PersonnelSituation>()
                .eq(PersonnelSituation::getProjectId, projectId)
                .eq(BaseDomain::getCreatedId, userId));
        if (situation == null) return null;
        SituationDetailDto result = new SituationDetailDto();
        BeanUtils.copyProperties(situation, result);
        if (situation.getBirthDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            result.setBirthDate(sdf.format(situation.getBirthDate()));
        }
        if (Constants.ZJPS_ROLE.equals(getCurrentRole())) {
            if (StringUtils.isNotBlank(result.getPrincipalName())) {
                result.setPrincipalName(Constants.DESENSITIZATION);
            }
            if (StringUtils.isNotBlank(result.getGender())) {
                result.setGender(Constants.DESENSITIZATION);
            }
            if (result.getBirthDate() != null) {
                result.setBirthDate(Constants.DESENSITIZATION);
            }
            if (StringUtils.isNotBlank(result.getAddressPostCode())) {
                result.setAddressPostCode(Constants.DESENSITIZATION);
            }
            if (StringUtils.isNotBlank(result.getTel())) {
                result.setTel(Constants.DESENSITIZATION);
            }
            if (StringUtils.isNotBlank(result.getEmail())) {
                result.setEmail(Constants.DESENSITIZATION);
            }
        }
        result.setMainWorkEducation(parseArray(situation.getMainWorkEducation(), MainWorkEducationDto.class));
        if (StringUtils.isNotBlank(situation.getProjectParticipants())) {
            List<ProjectParticipantsDto> projectParticipantsDtos = parseArray(situation.getProjectParticipants(), ProjectParticipantsDto.class);
            if (Constants.ZJPS_ROLE.equals(getCurrentRole())) {
                for (ProjectParticipantsDto dto : projectParticipantsDtos) {
                    dto.setName(Constants.DESENSITIZATION);
                    dto.setGender(Constants.DESENSITIZATION);
                    dto.setBirthDate(Constants.DESENSITIZATION);
                }
            }
            result.setProjectParticipants(projectParticipantsDtos);
        }
        return result;
    }

    /**
     * 承担单位情况
     * @param dto
     */
    public void saveResponsible(CreateResponsibleDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreate = false;
        ResponsibleUnit unit = unitMapper.selectById(dto.getId());
        if (unit == null) {
            unit = new ResponsibleUnit();
            isCreate = true;
        }

        if (isCreate) {
            checkIsCreated(dto.getProjectId(), userId, ResponsibleUnit.class);
            ResponsibleUnitInfo unitInfo = new ResponsibleUnitInfo();
            BeanUtils.copyProperties(dto, unitInfo);
            unitInfo.setShareStructure(toJSONString(dto.getShareStructure()));

            unitInfo.setId(UUIDGenerator.getUUID());
            unitInfo.create(userId);
            unitInfoMapper.insert(unitInfo);

            BeanUtils.copyProperties(dto, unit);
            unit.setPlanSituation(toJSONString(dto.getPlanSituation()));
            unit.setResponsibleInfoId(unitInfo.getId());

            unit.setId(UUIDGenerator.getUUID());
            unit.create(userId);
            unitMapper.insert(unit);
            processEnd(dto.getProjectId(), userId);
        } else {
            BeanUtils.copyProperties(dto, unit);
            unit.setPlanSituation(toJSONString(dto.getPlanSituation()));

            unit.update(userId);
            unitMapper.updateById(unit);

            ResponsibleUnitInfo unitInfo = unitInfoMapper.selectById(unit.getResponsibleInfoId());
            BeanUtils.copyProperties(dto, unitInfo, "id");
            unitInfo.setShareStructure(toJSONString(dto.getShareStructure()));
            unitInfo.update(userId);
            unitInfoMapper.updateById(unitInfo);
        }
    }

    public ResponsibleDetailDto responsibleDetail(String projectId, String userId){
        ResponsibleDetailDto result = new ResponsibleDetailDto();
        ResponsibleUnit unit = unitMapper.selectOne(new LambdaQueryWrapper<ResponsibleUnit>()
                .eq(ResponsibleUnit::getProjectId, projectId)
                .eq(BaseDomain::getCreatedId, userId));
        if (unit == null) return null;
        BeanUtils.copyProperties(unit, result);
        result.setPlanSituation(parseArray(unit.getPlanSituation(), PlanSituationDto.class));

        ResponsibleUnitInfo unitInfo = unitInfoMapper.selectById(unit.getResponsibleInfoId());
        BeanUtils.copyProperties(unitInfo, result, "id");
        result.setShareStructure(parseObject(unitInfo.getShareStructure(), LinkedHashMap.class));
        return result;
    }

    /**
     *项目计划
     * @param dto
     */
    public void saveSchedule(CreateScheduleDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreate = false;
        ProjectSchedule entry = scheduleMapper.selectById(dto.getId());
        if (entry == null) {
            entry = new ProjectSchedule();
            isCreate = true;
        }

        if (isCreate) {
            checkIsCreated(dto.getProjectId(), userId, ProjectSchedule.class);
            BeanUtils.copyProperties(dto, entry);
            entry.setId(UUIDGenerator.getUUID());
            entry.create(userId);

            scheduleMapper.insert(entry);
            processEnd(dto.getProjectId(), userId);
        } else {
            BeanUtils.copyProperties(dto, entry);
            entry.update(userId);

            scheduleMapper.updateById(entry);
        }
    }

    public ScheduleDetailDto scheduleDetail(String projectId, String userId){
        ProjectSchedule entry = scheduleMapper.selectOne(new LambdaQueryWrapper<ProjectSchedule>()
                .eq(ProjectSchedule::getProjectId, projectId)
                .eq(BaseDomain::getCreatedId, userId));
        if (entry == null) return null;
        ScheduleDetailDto result = new ScheduleDetailDto();
        BeanUtils.copyProperties(entry, result);
        return result;
    }

    /**
     * 资金
     * @param dto
     */
    public void saveFund(CreatedFundsDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreate = false;
        SourceFunds funds = fundsMapper.selectById(dto.getId());
        if (funds == null) {
            funds = new SourceFunds();
            isCreate = true;
        }

        if (isCreate) {
            checkIsCreated(dto.getProjectId(), userId, SourceFunds.class);
            SpendingBudget budget = new SpendingBudget();
            BeanUtils.copyProperties(dto, budget);
            budget.setId(UUIDGenerator.getUUID());
            budget.create(userId);

            budgetMapper.insert(budget);

            BeanUtils.copyProperties(dto, funds);
            funds.setId(UUIDGenerator.getUUID());
            funds.setBudgetId(budget.getId());
            funds.create(userId);

            fundsMapper.insert(funds);
            processEnd(dto.getProjectId(), userId);
        } else {
            BeanUtils.copyProperties(dto, funds);
            funds.update(userId);
            fundsMapper.updateById(funds);

            SpendingBudget budget = budgetMapper.selectById(funds.getBudgetId());
            BeanUtils.copyProperties(dto, budget, "id");
            budget.update(userId);
            budgetMapper.updateById(budget);
        }
    }

    public FundDetailDto fundDetail(String projectId, String userId){
        FundDetailDto result = new FundDetailDto();
        SourceFunds funds = fundsMapper.selectOne(new LambdaQueryWrapper<SourceFunds>()
                .eq(SourceFunds::getProjectId, projectId)
                .eq(BaseDomain::getCreatedId, userId));
        if (funds == null) return null;
        BeanUtils.copyProperties(funds, result);

        SpendingBudget budget = budgetMapper.selectById(funds.getBudgetId());
        BeanUtils.copyProperties(budget, result, "id");
        return result;
    }

    /**
     * 附件
     * @param dto
     */
    public void saveAttachment(CreatedAttachmentDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreate = false;
        AttachmentForm entry = attachmentMapper.selectById(dto.getId());
        if (entry == null) {
            entry = new AttachmentForm();
            isCreate = true;
        }

        if (isCreate) {
            checkIsCreated(dto.getProjectId(), userId, AttachmentForm.class);
            BeanUtils.copyProperties(dto, entry);
            attachmentToJson(dto, entry);
            entry.setId(UUIDGenerator.getUUID());

            entry.create(userId);
            attachmentMapper.insert(entry);
            processEnd(dto.getProjectId(), userId);
        } else {
            BeanUtils.copyProperties(dto, entry);
            attachmentToJson(dto, entry);
            entry.update(userId);
            attachmentMapper.updateById(entry);
        }

    }

    public AttachmentDetailDto attachmentDetail(String projectId, String userId){
        AttachmentForm entry = attachmentMapper.selectOne(new LambdaQueryWrapper<AttachmentForm>()
                .eq(AttachmentForm::getProjectId, projectId)
                .eq(BaseDomain::getCreatedId, userId));
        if (entry == null) return null;
        AttachmentDetailDto result = new AttachmentDetailDto();
        BeanUtils.copyProperties(entry, result);
        jsonParseAttachment(entry, result);
        return result;
    }

    private void jsonParseAttachment(AttachmentForm entry, AttachmentDetailDto result) {
        result.setCommitment(parseArray(entry.getCommitment(), AttachmentDTO.class));
        result.setRecommendation(parseArray(entry.getRecommendation(), AttachmentDTO.class));
        result.setLicense(parseArray(entry.getLicense(), AttachmentDTO.class));
        result.setAuditReport(parseArray(entry.getAuditReport(), AttachmentDTO.class));
        result.setIntellectualProperty(parseArray(entry.getIntellectualProperty(), AttachmentDTO.class));
        result.setSupportSituation(parseArray(entry.getSupportSituation(), AttachmentDTO.class));
        result.setOther(parseArray(entry.getOther(), AttachmentDTO.class));
    }

    private void attachmentToJson(CreatedAttachmentDto dto, AttachmentForm entry) {
        entry.setCommitment(toJSONString(dto.getCommitment()));
        entry.setRecommendation(toJSONString(dto.getRecommendation()));
        entry.setLicense(toJSONString(dto.getLicense()));
        entry.setAuditReport(toJSONString(dto.getAuditReport()));
        entry.setIntellectualProperty(toJSONString(dto.getIntellectualProperty()));
        entry.setSupportSituation(toJSONString(dto.getSupportSituation()));
        entry.setOther(toJSONString(dto.getOther()));
    }

    private boolean checkDeclareEntIsCreated(String projectId, String userId) {
        return entMapper.checkDeclareEntIsCreated(projectId, userId);
    }
    private ReentrantLock lock = new ReentrantLock();
    private void createDeclareEntRecord(String projectId, String userId) {
        DeclareEnt declareEnt = new DeclareEnt();
        declareEnt.setSubmitStatus("0");
        declareEnt.setDeclareId(projectId);
        declareEnt.create(userId);
        declareEnt.setId(UUIDGenerator.getUUID());
        DeclareProject declareProject = declareProjectMapper.selectById(projectId);
//        declareEnt.setProjectNo(ProjectNoUtils.generateProjectNo(declareProject.getPrefix(),""));
        lock.lock();
        try{
            String last4 = "0001";
            String maxProjectNo =  entMapper.getMaxProjectNo(declareProject.getPrefix());
            if(!StringUtils.isEmpty(maxProjectNo)){
                last4 = maxProjectNo.substring(maxProjectNo.length()-4);
                BigDecimal bd = new BigDecimal(last4).add(new BigDecimal(1));
                last4 = bd.toString();
                int legn = last4.length();
                if(legn != 4){
                    String prefix = "";
                    int dif = 4-legn;
                    for(int i =0;i<dif;i++){
                        prefix= prefix+"0";
                    }
                    last4 = prefix+last4;
                }
            }

//           declareEnt.setProjectNo(ProjectNoUtils.generateProjectNo(declareProject.getPrefix(),""));
            declareEnt.setProjectNo(declareProject.getPrefix()+last4);
            declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.TEMP.getCode());
            entMapper.insert(declareEnt);
        }finally{
            lock.unlock();
        }

    }

    private void processEnd(String projectId, String userId) {
        if (!checkDeclareEntIsCreated(projectId, userId)) createDeclareEntRecord(projectId, userId);
    }

    private void checkIsCreated(String projectId, String userId, Class<?> clasz) {
        String tableName = clasz.getAnnotation(TableName.class).value();
        if (conditionMapper.checkIsCreated(projectId, userId, tableName)) {
            throw new ValidationException("error", "已填写该表单,请勿重复提交");
        }
    }

    /**
     * 附件
     * @param dto
     */
    public void saveDebatesPPtAndVideo(CreatedDebateDto dto) {
        DeclareEnt declareEnt = entMapper.selectById(dto.getId());
        if(declareEnt == null){
            throw new ValidationException("error","项目不存在！");
        }
        if (CollectionUtils.isNotEmpty(dto.getCloseDebatePPT())) {
            declareEnt.setCloseDebatePPT(JSON.toJSONString(dto.getCloseDebatePPT()));
        }
        if (CollectionUtils.isNotEmpty(dto.getCloseDebateVideo())) {
            declareEnt.setCloseDebateVideo(JSON.toJSONString(dto.getCloseDebateVideo()));
        }
        entMapper.updateById(declareEnt);
    }
}
