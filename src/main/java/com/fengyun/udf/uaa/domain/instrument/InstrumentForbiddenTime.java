package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仪器禁用时间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
@TableName("in_instrument_forbidden_time")
public class InstrumentForbiddenTime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 仪器id
     */
    @TableField("instrument_id")
    private String instrumentId;

    /**
     * 禁用开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 禁用结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 创建者
     */
    @TableField("created_id")
    private String createdId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private LocalDateTime createdDate;


}
