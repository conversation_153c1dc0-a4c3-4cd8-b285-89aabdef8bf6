package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 流程模型
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-19
 */
@TableName("wf_process_model")
@Data
@EqualsAndHashCode(callSuper = false)
public class ProcessModel extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    /**
    * 主键
    */
    private String id;

    /**
    * 流程id
    */
    @TableField(value = "process_id")
    private String processId;

    /**
    * 模型(json格式)
    */
    private String model;

    /**
    * 流程图(json格式)
    */
    @TableField(value = "model_flow")
    private String modelFlow;

    /**
    * 状态
    */
    private String status;


    private Boolean deleted;


}
