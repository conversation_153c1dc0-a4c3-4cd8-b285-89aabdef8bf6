package com.fengyun.udf.uaa.dto.response.inspection.middle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntMiddleFundingDetailDTO", description = "DeclareEntMiddleFundingDetailDTO")
public class DeclareEntMiddleFundingDetailDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 单位自有投资
     */
    @ApiModelProperty("单位自有投资")
    private BigDecimal owned;

    /**
     * 单位自有投资 已到位
     */
    @ApiModelProperty("单位自有投资 已到位")
    private BigDecimal ownedInPlace;

    /**
     * 银行贷款
     */
    @ApiModelProperty("银行贷款")
    private BigDecimal loan;

    /**
     * 银行贷款 已到位
     */
    @ApiModelProperty("银行贷款 已到位")
    private BigDecimal loanInPlace;

    /**
     * 风险投资
     */
    @ApiModelProperty("风险投资")
    private BigDecimal vc;

    /**
     * 风险投资 已到位
     */
    @ApiModelProperty("风险投资 已到位")
    private BigDecimal vcInPlace;

    /**
     * 资助资金
     */
    @ApiModelProperty("资助资金")
    private BigDecimal funding;

    /**
     * 资助资金 已到位
     */
    @ApiModelProperty("资助资金 已到位")
    private BigDecimal fundingInPlace;

    /**
     * 其他资金
     */
    @ApiModelProperty("其他资金")
    private BigDecimal other;

    /**
     * 其他资金 已到位
     */
    @ApiModelProperty("其他资金 已到位")
    private BigDecimal otherInPlace;

}
