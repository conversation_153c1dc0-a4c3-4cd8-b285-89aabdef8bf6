package com.fengyun.udf.uaa.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.cache.redis.client.DictCacheClient;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Tables;
import com.fengyun.udf.uaa.domain.system.DictType;
import com.fengyun.udf.uaa.domain.system.DictValue;
import com.fengyun.udf.uaa.dto.request.system.*;
import com.fengyun.udf.uaa.dto.response.system.DictTypeDTO;
import com.fengyun.udf.uaa.dto.response.system.DictValueDTO;
import com.fengyun.udf.uaa.mapper.system.DictTypeMapper;
import com.fengyun.udf.uaa.mapper.system.DictValueMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fengyun.udf.uaa.constant.Constants.STATUS_ACTIVE;

@Service
@Transactional
public class DictService extends BaseService {

    @Autowired
    private DictTypeMapper typeMapper;

    @Autowired
    private DictValueMapper valueMapper;

    @Autowired
    private DictCacheClient dictCacheClient;

    public List<DictValue> getByCode(String code) {
        return valueMapper.selectList(new QueryWrapper<DictValue>().eq("TYPE_CODE", code).orderByAsc("SEQ"));
    }

    /**
     * 新建时验证字典类型是否存在
     *
     * @param code 字典类型码
     * @return boolean
     */
    public boolean dictTypeAlreadyExisting(String code) {
        long count = typeMapper.selectCount(new QueryWrapper<DictType>().eq(Tables.T_CODE_TYPE.CODE.name(), code));
        return count == 1;
    }

    /**
     * 编辑时验证字典类型是否存在
     *
     * @param id 字典类型码
     * @return boolean
     */
    public boolean dictTypeIsExisting(String id) {
        long count = typeMapper.selectCount(new QueryWrapper<DictType>().eq(Tables.T_CODE_TYPE.ID.name(), id));
        return count == 1;
    }

    /**
     * 新建时验证字典类型是否存在
     *
     * @param typeCode 类型码  value 字典值
     * @return boolean
     */
    public boolean dictValueAlreadyExisting(String typeCode, String value) {
        long count = valueMapper.selectCount(new QueryWrapper<DictValue>().eq(Tables.T_CODE_VALUE.TYPE_CODE.name(), typeCode)
                .eq(Tables.T_CODE_VALUE.VALUE.name(), value));
        return count == 1;
    }

    /**
     * 编辑时验证字典类型是否存在
     *
     * @param id 类型码  value 字典值
     * @return boolean
     */
    public boolean dictValueIsExisting(String id) {
        long count = valueMapper.selectCount(new QueryWrapper<DictValue>().eq(Tables.T_CODE_VALUE.ID.name(), id));
        return count == 1;
    }

    /**
     * 新增字典类型
     */
    public void addDictType(CreateDictTypeDTO createDictTypeDTO) {
        DictType dictType = new DictType();
        dictType.setId(UUIDGenerator.getUUID());
        dictType.setName(createDictTypeDTO.getName());
        dictType.setCode(createDictTypeDTO.getCode());
        dictType.setStatus(createDictTypeDTO.getStatus());
        dictType.setDescription(createDictTypeDTO.getDescription());
        dictType.create(SecurityUtils.getUserId());
        typeMapper.insert(dictType);

    }

    /**
     * 更新字典类型
     */
    public void updateDictType(UpdateDictTypeDTO updateDictTypeDTO) {
        DictType dictType = typeMapper.selectById(updateDictTypeDTO.getId());
        dictType.setName(updateDictTypeDTO.getName());
        dictType.setStatus(updateDictTypeDTO.getStatus());
        dictType.setDescription(updateDictTypeDTO.getDescription());
        dictType.update(SecurityUtils.getUserId());
        typeMapper.updateById(dictType);
    }

    /**
     * 新增字典值
     */
    public void addDictValue(CreateDictValueDTO createDictValueDTO) {
        DictValue dictValue = new DictValue();
        String typeCode = createDictValueDTO.getTypeCode();
        String stringValue = createDictValueDTO.getValue();
        String label = createDictValueDTO.getLabel();
        List<Integer> seq = valueMapper.selectSeqByCode(typeCode);
        dictValue.setId(UUIDGenerator.getUUID());
        dictValue.setTypeCode(createDictValueDTO.getTypeCode());
        dictValue.setValue(stringValue);
        dictValue.setLabel(label);
        dictValue.setIsVis(1);
        dictValue.setDescription(createDictValueDTO.getDescription());
        if (seq != null && seq.size() > 0) {
            dictValue.setDictSort(seq.get(0) + 1);
        } else {
            dictValue.setDictSort(0);
        }
        dictValue.setStatus(STATUS_ACTIVE);
        dictValue.create(SecurityUtils.getUserId());
        valueMapper.insert(dictValue);
        dictCacheClient.addDict(typeCode, stringValue, label);
    }

    /**
     * 更新字典值
     */
    public void updateDictValue(UpdateDictValueDTO updateDictValueDTO) {
        DictValue dictValue = valueMapper.selectById(updateDictValueDTO.getId());
        String typeCode = dictValue.getTypeCode();
        String stringValue = updateDictValueDTO.getValue();
        String label = updateDictValueDTO.getLabel();
        dictValue.setLabel(label);
        dictValue.setDescription(updateDictValueDTO.getDescription());
        dictValue.setValue(stringValue);
        dictValue.update(SecurityUtils.getUserId());
        valueMapper.updateById(dictValue);
        dictCacheClient.addDict(typeCode, stringValue, label);
    }

    /**
     * 按条件查询字典类型
     */
    public Page<DictTypeDTO> searchDictType(SearchDictTypeDTO searchDictTypeDTO, Page<DictTypeDTO> page) {
        List<DictTypeDTO> result = typeMapper.searchDictType(page, searchDictTypeDTO);
        page.setRecords(result);
        return page;
    }

    /**
     * 按TypeCode查询字典纸
     */
    public List<DictValueDTO> searchDictValue(String typeCode) {
        return valueMapper.selectByTypeCode(typeCode);
    }

    public Map searchMultipleDictValue(String typeCode) {
        List<String> types = splitParam(typeCode);
        if (types == null || types.size() == 0) {
            return new HashMap();
        }

        List<DictValueDTO> result = valueMapper.selectByMultipleTypeCode(types);
        Map<String, List<DictValueDTO>> map = new HashMap();
        if (result != null && result.size() > 0) {
            for (DictValueDTO d : result) {
                String code = d.getTypeCode();
                List<DictValueDTO> ll = map.get(code);
                if (ll == null) {
                    ll = new ArrayList<>();
                    map.put(code, ll);
                }
                ll.add(d);
            }
        }

        return map;
    }

    /**
     * 按ID逻辑删除字典类型
     */
    public void deleteDictType(String id) {
        DictType dictType = typeMapper.selectById(id);
        List<DictValueDTO> valueDTOS = valueMapper.selectByTypeCode(dictType.getCode());
        for (DictValueDTO valueDTO : valueDTOS) {
            deleteDictValue(valueDTO.getId());
            dictCacheClient.deleteDict(valueDTO.getTypeCode(), valueDTO.getValue());
        }
        typeMapper.deleteById(id);
    }

    /**
     * 按ID逻辑删除字典值
     */
    public void deleteDictValue(String id) {
        DictValue dictValue = valueMapper.selectById(id);
        String typeCode = dictValue.getTypeCode();
        String stringValue = dictValue.getValue();
        dictCacheClient.deleteDict(typeCode, stringValue);

        valueMapper.deleteById(id);
    }

    /**
     * 刷新字典缓存
     */
    public void refreshDictCache() {
        dictCacheClient.deleteAll();
        List<DictValue> dictValues = valueMapper.selectList(new QueryWrapper<DictValue>().eq(Tables.T_CODE_VALUE.STATUS.name(), STATUS_ACTIVE));
        for (DictValue dictValue : dictValues) {
            dictCacheClient.addDict(dictValue.getTypeCode(), dictValue.getValue(), dictValue.getLabel());
        }
    }
}
