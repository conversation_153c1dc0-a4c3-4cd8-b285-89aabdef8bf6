package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

/**
 * Created by wangg on 2019/7/5.
 */
@Data
@TableName("t_user")
public class User extends BaseDomain {

    @TableId("ID")
    private String id;

    @TableField("LOGIN_NAME")
    private String loginName;

    @TableField("TEL")
    private String tel;

    @TableField("NICK_NAME")
    private String nickName;

    @TableField("EMAIL")
    private String email;

    @TableField("PASSWORD")
    private String password;

    @TableField("STATUS")
    private String status;

    @TableField("IS_SYS")
    private String isSys;

    @TableField("TYPE")
    private String type;

    @TableField("IMAGE")
    private String image;

    @TableField("channel")
    private String channel;//注册渠道（SIGN_UP_CHANNEL）

    @TableField("subscribe")
    private Boolean subscribe;//是否订阅

    @TableField("subscribe_method")
    private String subscribeMethod;//订阅方式

    @TableField("inviter")
    private String inviter;//邀请人

    @TableField("policy_system")
    private String policySystem;//政策体系

    @TableField("attachment_license")
    private String attachmentLicense;

    @TableField("user_type")
    private Integer userType; // 1.企业 2.个人

    @TableField("company_name")
    private String companyName;

    @TableField("audit_status")
    private String auditStatus;

    @TableField("audit_desc")
    private String auditDesc;

    //框架协议
    @TableField("agreement_file")
    private String agreementFile;

    @TableField("order_state")
    private Boolean orderState = true;

}
