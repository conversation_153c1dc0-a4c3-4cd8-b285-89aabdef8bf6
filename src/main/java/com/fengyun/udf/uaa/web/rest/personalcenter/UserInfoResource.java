package com.fengyun.udf.uaa.web.rest.personalcenter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.request.system.ChangeAgreementFileDTO;
import com.fengyun.udf.uaa.dto.request.system.ChangePasswordDTO;
import com.fengyun.udf.uaa.dto.request.system.SearchUserInviterDTO;
import com.fengyun.udf.uaa.dto.request.user.UpdateUserInfoDTO;
import com.fengyun.udf.uaa.dto.response.system.UserInviterListDTO;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController(value = "UserInfoResource")
@RequestMapping("/api")
@Api(value = "个人中心-用户管理", tags = "个人中心-用户管理")
@Validated
public class UserInfoResource extends BaseResource {

    @Autowired
    private UserService userService;

    @PostMapping("/user/update/info/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update/info/update", methodDescription = "修改当前用户昵称", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户昵称")
    public ResponseEntity<ReturnResultDTO> updateCurrentUserInfo(@RequestBody UpdateUserInfoDTO dto) {
        userService.updateCurrentUserInfo(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/update/photo/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update/photo/update", methodDescription = "修改当前用户头像", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户头像")
    public ResponseEntity<ReturnResultDTO> updateCurrentUserPhoto(@RequestBody UpdateUserInfoDTO dto) {
        userService.updateCurrentUserPhoto(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/update/name/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update/name/update", methodDescription = "修改当前用户姓名", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户姓名")
    public ResponseEntity<ReturnResultDTO> updateCurrentUserName(@RequestBody UpdateUserInfoDTO dto) {
        userService.updateCurrentUserName(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/update/birthday/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update/birthday/update", methodDescription = "修改当前用户出生日期", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户出生日期")
    public ResponseEntity<ReturnResultDTO> updateCurrentUserBirthday(@RequestBody UpdateUserInfoDTO dto) {
        userService.updateCurrentUserBirthday(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/update/company/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update/company/update", methodDescription = "修改当前用户公司信息", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户公司信息")
    public ResponseEntity<ReturnResultDTO> updateCurrentUserCompany(@RequestBody UpdateUserInfoDTO dto) {
        userService.updateCurrentUserCompany(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/update/tel/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update/tel/update", methodDescription = "修改当前用户联系电话", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户联系电话")
    public ResponseEntity<ReturnResultDTO> updateCurrentUserContactNumber(@RequestBody UpdateUserInfoDTO dto) {
        userService.updateCurrentUserContactNumber(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/change/password/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/change/password/update", methodDescription = "修改当前用户密码", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户密码")
    public ResponseEntity<ReturnResultDTO> changePassword(@RequestBody ChangePasswordDTO dto) {
        userService.changePassword(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/change/password-new/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/change/password-new/update", methodDescription = "修改当前用户密码，不需要旧密码", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户密码，不需要旧密码")
    public ResponseEntity<ReturnResultDTO> changePasswordNew(@RequestBody ChangePasswordDTO dto) {
        userService.changePasswordNew(dto);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/change/agreement-file/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/change/agreement-file/update", methodDescription = "修改当前用户框架协议", httpMethod = "POST")
    @ApiOperation(value = "修改当前用户框架协议")
    public ResponseEntity<ReturnResultDTO> changeAgreementFile(@RequestBody ChangeAgreementFileDTO dto) {
        userService.changeAgreementFile(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

}
