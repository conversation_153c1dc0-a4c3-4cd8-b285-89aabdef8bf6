package com.fengyun.udf.uaa.web.rest.instrument;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.uaa.dto.request.instrument.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;

import com.fengyun.udf.uaa.dto.response.instrument.InstrumentListDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentDetailDTO;
import com.fengyun.udf.uaa.service.instrument.InstrumentService;
import org.springframework.web.bind.annotation.RestController;
import com.fengyun.udf.resource.BaseResource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-08-29
 */

@RestController
@RequestMapping("/api/manager/instrument")
@Api(value = "管理端-仪器", tags = "管理端-仪器")
@Validated
@Slf4j
@AllArgsConstructor
@PreAuthorize("@pms.hasPermission('management')")
public class ManagerInstrumentResource extends BaseResource {
    private final InstrumentService instrumentService;

    @PostMapping("/new")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/new", methodDescription = "添加仪器", httpMethod = "POST")
    @ApiOperation(value = "添加仪器")
    public ResponseEntity<ReturnResultDTO> create(@RequestBody @Validated InstrumentCreateDTO dto) {
        instrumentService.create(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/edit")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/edit", methodDescription = "修改仪器", httpMethod = "POST")
    @ApiOperation(value = "修改仪器")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody @Validated InstrumentUpdateDTO dto) {
        instrumentService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/view", methodDescription = "仪器详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "仪器详情")
    public ResponseEntity<ReturnResultDTO<InstrumentDetailDTO>> detail(@NotBlank(message = "id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentService.detail(id));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument", methodDescription = "仪器列表", httpMethod = "GET")
    @ApiOperation(value = "仪器列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentListDTO>>> page(Page page, InstrumentQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentService.page(page, dto));
    }

    @GetMapping("/delete")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/delete", methodDescription = "仪器删除", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "仪器删除")
    public ResponseEntity<ReturnResultDTO> delete(@NotBlank(message = "id不能为空") String id) {
        instrumentService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/forbidden-time")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/forbidden-time", methodDescription = "设置禁用时间", httpMethod = "POST")
    @ApiOperation(value = "设置禁用时间")
    public ResponseEntity<ReturnResultDTO> forbiddenTime(@RequestBody @Validated InstrumentForbiddenDTO dto) {
        instrumentService.forbiddenTime(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/forbidden-time/list")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/forbidden-time/list", methodDescription = "获取禁用时间", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "获取禁用时间")
    public ResponseEntity<ReturnResultDTO<List<InstrumentForbiddenTimeDTO>>> forbiddenTimeList(@NotBlank(message = "id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentService.forbiddenTimeList(id));
    }

    @GetMapping("/export")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument/export", methodDescription = "导出仪器", httpMethod = "GET")
    @ApiOperation(value = "导出仪器")
    public void export(InstrumentQueryDTO dto,HttpServletResponse response, HttpServletRequest request) throws IOException {
        List<InstrumentListDTO> list = instrumentService.export(dto);

        ExcelWriter writer = ExcelUtil.getWriter(true);

        writer.addHeaderAlias("index", "序号");
        writer.addHeaderAlias("name", "设备名称");
        writer.addHeaderAlias("num", "设备编号");
        writer.addHeaderAlias("model", "型号");
        writer.addHeaderAlias("factory", "厂家");
        writer.addHeaderAlias("address", "放置地点");
        writer.addHeaderAlias("statusStr", "状态");
        writer.addHeaderAlias("personNum", "每时间段人数");
        writer.addHeaderAlias("leasePriceStr", "仪器租赁（元）");
        writer.addHeaderAlias("testPriceStr", "委托测试（元）");
        writer.addHeaderAlias("remark", "备注");
        writer.addHeaderAlias("tecContent", "技术参数内容");
        writer.addHeaderAlias("featureContent", "功能特色内容");
        writer.addHeaderAlias("careContent", "注意事项内容");

        writer.setOnlyAlias(true);
        writer.write(list, true);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");

        String fileName = "租赁仪器导出表格.xlsx";

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.addHeader("filename", URLEncoder.encode(fileName, "UTF-8"));
        response.addHeader("Access-Control-Expose-Headers", "filename");

        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);

        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }


}
