package com.fengyun.udf.uaa.dto.response.inspection.end;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeclareEntEndBasicInfoListDTO", description = "DeclareEntEndBasicInfoListDTO")
public class DeclareEntEndBasicInfoListDTO extends AbstractDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 课题名称
     */
    @ApiModelProperty("课题名称")
    private String subjectName;

    /**
     * 申报类别
     */
    @ApiModelProperty("申报类别")
    @DictData(target = "declareTypeName", codeType = "DECLARE_TYPE_BAK")
    private String declareType;

    /**
     * 申报类别
     */
    @ApiModelProperty("申报类别")
    private String declareTypeName;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    private String companyName;

    /**
     * 负责人姓名
     */
    @ApiModelProperty("负责人姓名")
    private String principalName;

    /**
     * 负责人电话
     */
    @ApiModelProperty("负责人电话")
    private String principalTel;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    private String contactTel;

    /**
     * 是否企业类项目
     */
    @ApiModelProperty("是否企业类项目")
    private Boolean companyType;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @DictData(target = "statusName", codeType = "MIDDLE_INSPECTION_STATUS")
    private String status;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String statusName;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime createdDate;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime updatedDate;

    /**
     * 状态
     */
    @ApiModelProperty("项目状态")
    @DictData(target = "projectStatusName", codeType = "NEW_PROJECT_STATUS")
    private String projectStatus;

    /**
     * 状态
     */
    @ApiModelProperty("项目状态")
    private String projectStatusName;

    /**
     * 状态
     */
    @ApiModelProperty("申报ID")
    private String declareId;
}
