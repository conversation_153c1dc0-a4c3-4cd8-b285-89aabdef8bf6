package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.uaa.dto.request.inspection.middle.DeclareEntMiddleChargePrincipalSaveDTO;
import com.fengyun.udf.uaa.dto.request.inspection.middle.DeclareEntMiddleParticipantSaveDTO;
import com.fengyun.udf.uaa.dto.request.inspection.middle.DeclareEntMiddleTalentAwardsSaveDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProjectParticipantDetailDTO", description = "ProjectParticipantDetailDTO")
public class ProjectParticipantDetailDTO  extends AbstractDTO {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    @ApiModelProperty(value = "项目承担单位及负责人", required = true)
    @Valid
    private DeclareEntMiddleChargePrincipalDetailDTO chargePrincipal;

    @ApiModelProperty(value = "项目人员情况")
    private List<DeclareEntMiddleParticipantDetailDTO> participants;

    @ApiModelProperty(value = "主要参加人员")
    @Valid
    private List<DeclareEntMiddleTalentAwardsDetailDTO> talentAwards;

}
