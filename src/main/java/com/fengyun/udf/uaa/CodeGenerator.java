package com.fengyun.udf.uaa;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.engine.FykjTemplateEngine;

import java.io.IOException;
import java.util.Scanner;

/**
 * <AUTHOR>
 */
public class CodeGenerator {

    public static void main(String[] args) throws IOException {
        System.out.print("是否生成代码（Y/N）");
        Scanner sc = new Scanner(System.in);
        char c = (char)System.in.read();
        if(!"Y".equalsIgnoreCase(String.valueOf(c))){
            System.out.print("生成代码结束");
            return;
        }
        FastAutoGenerator.create("***************************************************************************************************************",
                "root", "YSZ@yfb@2018")
                .globalConfig(builder -> {
                    builder.author("wangg")
                            .outputDir(System.getProperty("user.dir") + "/src/main/java/"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.fengyun.udf.uaa")// 设置父包名
                            .moduleName("instrument")// 设置父包模块名
                            .xmlPath(System.getProperty("user.dir") + "/src/main/");
                })
                .strategyConfig(builder -> {
                    builder.addInclude("in_instrument_order_detail_confirm") // 设置需要生成的表名
                            .addTablePrefix("t_", "c_", "po_", "in_")              // 设置过滤表前缀
                            .enableService()          //是否生成 service
                            .serviceBuilder().superServiceClass("com.fengyun.udf.uaa.service.BaseService") // 设置baseservice
                            .controllerBuilder().superClass("com.fengyun.udf.resource.BaseResource")     // 设置BaseResource
                            .entityBuilder().superClass("com.fengyun.udf.domain.BaseDomain")     // BaseDomain
                    ;
                })
                .templateEngine(new FykjTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
