package com.fengyun.udf.uaa.dto.response.inspection.middle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 2025/2/13
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DeclareEntMiddleTalentAwardsDetailDTO {

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicantName;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private String year;

    /**
     * 申报单位
     */
    @ApiModelProperty("申报单位")
    private String declareUnit;

    /**
     * 人才称号
     */
    @ApiModelProperty("人才称号")
    private String talentTitle;

    /**
     * 是否获评
     */
    @ApiModelProperty("是否获评")
    private Boolean evaluated;


    private String evaluatedStr;

    public String getEvaluatedStr() {
        if (this.evaluated != null && evaluated) {
            return "是";
        } else {
            return "否";
        }
    }
}
