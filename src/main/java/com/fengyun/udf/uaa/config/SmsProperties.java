package com.fengyun.udf.uaa.config;

import com.fengyun.udf.sms.util.SmsTemplate;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Created by wangg on 2019/7/4.
 */
@Component
@ConfigurationProperties(prefix = "sms")
@Data
public class SmsProperties {
    private SmsTemplate register;
    private SmsTemplate login;
    private SmsTemplate restPassword;
    private SmsTemplate registerFail;
    private SmsTemplate registerSuccess;
    private SmsTemplate orderWork;

    private SmsTemplate orderBack;
    private SmsTemplate orderReject;
    private SmsTemplate orderPass;

    private String smsClient;
}
