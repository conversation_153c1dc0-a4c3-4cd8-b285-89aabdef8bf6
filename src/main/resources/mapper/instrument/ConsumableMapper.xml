<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.instrument.ConsumableMapper">

    <select id="selectPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.ConsumableListDTO">
        select
        id, name, num, num_residue, unit_price, status, unit
        from
        in_consumable
        where deleted =0
        <if test="condition.name != null and condition.name != '' ">
            and name like concat('%',#{condition.name},'%')
        </if>
        <if test="condition.numStart != null ">
            and num &gt;= #{condition.numStart}
        </if>
        <if test="condition.numEnd != null ">
            and num &lt;= #{condition.numEnd}
        </if>
        <if test="condition.unitPriceStart != null ">
            and unit_price &gt;= #{condition.unitPriceStart}
        </if>
        <if test="condition.unitPriceEnd != null ">
            and unit_price &lt;= #{condition.unitPriceEnd}
        </if>
        <if test="condition.numResidueStart != null ">
            and num_residue &gt;= #{condition.numResidueStart}
        </if>
        <if test="condition.numResidueEnd != null ">
            and num_residue &lt;= #{condition.numResidueEnd}
        </if>
        <if test="condition.status != null and condition.status != '' ">
            and status = #{condition.status}
        </if>
        <if test="condition.ids != null and condition.ids.size > 0">
            AND id in
            <foreach collection="condition.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by created_date desc
    </select>
</mapper>
