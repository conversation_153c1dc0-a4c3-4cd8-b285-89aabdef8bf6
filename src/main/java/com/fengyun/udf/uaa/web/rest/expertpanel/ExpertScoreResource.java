package com.fengyun.udf.uaa.web.rest.expertpanel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertScoreCreateDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreDTO;
import com.fengyun.udf.uaa.service.expertpanel.ExpertScoreService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/expert/score")
@Api(value = "专家评分", tags = "专家评分")
@Validated
@PreAuthorize("@pms.hasPermission('management')")
public class ExpertScoreResource extends BaseResource {

    @Autowired
    private ExpertScoreService expertScoreService;

    @GetMapping({"/page"})
    @ApiOperation(value = "待评分列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<DeclareEntShowDTO>> scorePage(Page page, String keyWord, String declareType) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreService.scorePage(page, keyWord, declareType));
    }

    @GetMapping({"/history/page"})
    @ApiOperation(value = "历史评分列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<DeclareEntShowDTO>> historyScorePage(Page page, String keyWord, String declareType) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreService.historyScorePage(page, keyWord, declareType));
    }

    @GetMapping({"/draft/page"})
    @ApiOperation(value = "评分草稿列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<DeclareEntShowDTO>> draftScorePage(Page page, String keyWord, String declareType) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreService.draftScorePage(page, keyWord, declareType));
    }

    @GetMapping({"/get/mark"})
    @CustomBaseException
    @ApiOperation(value = "查看打分")
    public ResponseEntity<ReturnResultDTO<ExpertScoreDTO>> getMark(String declareEntId) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreService.getMark(declareEntId));
    }

    @PostMapping({"/mark"})
    @CustomBaseException
    @ApiOperation(value = "新增打分")
    public ResponseEntity<ReturnResultDTO> mark(@RequestBody ExpertScoreCreateDTO dto) throws Exception {
        expertScoreService.mark(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/submit/mark"})
    @CustomBaseException
    @ApiOperation(value = "新增并提交打分")
    public ResponseEntity<ReturnResultDTO> submitMark(@RequestBody ExpertScoreCreateDTO dto) throws Exception {
        expertScoreService.submitMark(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/update/mark"})
    @CustomBaseException
    @ApiOperation(value = "修改打分")
    public ResponseEntity<ReturnResultDTO> updateMark(@RequestBody ExpertScoreCreateDTO dto) throws Exception {
        expertScoreService.updateMark(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/update/submit/mark"})
    @CustomBaseException
    @ApiOperation(value = "修改并提交打分")
    public ResponseEntity<ReturnResultDTO> updateSubmitMark(@RequestBody ExpertScoreCreateDTO dto) throws Exception {
        expertScoreService.updateSubmitMark(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/summary"})
    @ApiOperation(value = "打分结果汇总")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyWord", value = "keyWord", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "index", value = "index", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "申报类型 DECLARE_TYPE_BAK", value = "declareType", required = true, dataType = "String", paramType = "path"),
    })
    public ResponseEntity<ReturnResultDTO<DeclareEntExpertShowDTO>> summaryScorePage(
            Page page, String keyWord,String index, String declareType, String responsibleName) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreService.summaryScorePage(page, keyWord, index, declareType, responsibleName));
    }

    @GetMapping({"/detail"})
    @ApiOperation(value = "打分结果查询")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<ExpertScoreDTO>> scoreDetail(String declareEntId,String index) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreService.scoreDetail(declareEntId,index));
    }

    @GetMapping("/export")
    @CustomBaseException
    @ApiOperation(value = "导出打分结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyWord", value = "keyWord", dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "index", value = "index", dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "申报类型 DECLARE_TYPE_BAK", value = "declareType", dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "单位名称", value = "responsibleName", dataType = "String", paramType = "path"),
    })
    public void exportSummaryScore(HttpServletResponse response, HttpServletRequest request,
                                   String projectId,String index, String declareType, String responsibleName) throws Exception {
        expertScoreService.exportSummaryScore(response, request, projectId,index, declareType, responsibleName);
    }

    @GetMapping("/export/project")
    @CustomBaseException
    @ApiOperation(value = "导出已上传答辩ppt和视频项目")
    public void exportUploadedPPtAndVideoProject(HttpServletResponse response, HttpServletRequest request,String declareType,String isUploadedPPTVideo) throws Exception {
        expertScoreService.exportUploadedPPtAndVideoProject(response, request,declareType,isUploadedPPTVideo);
    }

}
