<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderDetailConfirmMapper">

    <select id="selectPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmListDTO">
        select
        id,
        title,
        user_id,
        user_type,
        user_name,
        amount,
        is_send, send_time, is_confirm, confirm_time
        from
        in_instrument_order_detail_confirm
        where 1=1
        <if test="condition.keyword != null and condition.keyword != ''">
            and (title like concat('%',#{condition.keyword},'%') or user_name like concat('%',#{condition.keyword},'%'))
        </if>
        <if test="condition.userId != null and condition.userId != ''">
            and user_id = #{condition.userId}
        </if>
        <if test="condition.isSend!= null">
            and is_send = #{condition.isSend}
        </if>
        <if test="condition.isConfirm!= null">
            and is_confirm = #{condition.isConfirm}
        </if>
        <if test="condition.startDate != null ">
            and send_time &gt;= #{condition.startDate}
        </if>
        <if test="condition.endDate != null ">
            and send_time &lt; #{condition.endDate}
        </if>

        order by created_date desc
    </select>
</mapper>
