package com.fengyun.udf.uaa.mapper.declareproject;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.declareproject.DeclareSubProject;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareSubProjectShowDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2021/2/2.
 */
@Repository
public interface DeclareSubProjectMapper extends BaseMapper<DeclareSubProject> {

    /**
     * 列表查询
     * @return
     */
    List<DeclareSubProjectShowDTO> page(Page page, @Param("declareName") String declareName);

    Map<String, String> selectSubProject(@Param("id") String id);

    DeclareSubProjectShowDTO selectSubProjectDetail(@Param("id") String id);
}