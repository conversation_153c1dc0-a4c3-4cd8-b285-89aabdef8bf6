package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by Administrator on 2021/3/11.
 */
@TableName("t_declare_ent_role")
@Data
@EqualsAndHashCode(callSuper = false)
public class DeclareEntRole extends BaseDomain {


    private String id;

    @TableField("declare_ent_id")
    private String declareEntId;

    @TableField("current_role")
    private String currentRole;

    @TableField("status")
    private String status;

    @TableField("deleted")
    private Boolean deleted;

}