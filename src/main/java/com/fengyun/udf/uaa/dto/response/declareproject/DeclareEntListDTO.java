package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 子项目列表DTO
 * <AUTHOR>
 * @version 1.0.0
 * 2022/09/19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DeclareEntListDTO extends AbstractDTO {

    @ApiModelProperty("子项目Id")
    private String id;

    @ApiModelProperty("父项目Id")
    private String declareId;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty("项目名称")
    private String subjectName;

    @ApiModelProperty("承担单位")
    private String responsibleName;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime createdDate;

    @ApiModelProperty(value = "项目状态")
    @DictData(target = "projectStatusStr",codeType = "NEW_PROJECT_STATUS")
    private String projectStatus;
    private String projectStatusStr;

    @ApiModelProperty(value = "申报类别")
    @DictData(target = "declareTypeStr", codeType = "DECLARE_TYPE_BAK")
    private String declareType;
    private String declareTypeStr;

    @ApiModelProperty(value = "项目进度")
    @DictData(target = "projectProgressStr", codeType = "PROJECT_PROGRESS")
    private String projectProgress;
    private String projectProgressStr;

    @ApiModelProperty(value = "申请用户Id")
    private String createdId;

}