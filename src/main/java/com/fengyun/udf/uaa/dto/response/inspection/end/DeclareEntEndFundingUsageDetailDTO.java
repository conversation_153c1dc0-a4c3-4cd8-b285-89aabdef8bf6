package com.fengyun.udf.uaa.dto.response.inspection.end;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntEndFundingUsageDetailDTO", description = "DeclareEntEndFundingUsageDetailDTO")
public class DeclareEntEndFundingUsageDetailDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 设备购置-预算
     */
    @ApiModelProperty("设备购置-预算")
    private BigDecimal equipmentPurchaseBudget;

    /**
     * 设备购置-国创中心支持
     */
    @ApiModelProperty("设备购置-国创中心支持")
    private BigDecimal equipmentPurchaseSupport;

    /**
     * 设备购置-实际支出经费
     */
    @ApiModelProperty("设备购置-实际支出经费")
    private BigDecimal equipmentPurchaseActual;

    /**
     * 设备购置-国创中心支持-实际
     */
    @ApiModelProperty("设备购置-国创中心支持-实际")
    private BigDecimal equipmentPurchaseSupportActual;

    /**
     * 设备试制-预算
     */
    @ApiModelProperty("设备试制-预算")
    private BigDecimal equipmentTrialBudget;

    /**
     * 设备试制-国创中心支持
     */
    @ApiModelProperty("设备试制-国创中心支持")
    private BigDecimal equipmentTrialSupport;

    /**
     * 设备试制-实际支出经费
     */
    @ApiModelProperty("设备试制-实际支出经费")
    private BigDecimal equipmentTrialActual;

    /**
     * 设备试制-国创中心支持-实际
     */
    @ApiModelProperty("设备试制-国创中心支持-实际")
    private BigDecimal equipmentTrialSupportActual;

    /**
     * 设备改造-预算
     */
    @ApiModelProperty("设备改造-预算")
    private BigDecimal equipmentModificationBudget;

    /**
     * 设备改造-国创中心支持
     */
    @ApiModelProperty("设备改造-国创中心支持")
    private BigDecimal equipmentModificationSupport;

    /**
     * 设备改造-实际支出经费
     */
    @ApiModelProperty("设备改造-实际支出经费")
    private BigDecimal equipmentModificationActual;

    /**
     * 设备改造-国创中心支持-实际
     */
    @ApiModelProperty("设备改造-国创中心支持-实际")
    private BigDecimal equipmentModificationSupportActual;

    /**
     * 材料费-预算
     */
    @ApiModelProperty("材料费-预算")
    private BigDecimal materialFeeBudget;

    /**
     * 材料费-国创中心支持
     */
    @ApiModelProperty("材料费-国创中心支持")
    private BigDecimal materialFeeSupport;

    /**
     * 材料费-实际支出经费
     */
    @ApiModelProperty("材料费-实际支出经费")
    private BigDecimal materialFeeActual;

    /**
     * 材料费-国创中心支持-实际
     */
    @ApiModelProperty("材料费-国创中心支持-实际")
    private BigDecimal materialFeeSupportActual;

    /**
     * 差旅费-预算
     */
    @ApiModelProperty("差旅费-预算")
    private BigDecimal travelExpensesBudget;

    /**
     * 差旅费-国创中心支持
     */
    @ApiModelProperty("差旅费-国创中心支持")
    private BigDecimal travelExpensesSupport;

    /**
     * 差旅费-实际支出经费
     */
    @ApiModelProperty("差旅费-实际支出经费")
    private BigDecimal travelExpensesActual;

    /**
     * 差旅费-国创中心支持-实际
     */
    @ApiModelProperty("差旅费-国创中心支持-实际")
    private BigDecimal travelExpensesSupportActual;

    /**
     * 劳务费-预算
     */
    @ApiModelProperty("劳务费-预算")
    private BigDecimal laborFeeBudget;

    /**
     * 劳务费-国创中心支持
     */
    @ApiModelProperty("劳务费-国创中心支持")
    private BigDecimal laborFeeSupport;

    /**
     * 劳务费-实际支出经费
     */
    @ApiModelProperty("劳务费-实际支出经费")
    private BigDecimal laborFeeActual;

    /**
     * 劳务费-国创中心支持-实际
     */
    @ApiModelProperty("劳务费-国创中心支持-实际")
    private BigDecimal laborFeeSupportActual;

    /**
     * 其他支出-预算
     */
    @ApiModelProperty("其他支出-预算")
    private BigDecimal otherBudget;

    /**
     * 其他支出-国创中心支持
     */
    @ApiModelProperty("其他支出-国创中心支持")
    private BigDecimal otherSupport;

    /**
     * 其他支出-实际支出经费
     */
    @ApiModelProperty("其他支出-实际支出经费")
    private BigDecimal otherActual;

    /**
     * 其他支出-国创中心支持-实际
     */
    @ApiModelProperty("其他支出-国创中心支持-实际")
    private BigDecimal otherSupportActual;

    /**
     * 绩效支出-预算
     */
    @ApiModelProperty("绩效支出-预算")
    private BigDecimal performancePayBudget;

    /**
     * 绩效支出-国创中心支持
     */
    @ApiModelProperty("绩效支出-国创中心支持")
    private BigDecimal performancePaySupport;

    /**
     * 绩效支出-实际支出经费
     */
    @ApiModelProperty("绩效支出-实际支出经费")
    private BigDecimal performancePayActual;

    /**
     * 绩效支出-国创中心支持-实际
     */
    @ApiModelProperty("绩效支出-国创中心支持-实际")
    private BigDecimal performancePaySupportActual;

    /**
     * 管理费-预算
     */
    @ApiModelProperty("管理费-预算")
    private BigDecimal managePayBudget;

    /**
     * 管理费-国创中心支持
     */
    @ApiModelProperty("管理费-国创中心支持")
    private BigDecimal managePaySupport;

    /**
     * 管理费-支出实际支出经费
     */
    @ApiModelProperty("管理费-支出实际支出经费")
    private BigDecimal managePayActual;

    /**
     * 管理费-国创中心支持-实际
     */
    @ApiModelProperty("管理费-国创中心支持-实际")
    private BigDecimal managePaySupportActual;

}
