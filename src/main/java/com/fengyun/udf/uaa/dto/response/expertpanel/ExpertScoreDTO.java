package com.fengyun.udf.uaa.dto.response.expertpanel;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.ZonedDateTime;

@ApiModel("评分dto")
@Data
@EqualsAndHashCode(callSuper = false)
public class ExpertScoreDTO extends AbstractDTO {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "主键")
    private String declareEntId;

    /**
     * 得分配置
     */
    @ApiModelProperty(value = "得分配置")
    private String scoreConfig;

    /**
     * 总得分
     */
    @ApiModelProperty(value = "总得分")
    private String totalScore;

    @ApiModelProperty(value = "用户")
    private String userName;

    @ApiModelProperty(value="时间")
    private ZonedDateTime createdDate;
    @ApiModelProperty(value="时间")
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createdDateStr;

}
