package com.fengyun.udf.uaa.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @Date 2021/08/19 11:26
 * @Description
 */
public class DateUtils {

    private static final Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));

    private static final SimpleDateFormat sdfToZero = new SimpleDateFormat("yyyy-MM-dd 00:00:00");

    private static final SimpleDateFormat dateTimeFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");

    public static Long getZeroTime() {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public static String formatDateTime(Long time) {
        return sdfToZero.format(time);
    }

    public static String formatZeroTime() {
        return sdfToZero.format(new Date());
    }

    public static String formatDate(Date date) {
        return dateFormatter.format(date);
    }

}
