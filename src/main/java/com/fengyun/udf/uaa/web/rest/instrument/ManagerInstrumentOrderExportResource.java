package com.fengyun.udf.uaa.web.rest.instrument;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportSendDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportUserListDTO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;

import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportUpdateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportListDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDetailDTO;
import com.fengyun.udf.uaa.service.instrument.InstrumentOrderExportService;
import org.springframework.web.bind.annotation.RestController;
import com.fengyun.udf.resource.BaseResource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2023-09-14
 */

@RestController
@RequestMapping("/api/manager/instrument-order-export")
@Api(value = "管理端-", tags = "管理端-")
@Validated
@Slf4j
@AllArgsConstructor
@PreAuthorize("@pms.hasPermission('management')")
public class ManagerInstrumentOrderExportResource extends BaseResource {
    private final InstrumentOrderExportService instrumentOrderExportService;

    @PostMapping("/new")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export/new", methodDescription = "添加", httpMethod = "POST")
    @ApiOperation(value = "添加")
    public ResponseEntity<ReturnResultDTO> create(@RequestBody @Validated InstrumentOrderExportCreateDTO dto) throws Exception {
        instrumentOrderExportService.create(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/edit")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export/edit", methodDescription = "编辑核准总金额", httpMethod = "POST")
    @ApiOperation(value = "编辑核准总金额")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody @Validated InstrumentOrderExportUpdateDTO dto) {
        instrumentOrderExportService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export/view", methodDescription = "详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderExportDetailDTO>> detail(@NotBlank(message = "id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderExportService.detail(id,true));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export", methodDescription = "列表", httpMethod = "GET")
    @ApiOperation(value = "列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderExportListDTO>>> page(Page page, InstrumentOrderExportQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderExportService.page(page, dto));
    }

    @GetMapping("/delete")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export/delete", methodDescription = "删除", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "删除")
    public ResponseEntity<ReturnResultDTO> delete(@NotBlank(message = "id不能为空") String id) {
        instrumentOrderExportService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/send")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export/send", methodDescription = "发送客户", httpMethod = "POST")
    @ApiOperation(value = "发送客户")
    public ResponseEntity<ReturnResultDTO> send(@RequestBody @Validated InstrumentOrderExportSendDTO dto) {
        instrumentOrderExportService.send(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/user-list")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-export/user-list", methodDescription = "用户列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userType", value = "用户类型", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "用户列表")
    public ResponseEntity<ReturnResultDTO<List<InstrumentOrderExportUserListDTO>>> userList(@NotNull(message = "用户类型不能为空")Integer userType) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderExportService.userList(userType));
    }


}
