package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 附件上传表单
* </p>
*
* <AUTHOR>
* @since 2022-03-18
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_attachment")
@Api(hidden = true)
public class AttachmentForm extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "承诺书")
    @TableField("commitment")
    private String commitment;

    @ApiModelProperty(value = "推荐表")
    @TableField("recommendation")
    private String recommendation;

    @ApiModelProperty(value = "执照")
    @TableField("license")
    private String license;

    @ApiModelProperty(value = "审计报告")
    @TableField("audit_report")
    private String auditReport;

    @ApiModelProperty(value = "知识产权")
    @TableField("intellectual_property")
    private String intellectualProperty;

    @ApiModelProperty(value = "支持情况")
    @TableField("support_situation")
    private String supportSituation;

    @ApiModelProperty(value = "其他附件")
    @TableField("other")
    private String other;

}
