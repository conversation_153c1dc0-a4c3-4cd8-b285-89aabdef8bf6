package com.fengyun.udf.uaa.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.system.UserAudit;
import com.fengyun.udf.uaa.dto.request.system.QueryRegisterUserDto;
import com.fengyun.udf.uaa.dto.response.system.EnRegisterListDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 企业用户审核表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Repository
public interface UserAuditMapper extends BaseMapper<UserAudit> {

        List<EnRegisterListDto> selectAuditRegisterPage(Page page, @Param("condition") QueryRegisterUserDto dto);
}
