package com.fengyun.udf.uaa.domain.inspection.middle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "declare_ent_middle_funding_remark", description = "经费使用情况备注表")
@TableName(value = "declare_ent_middle_funding_remark", autoResultMap = true)
public class DeclareEntMiddleFundingRemark extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 科目名称
     */
    @ApiModelProperty("科目名称")
    @TableField("subject_name")
    private String subjectName;

    /**
     * 明细
     */
    @ApiModelProperty("明细")
    @TableField("detail")
    private String detail;


    /**
     * 金额（万元）
     */
    @ApiModelProperty("金额（万元）")
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 收款单位
     */
    @ApiModelProperty("收款单位")
    @TableField("payee")
    private String payee;

    @ApiModelProperty("排序")
    @TableField("sort_num")
    private Integer sortNum;

}
