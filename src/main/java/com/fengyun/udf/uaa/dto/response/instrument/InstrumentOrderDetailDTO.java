package com.fengyun.udf.uaa.dto.response.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.interceptor.annotation.Nickname;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderConsumableDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
public class InstrumentOrderDetailDTO extends AbstractDTO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("仪器id")
    private String instrumentId;

    @ApiModelProperty("设备名称")
    private String instrumentName;

    @ApiModelProperty("租赁类型")
    @DictData(target = "leaseTypeStr", codeType = "INSTRUMENT_ORDER_LEASE_TYPE")
    private String leaseType;
    private String leaseTypeStr;

    @ApiModelProperty("企业名称")
    private String enName;

    @ApiModelProperty("预约联系人")
    private String contact;

    @ApiModelProperty("预约联系方式")
    private String contactTel;

    @ApiModelProperty("预约联系邮箱")
    private String contactEmail;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("是否熟练使用仪器")
    private Boolean skilledUse;

    @ApiModelProperty("是否已签框架协议")
    private Boolean signAgreement;

    @ApiModelProperty("预计使用样品数（个）")
    private String useSampleNum;

    @ApiModelProperty("样品名称")
    private String sampleName;

    @ApiModelProperty("样品包装")
    private String samplePackage;

    @ApiModelProperty("样品规格")
    private String sampleSpec;

    @ApiModelProperty("样品存放要求,INSTRUMENT_ORDER_SAMPLE_DEPOSIT")
    @DictData(target = "sampleDepositStr", codeType = "INSTRUMENT_ORDER_SAMPLE_DEPOSIT")
    private String sampleDeposit;
    private String sampleDepositStr;

    @ApiModelProperty("样品特性,INSTRUMENT_ORDER_SAMPLE_FEATURE")
    @DictData(target = "sampleFeatureStr", codeType = "INSTRUMENT_ORDER_SAMPLE_FEATURE")
    private String sampleFeature;
    private String sampleFeatureStr;

    @ApiModelProperty("检测项目")
    private String testProject;

    @ApiModelProperty("检测条件")
    private String testCondition;

    @ApiModelProperty("检测方法")
    private String testMethod;

    @ApiModelProperty("实验内容")
    private String content;

    @ApiModelProperty("证明材料")
    private String file;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    @DictData(target = "statusStr", codeType = "INSTRUMENT_ORDER_STATUS")
    private String status;
    private String statusStr;

    @ApiModelProperty("评价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime evaluateTime;

    @ApiModelProperty("评价内容")
    private String evaluateContent;

    @ApiModelProperty("评价人")
    @Nickname(target = "evaluateUserName")
    private String evaluateUser;
    private String evaluateUserName;

    @ApiModelProperty("结算状态")
    @DictData(target = "settlementStatusStr", codeType = "INSTRUMENT_ORDER_SETTLEMENT_STATUS")
    private String settlementStatus;
    private String settlementStatusStr;

    @ApiModelProperty("结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime settlementTime;

    @ApiModelProperty("结算人")
    @Nickname(target = "settlementUserName")
    private String settlementUser;
    private String settlementUserName;

    @ApiModelProperty("审核历史")
    private List<InstrumentOrderAuditRecordDTO> records;

    @ApiModelProperty("核准金额")
    private Double amount;

    @ApiModelProperty("生成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime createdDate;

    @ApiModelProperty("支付方式,INSTRUMENT_ORDER_PAY_MODE")
    @DictData(target = "payModeStr", codeType = "INSTRUMENT_ORDER_PAY_MODE")
    private String payMode;
    private String payModeStr;

    @ApiModelProperty("支付方式其他,INSTRUMENT_ORDER_PAY_MODE_OTHER")
    @DictData(target = "payModeOtherStr", codeType = "INSTRUMENT_ORDER_PAY_MODE_OTHER")
    private String payModeOther;
    private String payModeOtherStr;

    @ApiModelProperty("预约使用耗材")
    private List<InstrumentOrderConsumableDTO> orderConsumableList;

    @ApiModelProperty("设备耗材")
    private List<ConsumableListDTO> consumableList;

    @ApiModelProperty("预约时间")
    private List<InstrumentOrderDateDTO> orderDateDTOList;
    @ApiModelProperty("预约时间str")
    private String orderDateStr;

    @ApiModelProperty("发票文件")
    private String invoiceFile;

    @ApiModelProperty("发票发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendInvoiceTime;

    @ApiModelProperty("计算方式，INSTRUMENT_CALCULATE_METHOD")
    @DictData(target = "calculateMethodStr",codeType = "INSTRUMENT_CALCULATE_METHOD")
    private String calculateMethod;
    private String calculateMethodStr;

    @ApiModelProperty("预约编号")
    private String orderNum;


}