package com.fengyun.udf.uaa.dto.response.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
public class InstrumentOrderDetailConfirmDetailDTO {

    @ApiModelProperty("明细确认标题")
    private String title;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户类型")
    @DictData(target = "userTypeStr", codeType = "USER_TYPE")
    private Integer userType;
    private String userTypeStr;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("总核准金额")
    private Double amount;

    @ApiModelProperty("联系方式")
    private String contactTel;

    @ApiModelProperty("联系邮箱")
    private String contactEmail;

    @ApiModelProperty("文件")
    private String file;

    @ApiModelProperty("是否发送客户")
    private Boolean isSend;

    @ApiModelProperty("发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    @ApiModelProperty("是否确认")
    private Boolean isConfirm;

    @ApiModelProperty("确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmTime;

    
}