server:
  port: 9999

spring:
  application:
    name: uaa
  profiles:
    active: #spring.profiles.active#
  messages:
    basename: in18/validations,in18/exceptions,org/springframework/security/messages
    encoding: UTF-8





feign:
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true


# 注册中心
eureka:
  instance:
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: *********************************/eureka/

udf:
  #跨域配置
#  cors:
#    allowed-origins: "*"
#    allowed-methods: GET, PUT, POST, DELETE, OPTIONS
#    allowed-headers: "*"
#    exposed-headers:
#    allow-credentials: true
#    max-age: 1800
    cache:
      alias: swyy

sms:
  sms-client: fykj   # fy:公司默认，ali：阿里云  huyi：互亿 fykj:公司
  fykj:
    uri: https://ifs.ranxinjs.com/sms?action=send
    account: 800020
    password: teFr6e
    extno: 1069030
    signName: 【苏州市生物医药产业创新中心】
  register:
    template: 您的验证码是：{smsCaptchaCode}。请不要把验证码泄露给其他人。
    template-param: smsCaptchaCode
  login:
    template: 登录校验码：{smsCaptchaCode}，此校验码只用于登录您的账户，如非本人操作，请忽略该短信。
    template-param: smsCaptchaCode
  restPassword:
    template: 您的短信验证码是{smsCaptchaCode}。您正在通过手机号重置登录密码，如非本人操作，请忽略该短信。
    template-param: smsCaptchaCode
  registerFail:
    template: 您在国家生物药技术创新中心官网提交的账号注册审核未通过。不通过理由为：{name}
    template-param: name
  registerSuccess:
    template: 您在国家生物药技术创新中心官网的账号注册已审核通过。
  orderWork:
    template: "{userName}您好，租赁仪器使用明细已经生成，请您到国家生物技术创新中心平台（https://www.nctib.org.cn/）-个人空间-明细确认模块中，确认近期使用明细，若超过7个工作日未确认系统将自动确认。另请在15个工作日内付款。若有疑问，请邮箱联系：{emailAddr}"
    template-param:
      - userName
      - emailAddr
  orderBack:
    template: 您预约的仪器使用审核已被退回，请您到国家生物药技术创新平台（https://www.nctib.org.cn/）- 个人中心- 我的预约，查看退回原因并修改后提交。
  orderReject:
    template: 您预约的仪器使用审核不通过，请您到国家生物药技术创新平台（https://www.nctib.org.cn/）- 个人中心 - 我的预约，查看不通过原因并重新预约。
  orderPass:
    template: 您预约的仪器使用审核通过，预约成功，请您按时前往。

oauth2:
    web-client-configurations:
        - access-token-validity-in-seconds: 302400
          refresh-token-validity-in-seconds-for-remember-me: 604800
          client-id: policy_pc
          secret: fengyun
          client-type: pc
        - access-token-validity-in-seconds: -1
          refresh-token-validity-in-seconds-for-remember-me: -1
          client-id: policy_app
          secret: fengyun
          client-type: app
    ignore:
        urls:
            - /api/log
            - /api/upload/file/ua
            - /login**
            - /signup-login
            - /v2/api-docs/**
            - /api/refresh-token
            - /api/register/**
            - /api/states/**
            - /api/world/**
            - /api/dict/value
            - /api/dict/value/multiple
            - /api/init/**
            - /ueditor/**
            - /api/download/file
            - /rpc/**
            - /api/ua/**
            - /ws-push/**
            - /api/log/user/behavior
            - /api/cyt/register/tel
            - /api/otherservice/**
            - /mp/**
            - /api/logout*
            - /mini-app/*
            - /ua/show/**
            - /api/declare/list
            - /api/declare/get/**
    permission:
        enable: true


# mybatis-plus配置 详见： https://baomidou.gitee.io/mybatis-plus-doc/#/spring-boot
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml,classpath:/mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      updateStrategy: IGNORED

wx:
  miniapp:
    appid: wxfe365693e8dae476
    secret: 5c6651f4b9f211c2543261d45dfcf98f
    token:
    aesKey:
    msgDataFormat: JSON
  mp:
    appid:
    secret:
    token:
    aesKey:
    config-storage:
      type: redistemplate
      key-prefix: wx

email:
  host: smtp.partner.outlook.cn
  port: 587
  username: <EMAIL>
  password: NCTIB@2024
  nickname: NADTIP

