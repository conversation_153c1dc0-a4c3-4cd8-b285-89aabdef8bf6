package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_project_schedule")
@Api(hidden = true)
public class ProjectSchedule extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "项目实施年限")
    @TableField("implementation_period")
    private String implementationPeriod;

    @ApiModelProperty(value = "项目总投资")
    @TableField("total_investment")
    private String totalInvestment;

    @ApiModelProperty(value = "已投入资金")
    @TableField("invested_funds")
    private String investedFunds;

    @ApiModelProperty(value = "新增投入资金")
    @TableField("new_investment")
    private String newInvestment;

    @ApiModelProperty(value = "概述")
    @TableField("overview")
    private String overview;

    @ApiModelProperty(value = "第一阶段时间")
    @TableField("first_stage_date")
    private String firstStageDate;

    @ApiModelProperty(value = "第一阶段进度安排")
    @TableField("first_schedule")
    private String firstSchedule;

    @ApiModelProperty(value = "第二阶段时间")
    @TableField("second_stage_date")
    private String secondStageDate;

    @ApiModelProperty(value = "第二阶段进度安排")
    @TableField("second_schedule")
    private String secondSchedule;

    @ApiModelProperty(value = "第三阶段时间")
    @TableField("third_stage_date")
    private String thirdStageDate;

    @ApiModelProperty(value = "第三阶段进度安排")
    @TableField("third_schedule")
    private String thirdSchedule;
}
