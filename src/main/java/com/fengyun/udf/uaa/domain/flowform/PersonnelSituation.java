package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 项目人员表单
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_personnel_situation")
@Api(hidden = true)
public class PersonnelSituation extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "负责人姓名")
    @TableField("principal_name")
    private String principalName;

    @ApiModelProperty(value = "性别")
    @TableField("gender")
    private String gender;

    @ApiModelProperty(value = "出生日期")
    @TableField("birth_date")
    private Date birthDate;

    @ApiModelProperty(value = "通讯地址与邮编")
    @TableField("address_post_code")
    private String addressPostCode;

    @ApiModelProperty(value = "联系电话")
    @TableField("tel")
    private String tel;

    @ApiModelProperty(value = "受教育程度")
    @TableField("education")
    private String education;

    @ApiModelProperty(value = "电子邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty(value = "毕业院校")
    @TableField("graduated_school")
    private String graduatedSchool;

    @ApiModelProperty(value = "专业名称")
    @TableField("professional")
    private String professional;

    @ApiModelProperty(value = "工作方式 1.全职 2.兼职")
    @TableField("working_way")
    private String workingWay;

    @ApiModelProperty(value = "兼职时间比例")
    @TableField("part_time_ratio")
    private Float partTimeRatio;

    @ApiModelProperty(value = "职称")
    @TableField("professional_title")
    private String professionalTitle;

    @ApiModelProperty(value = "主要工作经历和教育经历")
    @TableField("main_work_education")
    private String mainWorkEducation;

    @ApiModelProperty(value = "主要工作业绩")
    @TableField("work_performance")
    private String workPerformance;

    @ApiModelProperty(value = "项目主要参加人员情况")
    @TableField("project_participants")
    private String projectParticipants;
}
