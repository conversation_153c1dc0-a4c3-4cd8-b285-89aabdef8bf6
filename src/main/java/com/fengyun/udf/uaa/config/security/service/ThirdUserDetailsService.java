package com.fengyun.udf.uaa.config.security.service;

import com.fengyun.udf.security.dto.AuthUser;
import com.fengyun.udf.uaa.domain.system.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by wangg on 2019/7/5.
 */
@Component
public class ThirdUserDetailsService extends BaseUserDetailsService {

    public UserDetails loadUserByOpenIdAndType(String openId, String openType) throws UsernameNotFoundException {
        User user = userService.findByOpenIdAndType(openId, openType);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在");
        }
        Set<String> authSet = new HashSet<>();
        authSet.add(user.getType());

        Collection<? extends GrantedAuthority> authorities
                = AuthorityUtils.createAuthorityList(authSet.toArray(new String[0]));
        return new AuthUser(user.getId(), user.getNickName(),
                user.getId(), user.getPassword(),
                true, true, true, true, authorities);
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return null;
    }
}
