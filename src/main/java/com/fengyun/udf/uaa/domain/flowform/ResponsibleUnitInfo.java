package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 承担单位基本情况
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_responsible_unit_info")
@Api(hidden = true)
public class ResponsibleUnitInfo extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "承担单位情况id")
    @TableField("ref_id")
    private String refId;

    @ApiModelProperty(value = "注册类型 registered 已注册/registering 拟注册")
    @TableField("register_type")
    private String registerType;

    @ApiModelProperty(value = "注册时间")
    @TableField("register_date")
    private Date registerDate;

    @ApiModelProperty(value = "注册地址")
    @TableField("register_address")
    private String registerAddress;

    @ApiModelProperty(value = "法人代表")
    @TableField("corporate_representative")
    private String corporateRepresentative;

    @ApiModelProperty(value = "注册资本(万元)")
    @TableField("registered_capital")
    private String registeredCapital;

    @ApiModelProperty(value = "组织机构代码")
    @TableField("organization_code")
    private String organizationCode;

    @ApiModelProperty(value = "实缴资本")
    @TableField("paid_up_capital")
    private String paidUpCapital;

    @ApiModelProperty(value = "上市情况")
    @TableField("listing_situation")
    private String listingSituation;

    @ApiModelProperty(value = "职工总数")
    @TableField("employees")
    private Integer employees;

    @ApiModelProperty(value = "主要管理人员数")
    @TableField("management")
    private Integer management;

    @ApiModelProperty(value = "研发人员比例")
    @TableField("research_proportion")
    private String researchProportion;

    @ApiModelProperty(value = "是否高企")
    @TableField("is_high")
    private Boolean isHigh;

    @ApiModelProperty(value = "上年销售收入")
    @TableField("last_sales_revenue")
    private String lastSalesRevenue;

    @ApiModelProperty(value = "上年研发投入")
    @TableField("last_research_investment")
    private String lastResearchInvestment;

    @ApiModelProperty(value = "股权构成")
    @TableField("share_structure")
    private String shareStructure;

    @ApiModelProperty(value = "企业获得荣誉情况")
    @TableField("honor")
    private String honor;

    @ApiModelProperty(value = "产学研合作情况")
    @TableField("cooperate")
    private String cooperate;

    @ApiModelProperty(value = "今后三年财务情况预测")
    @TableField("finance_prediction")
    private String financePrediction;

}
