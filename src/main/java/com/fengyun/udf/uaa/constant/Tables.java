package com.fengyun.udf.uaa.constant;

/**
 * <AUTHOR>
 * @version version 1.0
 */
public enum Tables {
    ;

    public enum T_USER {
        ID,
        TENANT_ID,
        LOGIN_NAME,
        TYPE,
        TEL,
        STATUS,
        IS_SYS
    }

    public enum T_ROLE {
        ID,
        ROLE,
        ROLE_NAME,
        <PERSON><PERSON><PERSON>PTION,
        IS_SYS,
        IS_VISIBLE,
        TENANT_ID
    }

    public enum T_TENANT {
        ID,
        CODE,
        IS_SYS,
        STATUS
    }

    public enum T_MENU {
        ID,
        PARENT_ID,
        IS_VISIBLE,
        IS_OPERATION,
        STATUS
    }

    public enum T_GROUP {
        ID,
        PARENT_ID,
        STATUS
    }

    public enum T_USER_ROLE {
        ID,
        USER_ID,
        ROLE_ID,
        TENANT_ID
    }

    public enum T_USER_GROUP {
        ID,
        USER_ID,
        GROUP_ID,
        TENANT_ID
    }

    public enum T_ROLE_MENU {
        ID,
        ROLE_ID,
        MENU_ID,
        TENANT_ID
    }

    public enum T_CODE_VALUE {
        ID,
        TYPE_CODE,
        VALUE,
        STATUS,
        IS_VIS,
        TENANT_CODE
    }

    public enum T_CODE_TYPE {
        ID,
        CODE,
        STATUS,
        IS_SYS,
        TENANT_CODE
    }

}
