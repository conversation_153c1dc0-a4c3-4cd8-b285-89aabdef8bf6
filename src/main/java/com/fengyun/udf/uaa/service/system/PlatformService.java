package com.fengyun.udf.uaa.service.system;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fengyun.udf.cache.redis.service.RedisClient;
import com.fengyun.udf.dto.RedisObjectDTO;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.system.Platform;
import com.fengyun.udf.uaa.dto.request.system.PlatformFieldQueryDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldListDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldUpdateDTO;
import com.fengyun.udf.uaa.mapper.system.PlatformMapper;
import com.fengyun.udf.uaa.service.BaseService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class PlatformService extends BaseService {
    @Autowired
    private PlatformMapper platformMapper;

    @Autowired
    private RedisClient redisClient;

    public List<PlatformDTO> getAttributeList() {
        List<Platform> list = platformMapper.selectList(new LambdaQueryWrapper<Platform>()
                .orderByAsc(Platform::getSort));
        List<PlatformDTO> dtoList = new ArrayList<>();
        for (Platform platform : list) {
            PlatformDTO dto = new PlatformDTO();
            BeanUtils.copyProperties(platform, dto);
            dtoList.add(dto);
        }
        return dtoList;
    }

    public void saveAttribute(Map<String, String> map) {
        platformMapper.saveAttribute(map);
        // 刷新缓存
        getPlatformInfo(true);
    }

    public Map<String, String> getPlatformInfo(Boolean forceFromDatabase) {
        RedisObjectDTO redisObjectDTO = new RedisObjectDTO("", "", "platformInfo");
        String info = redisClient.getObject(redisObjectDTO);
        if (!forceFromDatabase && info != null) {
            Map map = JSON.parseObject(info, Map.class);
            return map;
        } else {
            List<PlatformDTO> list = getAttributeList();
            Map<String, String> map = new HashMap<>();
            for (PlatformDTO dto : list) {
                if (!Constants.RICHTEXT_TYPE.equals(dto.getType())) {
                    map.put(dto.getName(), dto.getValue());
                }
            }
            redisObjectDTO.setValue(map);
            redisClient.setObject(redisObjectDTO);
            return map;
        }
    }

    public PlatformDTO getAttributeByName(String name) {
        Platform platform = platformMapper.selectOne(new LambdaQueryWrapper<Platform>().eq(Platform::getName, name));
        PlatformDTO dto = new PlatformDTO();
        BeanUtils.copyProperties(platform, dto);
        return dto;
    }

    /**
     * 查询平台管理字段列表
     */
    public List<PlatformFieldListDTO> getFieldList(PlatformFieldQueryDTO dto) {
        List<PlatformFieldListDTO> list = platformMapper.getPlatformFieldList(dto);
        return list;
    }

    /**
     * 新增管理平台字段
     */
    public void createPlatformField(PlatformFieldDTO dto) {
        Platform platform = new Platform();
        BeanUtils.copyProperties(dto, platform);
        platform.create(SecurityUtils.getUserId());
        platformMapper.insert(platform);
    }

    /**
     * 修改管理平台字段
     */
    public void updatePlatformField(PlatformFieldUpdateDTO dto) {
        Platform platform = new Platform();
        BeanUtils.copyProperties(dto, platform);
        platform.update(SecurityUtils.getUserId());
        platformMapper.updateById(platform);
    }

    /**
     * 删除管理平台字段
     */
    public void deletePlatformField(Integer id) {
        platformMapper.deleteById(id);
    }
}
