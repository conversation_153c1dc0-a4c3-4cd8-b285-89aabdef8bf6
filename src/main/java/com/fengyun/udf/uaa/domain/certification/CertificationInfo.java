package com.fengyun.udf.uaa.domain.certification;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* <p>
* 用户认证信息表
* </p>
*
* <AUTHOR>
* @since 2022-01-25
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_certification_info")
@ApiModel(value = "CertificationInfo对象", description = "用户认证信息表")
public class CertificationInfo extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "uuid")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "企业组织机构名称")
    @TableField("org_name")
    private String orgName;

    @ApiModelProperty(value = "附件列表")
    @TableField("attachment")
    private String attachment;
}
