package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 2025/2/13
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DeclareEntMiddleChargePrincipalDetailDTO {

    @ApiModelProperty("序号")
    private String projectId;

    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    private String responsibleUnit;

    /**
     * 参与单位名称
     */
    @ApiModelProperty("参与单位名称")
    private String participateUnit;

    //-------------------------------负责人信息-------------------------------

    /**
     * 负责人姓名
     */
    @ApiModelProperty("负责人姓名")
    private String principalName;

    /**
     * 负责人所在单位
     */
    @ApiModelProperty("负责人所在单位")
    private String principalBelongingCompany;

    /**
     * 职务
     */
    @ApiModelProperty("职务")
    private String principalPosition;

    /**
     * 在本项目中承担主要工作
     */
    @ApiModelProperty("在本项目中承担主要工作")
    private String principalWork;

    /**
     * 备注(如有人员增减，请说明原因)
     */
    @ApiModelProperty("备注(如有人员增减，请说明原因)")
    private String remarks;

    /**
     * 是否为国创中心双聘人才
     */
    @ApiModelProperty("是否为国创中心双聘人才")
    private Boolean doubleHire;



}
