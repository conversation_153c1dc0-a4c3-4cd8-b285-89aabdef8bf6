package com.fengyun.udf.uaa.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@SuppressWarnings("all")
public final class Constants {

    public static final String PC_CLIENT_ID = "policy_pc";
    public static final String APP_CLIENT_ID = "policy_app";

    public static final String JUSTAUTH_TYPE_WECHAT_MP = "WECHAT_MP";

    //分隔符
    public static final String SEPARATOR_COLON = ":";
    public static final String SEPARATOR_HORIZONTAL = "-";

    public static final String YES = "Y";
    public static final String NO = "N";

    public static final String STATUS_ACTIVE = "A";
    public static final String STATUS_DELETED = "D";
    public static final String STATUS_DISABLE = "DA";//已禁用状态

    //短信客户端类型
    public static final String FY = "fy";
    public static final String ALI = "ali";
    public static final String HUYI = "huyi";
    public static final String SZXXZX = "szxxzx"; //苏州信息中心
    public static final String JJXY = "jjxy"; //九江浔阳移动短信
    public static final String SZZDWD = "szzdwd";//数字诊断问答

    public static final String FYKJ = "fykj";//公司

    //短信类型
    public static final String SMS_TYPE_REGISTER = "register";
    public static final String SMS_TYPE_LOGIN = "login";
    public static final String SMS_TYPE_REST_PASSWORD = "restPassword";
    public static final String SMS_TYPE_REST_REG_FAIL = "registerFail";
    public static final String SMS_TYPE_REST_REG_SUC = "registerSuccess";

    public static final String DICT_KEY = "dict";

    public static final String DEFAULT_TENANT_CODE = "operation";

    public static final String TYPE_STATE = "CO";
    public static final String TYPE_PROVINCE = "P";
    public static final String TYPE_CITY = "C";
    public static final String TYPE_COUNTY = "D";

    public static final String WECHAT_MINI_APP_SESSION_KEY = "mini-app:session-key:";

    public static final String SYS_EXPERT_STATUS_A = "A";
    public static final String SYS_EXPERT_STATUS_F = "F";
    public static final String SYS_DEFAULT_PASSWORD = "QWer!@#098Mm";
    public static final String SYS_SIGNUP_CHANNEL_PC =  "pc";

    public static final String XLXS_FORMAT_NAME = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    public static final String XLS_FORMAT_NAME = "application/vnd.ms-excel";
    public static final String FIELD_TYPE_CODE = "FIELD_OF_EXPERTISE";
    public static final String SOURCE_TYPE_CODE = "SYS_EXPERT_SOURCE";
    public static final String MODULE_NAME = "MODULE_NAME";

    public static final String EN_REG_STATUS_PEN = "pen";
    public static final String EN_REG_STATUS_PASS = "pass";
    public static final String EN_REG_STATUS_DEAD = "dead";

    public static final String DESENSITIZATION = "****";

    public static final String ZJPS_ROLE = "zjps";

    public static final String FIRST_REVIEW ="firstReview";

    public static final String SECOND_REVIEW = "secondReview";

    public static final String APPROVE = "approve";

    public static final String MIDDLE_INSPECTION = "middleInspection";
    public static final String END_INSPECTION = "endInspection";

    public static final String FINAL_REVIEW = "项目评审";

    public static final String RICHTEXT_TYPE = "neditor";

    public static final String CONTACT_TEL = "0512-67998885";
    public static final String CONTACT_EMAIL = "<EMAIL>";

    public static final Map<String, String> n2cMap = new HashMap<String, String>(10) {{
        put("0", "零");put("1", "壹");put("2", "贰");put("3", "叁");put("4", "肆");
        put("5", "伍");put("6", "陆");put("7", "柒");put("8", "捌");put("9", "玖");}};

    public enum CLIENT_TYPE {
        PC("PC端"),
        APP("移动端");

        CLIENT_TYPE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum IDENTITY_AUDIT_STATUS {
        PENDING("待审核"),
        REJECT("已拒绝"),
        PASS("已通过");

        IDENTITY_AUDIT_STATUS(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum AUDIT_STATUS {
        P("通过"),
        R("拒绝");

        AUDIT_STATUS(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum MENU_BELONG {
        management("管理后台菜单"),
        personal("个人中心菜单"),
        navigation("导航菜单");

        MENU_BELONG(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum ROLE_TYPE {
        management("管理角色"),
        general("普通角色");

        ROLE_TYPE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum APP_STATUS {
        UP("已上架"),
        DOWN("已下架"),
        D("删除");

        APP_STATUS(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum APP_TYPE {
        SYS("基础应用"),
        PRODUCT("产品应用");

        APP_TYPE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum WEBCONFIG_TYPE {
        portal("个人中心配置"),
        management("管理平台配置");

        WEBCONFIG_TYPE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum ENTERPRISE_STATUS {
        UNAUTH("未审核"),
        PENDING("待审核"),
        REJECT("已拒绝"),
        PASS("审核通过"),;

        ENTERPRISE_STATUS(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum ENTERPRISE_SOURCE {
        ADMIN("管理员"),
        APPLY("申请"),
        ;

        ENTERPRISE_SOURCE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum PE_STATUS {
        UNAUTH("未审核"),
        PENDING("待审核"),
        REJECT("已拒绝"),
        PASS("审核通过"),;

        PE_STATUS(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum PE_SOURCE {
        ADMIN("管理员"),
        APPLY("申请"),
        INVITE("邀请"),
        MEMBER("镇长"),
        ;

        PE_SOURCE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public  enum IDENTITY_TYPE {
        enterprise("企业");

        IDENTITY_TYPE(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum USER_CATEGORY {
        signup("注册用户"),
        vip("VIP用户"),
        ;

        USER_CATEGORY(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum SIGN_UP_CHANNEL {
        pc("PC"),
        mini_app("小程序"),
        app("APP"),
        ;

        SIGN_UP_CHANNEL(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum TDECLARE_STATUS {
        CONFIRM("已申报"),
        CANCEL("退回修改"),
        PENDING("未申报"),
        ;

        TDECLARE_STATUS(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum DECLARE_PROJECT_STATUS {
        SHANG_JIA("1","上架"),
        XIA_JIA("2", "下架");

        DECLARE_PROJECT_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum DECLARE_PROJECT_TOP {
        YES("Y"),
        NO("N");

        DECLARE_PROJECT_TOP(String code) {
            this.code = code;
        }

        private String code;

        public String getCode() {
            return code;
        }

    }

    public enum DECLARE_STATUS {
        DECLARE_STATUS_1("1","申报中"),
        DECLARE_STATUS_2("2", "已结束"),
        DECLARE_STATUS_3("3", "待开始");

        DECLARE_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum EN_DECLARE_STATUS {
        SUBMIT("0", "首次提交"),
        APPROVE("1", "审批中"),
        BACK("2", "退回修改"),
        APPROVE_PASS("3", "审批通过"),
        APPROVE_REJECT("4", "审批不通过");

        EN_DECLARE_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    public enum DECLARE_SUBMIT_STATUS {
        DECLARE_SUBMIT_STATUS_0("0", "待提交"),
        DECLARE_SUBMIT_STATUS_1("1", "已提交");

        DECLARE_SUBMIT_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    public enum DECLARE_PUBLIC_FLAG {
        DECLARE_PUBLIC_FLAG_0("0", "未公示"),
        DECLARE_PUBLIC_FLAG_1("1", "已公示"),
        DECLARE_PUBLIC_FLAG_2("2", "项目立项");

        DECLARE_PUBLIC_FLAG(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    public enum DECLARE_PUBLIC_STATUS {
        DECLARE_PUBLIC_STATUS_0("0", "待提交"),
        DECLARE_PUBLIC_STATUS_1("1", "申报中"),
        DECLARE_PUBLIC_STATUS_2("2", "审核中"),
        DECLARE_PUBLIC_STATUS_3("3", "项目公示"),
        DECLARE_PUBLIC_STATUS_4("4", "项目立项");

        DECLARE_PUBLIC_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    public enum EXPERT_PANEL_STATUS {
        EXPERT_PANEL_STATUS_0("0", "未关联"),
        EXPERT_PANEL_STATUS_1("1", "已关联");

        EXPERT_PANEL_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    public enum EXPERT_SCORE_TEMPLATE_STATUS {
        EXPERT_SCORE_TEMPLATE_STATUS_0("0", "未关联"),
        EXPERT_SCORE_TEMPLATE_STATUS_1("1", "已关联");

        EXPERT_SCORE_TEMPLATE_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    public enum SCORE_HISTORY_STATUS {
        SCORE_HISTORY_STATUS_0("0", "待提交"),
        SCORE_HISTORY_STATUS_1("1", "已提交");

        SCORE_HISTORY_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    /**
     * 用于个人中心项目状态，项目暂存-待提交，项目提交-项目初审，上传审批结果-第一轮审批通过/第一轮审批不通过，上传公示名单-项目立项/项目未立项
     */
    public enum NEW_PROJECT_STATUS {
        // 项目状态
        TEMP("0","待提交"),
        FIRST_REVIEW("1","项目初审"),
        FIRST_REVIEW_PASS("2","第一轮审核通过"),
        FIRST_REVIEW_NO_PASS("3","第一轮审核不通过"),
        SECOND_REVIEW_PASS("4","第二轮审核通过"),
        SECOND_REVIEW_NO_PASS("5","第二轮审核不通过"),
        PROJECT_APPROVE("6","项目立项"),
        PROJECT_NO_APPROVE("7","项目未立项"),
        FINAL_REVIEW_PASS("8","通过评审"),
        FINAL_REVIEW_NO_PASS("9","未通过评审"),
        FINAL_REVIEW_PENDING("10","待定"),
        MID_INSPECTION("11","中期检查"),
        MID_INSPECTION_AUDITING("12","中期检查审核中"),
        MID_INSPECTION_PASS("13","中期检查通过"),
        MID_INSPECTION_NO_PASS("14","中期检查不通过"),
        FINAL_ACCEPTANCE("15","结题验收"),
        FINAL_ACCEPTANCE_AUDITING("16","结题验收审核中"),
        FINAL_ACCEPTANCE_PASS("17","结题验收通过"),
        FINAL_ACCEPTANCE_NO_PASS("18","结题验收不通过"),
        ;

        NEW_PROJECT_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum NEW_PROJECT_FLOW {
        TEMP("0","待提交"),
        REVIEW("1","项目初审"),
        FIRST_REVIEW("2","第一轮审核"),
        SECOND_REVIEW("3","第二轮审核"),
        PROJECT_APPROVE("4","项目立项(项目评审结果)"),
        MID_INSPECTION("5","中期检查"),
        MID_INSPECTION_COMPLETE("6","中期检查完成"),
        FINAL_ACCEPTANCE("7","结题验收"),
        FINAL_ACCEPTANCE_COMPLETE("8","结题验收完成"),
        ;

        NEW_PROJECT_FLOW(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 项目进度
     */
    public enum PROJECT_PROGRESS {
        // 项目进度
        CONTRACT_SUBMIT("合同提交下发"),
        CONTRACT_PASS("合同提交阶段通过"),
        FIRST_RECEIPT("第一笔资金收据阶段"),
        FIRST_RECEIPT_PASS("第一笔资金收据阶段通过"),
        MID_INSPECTION("中期检查"),
        MID_INSPECTION_PASS("中期检查通过"),
        SECOND_RECEIPT("第二笔资金收据阶段"),
        SECOND_RECEIPT_PASS("第二笔资金收据阶段通过"),
        FINAL_ACCEPTANCE("结题验收"),
        FINAL_ACCEPTANCE_PASS("结题验收通过"),
        THIRD_RECEIPT("第三笔资金收据阶段"),
        THIRD_RECEIPT_PASS("第三笔资金收据阶段通过")
        ;

        PROJECT_PROGRESS(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum CONTRACT_ASSIGN_ROLE {
        // 合同审核节点
        SUBMIT("合同提交"),
        AUDIT("合同审核"),
        ;

        CONTRACT_ASSIGN_ROLE(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum RECEIPT_ASSIGN_ROLE {
        // 收据审核节点
        SUBMIT("收据提交"),
        AUDIT("收据审核"),
        ;

        RECEIPT_ASSIGN_ROLE(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum INSTRUMENT_STATUS {
        A("正常使用"),
        M("维修中"),
        S("已停用"),
        ;

        INSTRUMENT_STATUS(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum INSTRUMENT_ORDER_STATUS {
        pre("待审核"),
        back("审核退回"),
        reject("审核不通过"),
        pass("审核通过，预约成功"),
        cancel("已撤回"),
        ;

        INSTRUMENT_ORDER_STATUS(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum INSTRUMENT_ORDER_AUDIT_NODE {
        qy("企业提交"),
        audit("工作人员审核"),
        ;

        INSTRUMENT_ORDER_AUDIT_NODE(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum INSTRUMENT_ORDER_WORK_STATUS {
        draft("暂存"),
        commit("提交"),
        final_draft("最终暂存"),
        final_commit("最终提交"),
        ;

        INSTRUMENT_ORDER_WORK_STATUS(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum INSTRUMENT_ORDER_SETTLEMENT_STATUS {
        YES("已结算"),
        NO("未结算"),
        ;

        INSTRUMENT_ORDER_SETTLEMENT_STATUS(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    public enum CONSUMABLE_STATUS {
        YES("已上架"),
        NO("已下架"),
        ;

        CONSUMABLE_STATUS(String desc) {
            this.desc = desc;
        }

        private final String desc;

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 是否默认文字
     */
    public static final String TRUE_TEXT = "是";
    public static final String FALSE_TEXT = "否";
}
