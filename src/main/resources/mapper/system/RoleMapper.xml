<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.system.RoleMapper">
	<select id="searchRole" resultType="com.fengyun.udf.uaa.dto.response.system.RoleListDTO">
		SELECT role.ID AS id,
		role.ROLE AS role,
		role.ROLE_NAME AS roleName,
		role.DESCRIPTION AS roleDesc,
		role.IS_SYS AS isSys,
		role.ROLE_TYPE AS roleType,
		role.CREATED_ID AS createdId,
		role.CREATED_DATE AS createdDate,
		(SELECT NICK_NAME FROM t_user WHERE ID = role.CREATED_ID) AS createdName
		FROM t_role role
		WHERE 1=1
		<if test="condition.isSys != null and condition.isSys != ''">
			AND role.IS_SYS = #{condition.isSys}
		</if>
		<if test="condition.roleType != null and condition.roleType != ''">
			AND role.ROLE_TYPE = #{condition.roleType}
		</if>
	</select>

	<select id="selectRoleByUser" resultType="string">
		SELECT DISTINCT r.ROLE
		FROM t_role r
				 JOIN t_user_role ur ON ur.ROLE_ID = r.ID
		WHERE ur.USER_ID = #{userId}
	</select>

	<select id="getUserRoleList" resultType="com.fengyun.udf.uaa.domain.system.Role">
	  SELECT
		*
	  FROM
		t_role r
	  JOIN t_user_role ur ON r.id = ur.ROLE_ID
	  WHERE 1=1
		<if test="userId !=null and userId!=''">
			AND ur.USER_ID = #{userId}
		</if>

	</select>

	<select id="findByTenantId" resultType="com.fengyun.udf.uaa.domain.system.Role">
		SELECT *
		FROM t_role
		WHERE TENANT_ID = #{tenantId}
	</select>

	<select id="findByTenantIdAndIdentity" resultType="com.fengyun.udf.uaa.domain.system.Role">
		SELECT r.*
		FROM t_role r
		JOIN t_identity_role ir on ir.role_id = r.id
		WHERE ir.identity = #{identity}
		AND ir.tenant_id = #{tenantId}
	</select>
</mapper>
