package com.fengyun.udf.uaa.dto.response.instrument;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @since 2023-09-14
 */
@Getter
@Setter
public class InstrumentOrderExportListDTO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户类型")
    @DictData(target = "userTypeStr", codeType = "USER_TYPE")
    private Integer userType;
    private String userTypeStr;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户注册手机号")
    private String tel;

    @ApiModelProperty("核准金额")
    private Double amount;

    @ApiModelProperty("是否发送客户")
    private Boolean isSend;

    @ApiModelProperty("发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    @ApiModelProperty("明细文件")
    private String excelFile;

    @ApiModelProperty("生成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    @ApiModelProperty("是否确认")
    private Boolean isConfirm;

    @ApiModelProperty("确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmTime;

    @ApiModelProperty("联系方式")
    private String contactTel;

    @ApiModelProperty("联系邮箱")
    private String contactEmail;

}