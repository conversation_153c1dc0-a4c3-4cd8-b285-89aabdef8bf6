package com.fengyun.udf.uaa.web.rest.declareproject;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.constant.DeclareCons;
import com.fengyun.udf.uaa.dto.request.declareproject.*;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptListDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * 2024/7/22
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(value = "项目申报-项目收据-管理端", tags = "项目申报-项目收据-管理端")
@Validated
@RestController
@SuppressWarnings("all")
@RequestMapping({"/api/platform/declare/ent/receipt"})
@PreAuthorize("@pms.hasPermission('management')")
public class DeclareEntReceiptPlatformResource extends BaseResource {

    @Autowired
    private DeclareEntService declareEntService;

    @PostMapping("/audit")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/receipt/audit", methodDescription = "收据审核", httpMethod = "POST")
    @ApiOperation(value = "收据审核")
    public ResponseEntity<ReturnResultDTO<?>> auditReceipt(@Validated @RequestBody DeclareEntHandlerDTO dto) {
        if(StrUtil.isBlank(dto.getCategory())) {
            dto.setCategory(DeclareCons.RECEIPT_CATEGORY.FIRST.getCode());
        }
        declareEntService.auditReceipt(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/detail")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/receipt/detail", methodDescription = "收据详情", httpMethod = "POST")
    @ApiOperation(value = "收据详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "category", value = "收据次数", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectContractDetailDTO>> receiptDetail(
            @Validated @NotBlank(message = "项目id不能为空") String id, String category) {
        // 给收据默认赋值第一次
        if(StrUtil.isBlank(category)) {
            category = DeclareCons.RECEIPT_CATEGORY.FIRST.getCode();
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.receiptDetail(id, category));
    }

    @GetMapping("/page")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/receipt/page", methodDescription = "收据汇总列表", httpMethod = "POST")
    @ApiOperation(value = "收据汇总列表")
    public ResponseEntity<ReturnResultDTO<Page<ProjectReceiptListDTO>>> receiptPage(
            Page<ProjectReceiptListDTO> page,
            @ModelAttribute QueryContractDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.receiptPage(page, dto));
    }

}
