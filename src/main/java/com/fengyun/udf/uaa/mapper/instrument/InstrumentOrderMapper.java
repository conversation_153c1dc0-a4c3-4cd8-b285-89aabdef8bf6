package com.fengyun.udf.uaa.mapper.instrument;

import com.fengyun.udf.uaa.domain.instrument.InstrumentOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderListDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 仪器预约 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
public interface InstrumentOrderMapper extends BaseMapper<InstrumentOrder> {

    List<InstrumentOrderListDTO> selectPage(Page page, @Param("condition")InstrumentOrderQueryDTO dto);

    List<InstrumentOrderExportDTO> selectExportList(InstrumentOrderExportCreateDTO dto);

    List<InstrumentOrderExportDTO> selectOrderConsumable(Integer id);

    List<String> selectOrderDateTime(@Param("instrumentId")String instrumentId, @Param("orderDate")LocalDate orderDate);

    String selectNextOrderNum(String date);

}
