package com.fengyun.udf.uaa.service.declareproject;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.declareproject.*;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertScoreTemplate;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.*;
import com.fengyun.udf.uaa.dto.request.expertpanel.QueryRelationProjectDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.*;
import com.fengyun.udf.uaa.dto.response.module.SaveContentDto;
import com.fengyun.udf.uaa.mapper.declareproject.*;
import com.fengyun.udf.uaa.mapper.expertpanel.ExpertScoreTemplateMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.module.ContentService;
import com.fengyun.udf.util.DateConverter;
import com.fengyun.udf.util.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Transactional
@RequiredArgsConstructor
public class DeclareProjectService extends BaseService {

    private final DeclareProjectMapper declareProjectMapper;

    private final DeclareEntMapper declareEntMapper;

    private final DeclareEntRoleMapper declareEntRoleMapper;

    private final ProcessModelMapper processModelMapper;

    private final DeclareFlowService declareFlowService;

    private final RuNodeTaskHistoryMapper ruNodeTaskHistoryMapper;

    private final ContentService contentService;

    private final ExpertScoreTemplateMapper expertScoreTemplateMapper;

    /**
     * 申报政策保存
     * @param dto 编辑申报信息DTO
     */
    public void save(CreateDeclareProjectDTO dto) {
        ZonedDateTime nowTime = ZonedDateTime.now();
        DeclareProject entity = new DeclareProject();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(UUIDGenerator.getUUID());
        // 关联流程id默认写死
        entity.setFlowId("1");
        if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
            entity.setAttachments(JSON.toJSONString(dto.getAttachments()));
        }
        entity.setSubmitStatus(Constants.DECLARE_SUBMIT_STATUS.DECLARE_SUBMIT_STATUS_0.getCode());
        entity.setStatus(Constants.DECLARE_PROJECT_STATUS.SHANG_JIA.getCode());
        entity.setTop(Constants.DECLARE_PROJECT_TOP.NO.getCode());
        entity.setProjectStatus(Constants.NEW_PROJECT_FLOW.TEMP.getCode());
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        entity.setCreatedId(userId);
        entity.setUpdatedId(userId);
        entity.setCreatedDate(nowTime);
        entity.setUpdatedDate(nowTime);
        entity.setPrefix(dto.getPrefix());
        entity.setSecondExpertScoreTemplateId(dto.getSecondExpertScoreTemplateId());
        // 评审模板
        if (CollectionUtils.isNotEmpty(dto.getReviewTemplate())) {
            entity.setReviewTemplate(JSON.toJSONString(dto.getReviewTemplate()));
        }
        declareProjectMapper.insert(entity);
        // 更新状态
        if (StringUtils.isNotBlank(dto.getExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("1");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
        if (StringUtils.isNotBlank(dto.getSecondExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getSecondExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("2");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
    }

    /**
     * 新增发布
     * @param dto 编辑申报信息DTO
     */
    public void addAndSubmit(CreateDeclareProjectDTO dto) {
        ZonedDateTime nowTime = ZonedDateTime.now();
        DeclareProject entity = new DeclareProject();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(UUIDGenerator.getUUID());
        // 关联流程id默认写死
        entity.setFlowId("1");
        if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
            entity.setAttachments(JSON.toJSONString(dto.getAttachments()));
        }
        entity.setSubmitStatus(Constants.DECLARE_SUBMIT_STATUS.DECLARE_SUBMIT_STATUS_1.getCode());
        entity.setStatus(Constants.DECLARE_PROJECT_STATUS.SHANG_JIA.getCode());
        entity.setTop(Constants.DECLARE_PROJECT_TOP.NO.getCode());
        entity.setProjectStatus(Constants.NEW_PROJECT_FLOW.REVIEW.getCode());
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        entity.setCreatedId(userId);
        entity.setUpdatedId(userId);
        entity.setCreatedDate(nowTime);
        entity.setUpdatedDate(nowTime);
        entity.setPrefix(dto.getPrefix());
        // 评审模板
        if (CollectionUtils.isNotEmpty(dto.getReviewTemplate())) {
            entity.setReviewTemplate(JSON.toJSONString(dto.getReviewTemplate()));
        }

        entity.setSecondExpertScoreTemplateId(dto.getSecondExpertScoreTemplateId());
        declareProjectMapper.insert(entity);
        // 更新状态
        if (StringUtils.isNotBlank(dto.getExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("1");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
        if (StringUtils.isNotBlank(dto.getSecondExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getSecondExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("2");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
    }

    /**
     * 申报政策更新
     * @param dto 编辑申报信息DTO
     */
    public void update(CreateDeclareProjectDTO dto) {
        DeclareProject entity = declareProjectMapper.selectById(dto.getId());
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        String oldExpertScoreTemplateId = entity.getExpertScoreTemplateId();
        entity.setAttachments(CollectionUtils.isEmpty(dto.getAttachments())?
                null : JSON.toJSONString(dto.getAttachments()));
        ZonedDateTime nowTime = ZonedDateTime.now();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        entity.setPrefix(dto.getPrefix());
        entity.setSecondExpertScoreTemplateId(dto.getSecondExpertScoreTemplateId());
        // 评审模板
        if (CollectionUtils.isNotEmpty(dto.getReviewTemplate())) {
            entity.setReviewTemplate(JSON.toJSONString(dto.getReviewTemplate()));
        }
        declareProjectMapper.updateById(entity);
        // 判断之前的模板是否有关联，没有则更新之前模板为未关联
        if (StringUtils.isNotBlank(oldExpertScoreTemplateId)) {
            int count = declareProjectMapper.getTemplateById(entity.getId(), oldExpertScoreTemplateId);
            if (count == 0) {
                ExpertScoreTemplate oldExpertScoreTemplate = expertScoreTemplateMapper.selectById(oldExpertScoreTemplateId);
                if (oldExpertScoreTemplate == null) {
                    throw new ValidationException("error", "模板不存在");
                }
                oldExpertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_0.getCode());
                expertScoreTemplateMapper.updateById(oldExpertScoreTemplate);
            }
        }
        // 更新状态
        if (StringUtils.isNotBlank(dto.getExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("1");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
        if (StringUtils.isNotBlank(dto.getSecondExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getSecondExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("2");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
    }

    /**
     * 提交发布
     * @param dto 编辑申报信息DTO
     */
    public void updateAndSubmit(CreateDeclareProjectDTO dto) {
        DeclareProject entity = declareProjectMapper.selectById(dto.getId());
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        String oldExpertScoreTemplateId = entity.getExpertScoreTemplateId();
        entity.setSubmitStatus(Constants.DECLARE_SUBMIT_STATUS.DECLARE_SUBMIT_STATUS_1.getCode());
        if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
            entity.setAttachments(JSON.toJSONString(dto.getAttachments()));
        }
        // 答辩ppt模板
        if (CollectionUtils.isNotEmpty(dto.getReviewTemplate())) {
            entity.setReviewTemplate(JSON.toJSONString(dto.getReviewTemplate()));
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        String prefix = entity.getPrefix();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        if(Constants.NEW_PROJECT_FLOW.TEMP.getCode().equals(entity.getProjectStatus())){
            entity.setProjectStatus(Constants.NEW_PROJECT_FLOW.REVIEW.getCode());
        }
        entity.setPrefix(prefix);
        entity.setSecondExpertScoreTemplateId(dto.getSecondExpertScoreTemplateId());
        declareProjectMapper.updateById(entity);
        // 判断之前的模板是否有关联，没有则更新之前模板为未关联
        if (StringUtils.isNotBlank(oldExpertScoreTemplateId)) {
            int count = declareProjectMapper.getTemplateById(entity.getId(), oldExpertScoreTemplateId);
            if (count == 0) {
                ExpertScoreTemplate oldExpertScoreTemplate = expertScoreTemplateMapper.selectById(oldExpertScoreTemplateId);
                if (oldExpertScoreTemplate == null) {
                    throw new ValidationException("error", "模板不存在");
                }
                oldExpertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_0.getCode());
                expertScoreTemplateMapper.updateById(oldExpertScoreTemplate);
            }
        }
        // 更新状态
        if (StringUtils.isNotBlank(dto.getExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("1");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
        if (StringUtils.isNotBlank(dto.getSecondExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(dto.getSecondExpertScoreTemplateId());
            if (expertScoreTemplate == null) {
                throw new ValidationException("error", "模板不存在");
            }
            expertScoreTemplate.setNumber("2");
            expertScoreTemplate.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_1.getCode());
            expertScoreTemplate.setUpdatedId(SecurityUtils.getUserId());
            expertScoreTemplate.setUpdatedDate(nowTime);
            expertScoreTemplateMapper.updateById(expertScoreTemplate);
        }
    }

    public Page<DeclareProjectShowDTO> page(Page<DeclareProjectShowDTO> page, QueryDeclareProjectDTO obj) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        List<DeclareProjectShowDTO> list = declareProjectMapper.page(page, obj);
        list.stream().forEach(dto -> {
            // 答辩ppt模板 收据模板 中期检查模板 结题验收模板
            dto.setReviewTemplate(JSON.parseArray(dto.getReviewTemplates(), AttachmentDTO.class));

            // 判断项目是否有申报数据
//            List<DeclareEnt> declareEnts = declareEntMapper.selectList(new LambdaQueryWrapper<DeclareEnt>()
//                    .eq(DeclareEnt::getDeclareId, dto.getId()));
            if (dto.getDeclareEntNums() > 0) {
                // 公开按钮判断?
                boolean publishButtonFlag = Constants.NEW_PROJECT_FLOW.SECOND_REVIEW.getCode().equals(dto.getProjectStatus());
/*                for (DeclareEnt declareEnt : declareEnts) {
                    // 没看明白
                    DeclareProject declareProject = declareProjectMapper.selectById(declareEnt.getDeclareId());
                    // 申报项目是第二轮审核通过就显示按钮?
                    if(Constants.NEW_PROJECT_FLOW.SECOND_REVIEW.getCode().equals(declareProject.getProjectStatus())){
                        publishButtonFlag = true;
                    }
                }*/
                // 申报期结束才能显示公开按钮
                if (publishButtonFlag && now.isAfter(LocalDate.parse(dto.getDeclareEndTime(), dateTimeFormatter))) {
                    // 显示发布公示按钮
                    if (StringUtils.isBlank(dto.getPublicFlag())
                            || dto.getPublicFlag().equals(Constants.DECLARE_PUBLIC_FLAG.DECLARE_PUBLIC_FLAG_0.getCode())) {
                        dto.setPublishButtonFlag("1");
                    }
                }
            }
        });
        page.setRecords(list);
        return page;
    }

    private void setStatus(DeclareProjectShowDTO entity) {
        if (StringUtils.isNotEmpty(entity.getDeclareEndTime())) {
            if (DateConverter.parseEnd(entity.getDeclareEndTime()).isBefore(ZonedDateTime.now())) {
                entity.setDeclareStatus(Constants.DECLARE_STATUS.DECLARE_STATUS_2.getCode());
            } else if (DateConverter.parseStart(entity.getDeclareBeginTime()).isAfter(ZonedDateTime.now())) {
                entity.setDeclareStatus(Constants.DECLARE_STATUS.DECLARE_STATUS_3.getCode());
            } else {
                entity.setDeclareStatus(Constants.DECLARE_STATUS.DECLARE_STATUS_1.getCode());
            }
        } else {
            entity.setDeclareStatus(Constants.DECLARE_STATUS.DECLARE_STATUS_1.getCode());
        }
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    public DeclareProjectShowDTO getById(String id) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        DeclareProjectShowDTO dto = new DeclareProjectShowDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.setPrefix(entity.getPrefix());

        dto.setAttachments(JSON.parseArray(entity.getAttachments(), AttachmentDTO.class));
        dto.setReviewTemplate(JSON.parseArray(dto.getReviewTemplates(), AttachmentDTO.class));

        if (StringUtils.isNotBlank(entity.getExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(entity.getExpertScoreTemplateId());
            if (expertScoreTemplate != null) {
                dto.setExpertScoreTemplateName(expertScoreTemplate.getTemplateName());
            }
        }
        if (StringUtils.isNotBlank(entity.getSecondExpertScoreTemplateId())) {
            ExpertScoreTemplate expertScoreTemplate = expertScoreTemplateMapper.selectById(entity.getSecondExpertScoreTemplateId());
            if (expertScoreTemplate != null) {
                dto.setSecondExpertScoreTemplateName(expertScoreTemplate.getTemplateName());
            }
        }

        List<DeclareEnt> declareEntList = declareEntMapper.selectList(new LambdaQueryWrapper<DeclareEnt>()
                .eq(DeclareEnt::getCreatedId, SecurityUtils.getUserId())
                .eq(DeclareEnt::getDeclareId, entity.getId()));
        if (CollectionUtils.isNotEmpty(declareEntList)) {
            dto.setDeclareFlag(false);
            dto.setDeclareContinue(false);
            declareEntList.forEach(item -> {
                if (StringUtils.isEmpty(item.getStatus())
                        || item.getStatus().equals(Constants.TDECLARE_STATUS.PENDING.name())
                        || item.getStatus().equals(Constants.TDECLARE_STATUS.CANCEL.name())) {
                    dto.setDeclareContinue(true);
                }
            });
        } else {
            dto.setDeclareFlag(true);
        }
        setStatus(dto);
        return dto;
    }

    /**
     * 删除
     * @param id
     */
    public void delete(String id) {
        declareProjectMapper.deleteById(id);
    }

    /**
     * 企业端列表查询
     * @param page
     * @param obj
     * @return
     */
    public Page<DeclareProjectShowDTO> list(Page page, QueryDeclareProjectDTO obj) {
        // 只查询上架的、已提交的
        obj.setSubmitStatus(Constants.DECLARE_SUBMIT_STATUS.DECLARE_SUBMIT_STATUS_1.getCode());
        obj.setStatus(Constants.DECLARE_PROJECT_STATUS.SHANG_JIA.getCode());
        List<DeclareProjectShowDTO> list = declareProjectMapper.page(page, obj);
        if (CollectionUtils.isNotEmpty(list)){
            // 设置状态
            for (DeclareProjectShowDTO entity : list) {
                LambdaQueryWrapper<DeclareEnt> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(DeclareEnt::getCreatedId, SecurityUtils.getUserId())
                        .eq(DeclareEnt::getDeclareId, entity.getId());
                List<DeclareEnt> declareEntList = declareEntMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(declareEntList)) {
                    entity.setDeclareFlag(false);
                    entity.setDeclareContinue(false);
                    declareEntList.forEach(item -> {
                        if (StringUtils.isEmpty(item.getStatus())
                                || item.getStatus().equals(Constants.TDECLARE_STATUS.PENDING.name())
                                || item.getStatus().equals(Constants.TDECLARE_STATUS.CANCEL.name())) {
                            entity.setDeclareContinue(true);
                        }
                    });
                } else {
                    entity.setDeclareFlag(true);
                }
                setStatus(entity);
            }
        }
        page.setRecords(list);
        return page;
    }

    /**
     * 企业端查询我的申报
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 我的申列表报列表
     */
    public Page<DeclareEntShowDTO> myList(Page<DeclareEntShowDTO> page, QueryMyDeclareProjectDTO dto) {
        dto.setCreatedId(SecurityUtils.getUserId());
        List<DeclareEntShowDTO> list = declareEntMapper.page(page, dto)
                .stream()
                .peek(entity -> {
                    entity.setReviewTemplates(JSON.parseArray(entity.getReviewTemplate(), AttachmentDTO.class));
                    entity.setIsShowDownloadPPT(StringUtils.isNotEmpty(entity.getReviewTemplate())&&
                            Constants.NEW_PROJECT_STATUS.FIRST_REVIEW_PASS.getCode().equals(entity.getProjectStatus())
                            ? "1" : "0");
                    entity.setCloseDebatePPTs(JSON.parseArray(entity.getCloseDebatePPT(), AttachmentDTO.class));
                    entity.setCloseDebateVideos(JSON.parseArray(entity.getCloseDebateVideo(), AttachmentDTO.class));
                    setEnDeclareStatus(entity);
                })
                .collect(Collectors.toList());
        page.setRecords(list);
        return page;
    }

    public Page<DeclareEntExpertShowDTO> getRelationList(Page page, QueryRelationProjectDTO obj) {
        List<DeclareEntExpertShowDTO> list = new ArrayList<>();
        if(StringUtils.isEmpty(obj.getIndex()) || "1".equals(obj.getIndex())){
            list = declareEntMapper.pageRelation(page, obj);
        }else if("2".equals(obj.getIndex())){
            list = declareEntMapper.pageRelation2(page, obj);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            for (DeclareEntExpertShowDTO entity : list) {
                if (StringUtils.isNotBlank(entity.getMember())) {
                    List<String> userIds = splitParam(entity.getMember());
                    String userNames = userMapper.searchUserNames(userIds);
                    entity.setMemberName(userNames);
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    private void setEnDeclareStatus(DeclareEntShowDTO entity) {
        if (StringUtils.isNotBlank(entity.getEntStatus())) {
            if (entity.getEntStatus().equals(Constants.TDECLARE_STATUS.CONFIRM.name())) {
                entity.setEnDeclareStatus(Constants.EN_DECLARE_STATUS.APPROVE.getCode());
            }
            if (entity.getEntStatus().equals(Constants.TDECLARE_STATUS.CANCEL.name())) {
                entity.setEnDeclareStatus(Constants.EN_DECLARE_STATUS.BACK.getCode());
            }
        }
        if ("end".equals(entity.getCurrentRole())) {
            entity.setEnDeclareStatus(Constants.EN_DECLARE_STATUS.APPROVE_REJECT.getCode());
        }
    }

    public DeclareEntDetailDTO getById(String id, String type) {
        return getByIdAndChildren(id, "", type);
    }


    public List<String> getChildrenItemById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new ValidationException("error", "主键不能为空");
        }
        List<String> resultList = new ArrayList<>();
        DeclareProject declareProject = declareProjectMapper.selectById(id);
        DeclareEntDetailDTO showDTO = new DeclareEntDetailDTO();
        BeanUtils.copyProperties(declareProject, showDTO);
        JSONArray source = JSONArray.parseObject(declareProject.getFileContent(), JSONArray.class);
        if (CollectionUtils.isEmpty(source)) {
            return resultList;
        }
        int i = source.size();
        for (int j = 0; j < i; j++) {
            JSONObject jsonObject = source.getJSONObject(j);
            if (StringUtils.isNotBlank(String.valueOf(jsonObject.get("name")))) {
                resultList.add(jsonObject.getString("name"));
            }
        }
        return resultList;
    }


    public DeclareEntDetailDTO getByIdAndChildren(String id, String children, String type) {
        if (StringUtils.isBlank(id)) {
            throw new ValidationException("error", "主键不能为空");
        }
        DeclareEntDetailDTO showDTO = new DeclareEntDetailDTO();
        DeclareEnt declareEnt = declareEntMapper.selectById(id);
        DeclareEntInfoDTO entInfoDTO = new DeclareEntInfoDTO();
        BeanUtils.copyProperties(declareEnt, entInfoDTO);
        showDTO.setDeclareEntInfoDTO(entInfoDTO);
        showDTO.setCreatedDate(declareEnt.getCreatedDate());
        showDTO.setNode(declareEnt.getCurrentRole());
        showDTO.setDeclareEntId(declareEnt.getId());
        showDTO.setIsDeclare(false);
        return showDTO;
    }

    private ReentrantLock lock = new ReentrantLock();
    public void createEntDeclare(CreateDeclareEntDTO createDeclareEntDTO) {
        LambdaQueryWrapper<DeclareEnt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeclareEnt::getDeclareId, createDeclareEntDTO.getDeclareId());
        queryWrapper.eq(BaseDomain::getCreatedId, SecurityUtils.getUserId());
        List<DeclareEnt> declareEnts = declareEntMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(declareEnts)) {
            throw new ValidationException("error", "数据不存在");
        }
        DeclareEnt declareEnt = declareEnts.get(0);
        BeanUtils.copyProperties(createDeclareEntDTO, declareEnt);
        declareEnt.create(SecurityUtils.getUserId());
        declareEnt.setStatus(Constants.TDECLARE_STATUS.CONFIRM.name());
        declareEnt.setEntStatus(Constants.TDECLARE_STATUS.CONFIRM.name());
        declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.FIRST_REVIEW.getCode());
        declareEnt.setSubmitStatus(Constants.DECLARE_SUBMIT_STATUS.DECLARE_SUBMIT_STATUS_1.getCode());
        DeclareProject declareProject = declareProjectMapper.selectById(declareEnt.getDeclareId());
        declareProject.setProjectStatus(Constants.NEW_PROJECT_FLOW.REVIEW.getCode());
        lock.lock();
        try{
            if(StringUtils.isEmpty(declareEnt.getProjectNo())){
                String last4 = "0001";
                String maxProjectNo =  declareEntMapper.getMaxProjectNo(declareProject.getPrefix());
                if(!StringUtils.isEmpty(maxProjectNo)){
                    last4 = maxProjectNo.substring(maxProjectNo.length()-4);
                    BigDecimal bd = new BigDecimal(last4).add(new BigDecimal(1));
                    last4 = bd.toString();
                    int legn = last4.length();
                    if(legn != 4){
                        String prefix = "";
                        int dif = 4-legn;
                        for(int i =0;i<dif;i++){
                            prefix= prefix+"0";
                        }
                        last4 = prefix+last4;
                    }
                }

//           declareEnt.setProjectNo(ProjectNoUtils.generateProjectNo(declareProject.getPrefix(),""));
                declareEnt.setProjectNo(declareProject.getPrefix()+last4);
            }
            declareFlowService.start(declareEnt);
            declareEntMapper.updateById(declareEnt);
        }finally{
            lock.unlock();
        }

        // 新增历史节点
        RuNodeTaskHistory ruNodeTaskHistory = new RuNodeTaskHistory();
        ruNodeTaskHistory.setId(UUIDGenerator.getUUID());
        ruNodeTaskHistory.setAssignUserId(SecurityUtils.getUserId());
        ruNodeTaskHistory.setAssignUserName(SecurityUtils.getUserName());
        ruNodeTaskHistory.setAssignUserRole("enterprise");
        ruNodeTaskHistory.setProcessTime(ZonedDateTime.now());
        ruNodeTaskHistory.setCreatedId(SecurityUtils.getUserId());
        ruNodeTaskHistory.setRuProcessId(declareEnt.getId());
        ruNodeTaskHistory.setAuditRemarks("");
        ruNodeTaskHistory.setSelectEvent("start");
        ruNodeTaskHistory.create(SecurityUtils.getUserId());
        ruNodeTaskHistoryMapper.insert(ruNodeTaskHistory);
    }

    public void updateEntDeclare(CreateDeclareEntDTO createDeclareEntDTO) {
        LambdaQueryWrapper<DeclareEnt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeclareEnt::getDeclareId, createDeclareEntDTO.getDeclareId());
        queryWrapper.eq(BaseDomain::getCreatedId, SecurityUtils.getUserId());
        queryWrapper.orderByDesc(BaseDomain::getCreatedDate);
        List<DeclareEnt> declareEnts = declareEntMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(declareEnts)) {
            throw new ValidationException("error", "数据不存在");
        }
        DeclareEnt declareEnt = declareEnts.get(0);
        if (!"CANCEL".equals(declareEnt.getEntStatus())) {
            throw new ValidationException("error", "状态不正确，不能进行重新提交");
        }
        BeanUtils.copyProperties(createDeclareEntDTO, declareEnt);
        declareEnt.update(SecurityUtils.getUserId());
        declareEnt.setStatus(Constants.TDECLARE_STATUS.CONFIRM.name());
        declareEnt.setEntStatus(Constants.TDECLARE_STATUS.CONFIRM.name());
        declareEnt.setSubmitStatus(Constants.DECLARE_SUBMIT_STATUS.DECLARE_SUBMIT_STATUS_1.getCode());
        LambdaQueryWrapper<DeclareEntRole> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(DeclareEntRole::getDeclareEntId, declareEnt.getId());
        queryWrapper1.eq(DeclareEntRole::getCurrentRole, declareEnt.getCurrentRole());
        List<DeclareEntRole> declareEntRoles = declareEntRoleMapper.selectList(queryWrapper1);
        String id = "";
        if (CollectionUtils.isNotEmpty(declareEntRoles)) {
            id = declareEntRoles.get(0).getId();
        }
        declareFlowService.next(declareEnt, "restart", "", id);
        declareEntMapper.updateById(declareEnt);
    }

    public Boolean enAble() {
        Boolean enable = false;
        ProcessModel processModel = processModelMapper.selectById("0");
        if (processModel != null) {
            enable = processModel.getDeleted();
        }
        return enable;
    }

    public List<DeclareProjectShowDTO> getDeclareList(){
        return declareProjectMapper.getDeclareList();
    }

    /**
     * 上架
     * @param id
     */
    public void up(String id) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        if (entity.getStatus().equals(Constants.DECLARE_PROJECT_STATUS.SHANG_JIA.getCode())) {
            throw new ValidationException("error", "申报项目已上架");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        entity.setStatus(Constants.DECLARE_PROJECT_STATUS.SHANG_JIA.getCode());
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareProjectMapper.updateById(entity);
    }

    /**
     * 下架
     * @param id
     */
    public void down(String id) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        if (entity.getStatus().equals(Constants.DECLARE_PROJECT_STATUS.XIA_JIA.getCode())) {
            throw new ValidationException("error", "申报项目已下架");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        entity.setStatus(Constants.DECLARE_PROJECT_STATUS.XIA_JIA.getCode());
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareProjectMapper.updateById(entity);
    }

    /**
     * 置顶
     * @param id
     */
    public void top(String id) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        if (entity.getTop().equals(Constants.DECLARE_PROJECT_TOP.YES.getCode())) {
            throw new ValidationException("error", "申报项目已置顶");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        entity.setTop(Constants.DECLARE_PROJECT_TOP.YES.getCode());
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareProjectMapper.updateById(entity);
    }

    /**
     * 取消置顶
     * @param id
     */
    public void cancelTop(String id) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        if (entity.getTop().equals(Constants.DECLARE_PROJECT_TOP.NO.getCode())) {
            throw new ValidationException("error", "申报项目已取消置顶");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        entity.setTop(Constants.DECLARE_PROJECT_TOP.NO.getCode());
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareProjectMapper.updateById(entity);
    }

    public void publish(String id, SaveContentDto dto) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        if (entity.getTop().equals(Constants.DECLARE_PUBLIC_FLAG.DECLARE_PUBLIC_FLAG_1.getCode())) {
            throw new ValidationException("error", "申报项目已发布公示");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        entity.setPublicFlag(Constants.DECLARE_PUBLIC_FLAG.DECLARE_PUBLIC_FLAG_1.getCode());
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareProjectMapper.updateById(entity);
        // 增加发布公示
        contentService.save(dto);
    }

    public void establish(String id) {
        DeclareProject entity = declareProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报项目不存在");
        }
        if (Constants.DECLARE_PUBLIC_FLAG.DECLARE_PUBLIC_FLAG_2.getCode().equals(entity.getPublicFlag())) {
            throw new ValidationException("error", "申报项目已立项");
        }
        if (StringUtils.isEmpty(entity.getReviewTemplate())) {
            throw new ValidationException("error", "请先上传答辩PPT模板");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        entity.setPublicFlag(Constants.DECLARE_PUBLIC_FLAG.DECLARE_PUBLIC_FLAG_2.getCode());
        entity.setProjectStatus(Constants.NEW_PROJECT_FLOW.PROJECT_APPROVE.getCode());
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareProjectMapper.updateById(entity);
    }

    public List<Map<String, String>> importReviewDTO(String id,String type,InputStream in){
        String userId = SecurityUtils.getUserId();
        DeclareProject declareProject = declareProjectMapper.selectById(id);
        if (in == null) {
            throw new ValidationException("error", "读取文件错误，请重新上传文件");
        }
        if(StringUtils.isEmpty(type)){
            throw new ValidationException("error","请提供文件上传类型!");
        }
        if(declareProject == null ){
            throw new ValidationException("error","项目不存在!");
        }
        List<Map<String, String>> errorList = new ArrayList<>();
        ExcelReader reader = ExcelUtil.getReader(in);
        List<ImportRecordDTO> list = reader.read(0, 2, ImportRecordDTO.class);
        if(CollectionUtils.isEmpty(list)){
            throw new ValidationException("","名单中没有数据!");
        }
        // 储存企业申报ID
        List<String > declareEntPassIdList = new ArrayList<>();
        for (ImportRecordDTO dto: list) {
            List<RecordDTO> recordDTOS = declareEntMapper.getReviewPassRecords(dto.getSubjectName(),dto.getResponsibleName(),id);
            if(CollectionUtils.isEmpty(recordDTOS) || recordDTOS.size()>1){
                Map<String, String> map = new HashMap<>();
                map.put("projectName",dto.getSubjectName());
                map.put("respName",dto.getResponsibleName());
                errorList.add(map);
            }else {
                declareEntPassIdList.add(recordDTOS.get(0).getEntId());
            }
        }
        // 存在错误,直接返回错误列表
        if(errorList.size() > 0){
            return errorList;
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        // 查询已通过企业申报列表
        List<DeclareEnt> declareEntPassList = declareEntMapper.selectBatchIds(declareEntPassIdList);
        // 处理项目状态
        declareProject.setProjectStatus(type.equals(Constants.FIRST_REVIEW)?
                Constants.NEW_PROJECT_FLOW.FIRST_REVIEW.getCode(): Constants.NEW_PROJECT_FLOW.SECOND_REVIEW.getCode());
        declareProject.setUpdatedId(SecurityUtils.getUserId());
        declareProject.setUpdatedDate(nowTime);
        // 处理已通过企业申报状态
        processReviewPass(
                declareEntPassList,
                type.equals(Constants.FIRST_REVIEW)
                        ? Constants.NEW_PROJECT_STATUS.FIRST_REVIEW_PASS.getCode()
                        : Constants.NEW_PROJECT_STATUS.SECOND_REVIEW_PASS.getCode(),
                nowTime,
                userId
        );

        // 查询未通过的企业申报列表
        List<DeclareEnt> declareEntListNoPass = declareEntMapper.selectList(new LambdaQueryWrapper<DeclareEnt>()
                // 当前项目
                .eq(DeclareEnt::getDeclareId, id)
                // 排除pass的项目
                .notIn(DeclareEnt::getId, declareEntPassIdList)
                // 已提交状态
                .eq(DeclareEnt::getSubmitStatus, "1")
                // 排除第一轮评审未通过
                .ne(DeclareEnt::getProjectStatus,Constants.NEW_PROJECT_STATUS.FIRST_REVIEW_NO_PASS.getCode()));
        // 处理未通过企业申报状态
        processReviewPass(
                declareEntListNoPass,
                type.equals(Constants.FIRST_REVIEW)
                        ? Constants.NEW_PROJECT_STATUS.FIRST_REVIEW_NO_PASS.getCode()
                        : Constants.NEW_PROJECT_STATUS.SECOND_REVIEW_NO_PASS.getCode(),
                nowTime,
                userId);
        // 更新项目
        declareProjectMapper.updateById(declareProject);
        // 更新企业申报
        declareEntPassList.addAll(declareEntListNoPass);
        declareEntPassList.forEach(declareEntMapper::updateById);
        return errorList;
    }

    /**
     * 批量处理项目状态
     * @param declareEntList 企业申报列表
     * @param projectStatus 项目状态
     * @param nowTime 当前时间
     * @param userId 操作用户Id
     */
    private void processReviewPass(List<DeclareEnt> declareEntList, String projectStatus, ZonedDateTime nowTime, String userId) {
        declareEntList.parallelStream().forEach(x -> {
            x.setProjectStatus(projectStatus);
            x.setUpdatedId(userId);
            x.setUpdatedDate(nowTime);
        });
    }

    public Page<DeclareEntExpertShowDTO> getRelationList2(Page page, QueryRelationProjectDTO obj) {
        List<DeclareEntExpertShowDTO> list = declareEntMapper.pageRelation2(page, obj);
        if (CollectionUtils.isNotEmpty(list)) {
            for (DeclareEntExpertShowDTO entity : list) {
                if (StringUtils.isNotBlank(entity.getMember())) {
                    List<String> userIds = splitParam(entity.getMember());
                    String userNames = userMapper.searchUserNames(userIds);
                    entity.setMemberName(userNames);
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    /**
     * 发起中期检查
     * (推进项目到中期检查阶段)
     * @param declareId 申报信息id
     */
    public void toMiddleInspectionStage(String declareId) {
        DeclareProject declareProject = getDeclareProjectFromDatabase(declareId);
        // 校验项目现在是不是立项阶段
        if(!Constants.NEW_PROJECT_FLOW.PROJECT_APPROVE.getCode().equals(declareProject.getProjectStatus())) {
            throw new ValidationException("申报信息", "该记录当前状态不允许进入中期检查阶段");
        }
        // 推进到中期检查阶段
        declareProject.setProjectStatus(Constants.NEW_PROJECT_FLOW.MID_INSPECTION.getCode());
        // 记录更新人时间
        declareProject.update(SecurityUtils.getUserId());
        declareProjectMapper.updateById(declareProject);

        // 第一次资金收据通过的用户项目, 进入中期检查阶段
        // 查找符合条件的项目
        List<DeclareEntListDTO> firstReceiptPassEns = declareEntMapper.getByDeclareIdAndStatus(declareId,
                Constants.NEW_PROJECT_STATUS.FINAL_REVIEW_PASS.getCode(),
                Constants.PROJECT_PROGRESS.FIRST_RECEIPT_PASS.name());
        if(firstReceiptPassEns.isEmpty()) {
            throw new ValidationException("中期检查", "该申报信息下没有符合条件的项目可以进入中期检查阶段");
        }
        // 更新项目状态和进程
        declareEntMapper.selectBatchIds(firstReceiptPassEns.stream()
                .map(DeclareEntListDTO::getId)
                .collect(Collectors.toSet()))
                .stream()
                .peek(x -> {
                    // 项目状态
                    x.setProjectStatus(Constants.NEW_PROJECT_STATUS.MID_INSPECTION.getCode());
                    // 项目进程
                    x.setProjectProgress(Constants.PROJECT_PROGRESS.MID_INSPECTION.name());
                    x.update(SecurityUtils.getUserId());
                })
                .forEach(declareEntMapper::updateById);
    }

    public List<DeclareEntImportErrorDTO> importMiddleInspectionEntList(String declareId, MultipartFile file) {
        DeclareProject declareProject = getDeclareProjectFromDatabase(declareId);
        // 校验项目阶段
        if(!Constants.NEW_PROJECT_FLOW.MID_INSPECTION.getCode().equals(declareProject.getProjectStatus())) {
            throw new ValidationException("申报信息", "该记录当前状态不允许导入中期检查专家评审通过模板");
        }
        try(InputStream fileInputStream = file.getInputStream()) {
            ExcelReader excelReader = ExcelUtil.getReader(fileInputStream);
            List<String> declareEntPassIdList = new ArrayList<>();
            List<DeclareEntImportErrorDTO> errorList = new ArrayList<>();
            List<ImportRecordDTO> entList = excelReader.read(0, 2, ImportRecordDTO.class);
            if(CollectionUtils.isEmpty(entList)){
                throw new ValidationException("","名单中没有数据!");
            }
            for (ImportRecordDTO dto: entList) {
                List<RecordDTO> records = declareEntMapper.getReviewPassRecords(dto.getSubjectName()
                                , dto.getResponsibleName(), declareId);
                if(CollectionUtils.isEmpty(records) || records.size() > 1){
                    DeclareEntImportErrorDTO error = new DeclareEntImportErrorDTO();
                    error.setSubjectName(dto.getSubjectName());
                    error.setResponsibleName(dto.getResponsibleName());
                    error.setErrorReason("未找到或找到多个对应项目");
                    errorList.add(error);
                }else {
                    RecordDTO record = records.get(0);
                    if(!Constants.NEW_PROJECT_STATUS.MID_INSPECTION_AUDITING.getCode().equals(record.getProjectStatus())) {
                        DeclareEntImportErrorDTO error = new DeclareEntImportErrorDTO();
                        error.setSubjectName(dto.getSubjectName());
                        error.setResponsibleName(dto.getResponsibleName());
                        error.setErrorReason("项目的状态不是\"中期检查审核中\"");
                        errorList.add(error);
                    }
                    declareEntPassIdList.add(record.getEntId());
                }
                // 存在错误,直接返回错误列表
                if(!errorList.isEmpty()){
                    return errorList;
                }
                handleMiddleInspectionPass(declareId, declareEntPassIdList);
            }
        } catch (IOException e) {
            throw new ValidationException("申报信息", "文件解析出错");
        }
        // 推进项目到中期检查完成阶段
        toMiddleInspectionCompleteStage(declareProject);
        return null;

    }

    private void handleMiddleInspectionPass(String declareId, List<String> declareEntPassIdList) {
        // 通过
        List<DeclareEnt> passList = declareEntMapper.selectBatchIds(declareEntPassIdList);
        // 未通过
        List<DeclareEnt> noPassList = declareEntMapper.selectList(new LambdaQueryWrapper<DeclareEnt>()
                // 当前项目
                .eq(DeclareEnt::getDeclareId, declareId)
                // 排除pass的项目
                .notIn(DeclareEnt::getId, declareEntPassIdList)
                // 中期检查审核中的
                .eq(DeclareEnt::getProjectProgress, Constants.NEW_PROJECT_STATUS.MID_INSPECTION_AUDITING.getCode()));
        String userId = SecurityUtils.getUserId();
        // 更新状态
        Stream.concat(
                passList.stream()
                        .peek(x -> {
                            x.setProjectStatus(Constants.NEW_PROJECT_STATUS.MID_INSPECTION_PASS.getCode());
                            x.setProjectProgress(Constants.PROJECT_PROGRESS.SECOND_RECEIPT.name());
                            x.update(userId);
                        }),
                noPassList.stream()
                        .peek(x -> x.setProjectStatus(Constants.NEW_PROJECT_STATUS.MID_INSPECTION_NO_PASS.getCode()).update(userId))
        ).forEach(declareEntMapper::updateById);
    }

    private DeclareProject getDeclareProjectFromDatabase(String id) {
        return Optional.ofNullable(declareProjectMapper.selectById(id))
                .orElseThrow(() -> new ValidationException("申报信息", "该记录在本系统中不存在"));
    }

    public void toMiddleInspectionCompleteStage(DeclareProject declareProject) {
        // 推进到中期检查阶段
        declareProject.setProjectStatus(Constants.NEW_PROJECT_FLOW.MID_INSPECTION_COMPLETE.getCode());
        // 记录更新人时间
        declareProject.update(SecurityUtils.getUserId());
        declareProjectMapper.updateById(declareProject);
    }

    public void toEndInspectionStage(String declareId) {
        DeclareProject declareProject = getDeclareProjectFromDatabase(declareId);
        // 校验项目现在是不是中期检查完成阶段
        if(!Constants.NEW_PROJECT_FLOW.MID_INSPECTION_COMPLETE.getCode().equals(declareProject.getProjectStatus())) {
            throw new ValidationException("结题验收", "该记录当前状态不允许进入结题验收阶段");
        }
        // 推进到中期检查阶段
        declareProject.setProjectStatus(Constants.NEW_PROJECT_FLOW.FINAL_ACCEPTANCE.getCode());
        // 记录更新人时间
        declareProject.update(SecurityUtils.getUserId());
        declareProjectMapper.updateById(declareProject);

        // 第一次资金收据通过的用户项目, 进入中期检查阶段
        // 查找符合条件的项目
        List<DeclareEntListDTO> secondReceiptPassEns = declareEntMapper.getByDeclareIdAndStatus(declareId,
                Constants.NEW_PROJECT_STATUS.MID_INSPECTION_PASS.getCode(),
                Constants.PROJECT_PROGRESS.SECOND_RECEIPT_PASS.name());
        if(secondReceiptPassEns.isEmpty()) {
            throw new ValidationException("结题验收", "该申报信息下没有符合条件的项目可以进入结题验收阶段");
        }
        // 更新项目状态和进程
        declareEntMapper.selectBatchIds(secondReceiptPassEns.stream()
                        .map(DeclareEntListDTO::getId)
                        .collect(Collectors.toSet()))
                .stream()
                .peek(x -> {
                    // 项目状态
                    x.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE.getCode());
                    // 项目进程
                    x.setProjectProgress(Constants.PROJECT_PROGRESS.FINAL_ACCEPTANCE.name());
                    x.update(SecurityUtils.getUserId());
                })
                .forEach(declareEntMapper::updateById);
    }

    public List<DeclareEntImportErrorDTO> importEndInspectionEntList(String declareId, MultipartFile file) {
        DeclareProject declareProject = getDeclareProjectFromDatabase(declareId);
        // 校验项目阶段
        if(!Constants.NEW_PROJECT_FLOW.FINAL_ACCEPTANCE.getCode().equals(declareProject.getProjectStatus())) {
            throw new ValidationException("申报信息", "该记录当前状态不允许导入结题验收专家评审通过模板");
        }
        try(InputStream fileInputStream = file.getInputStream()) {
            ExcelReader excelReader = ExcelUtil.getReader(fileInputStream);
            List<String> declareEntPassIdList = new ArrayList<>();
            List<DeclareEntImportErrorDTO> errorList = new ArrayList<>();
            List<ImportRecordDTO> entList = excelReader.read(0, 2, ImportRecordDTO.class);
            if(CollectionUtils.isEmpty(entList)){
                throw new ValidationException("","名单中没有数据!");
            }
            for (ImportRecordDTO dto: entList) {
                List<RecordDTO> records = declareEntMapper.getReviewPassRecords(dto.getSubjectName()
                        , dto.getResponsibleName(), declareId);
                if(CollectionUtils.isEmpty(records) || records.size() > 1){
                    DeclareEntImportErrorDTO error = new DeclareEntImportErrorDTO();
                    error.setSubjectName(dto.getSubjectName());
                    error.setResponsibleName(dto.getResponsibleName());
                    error.setErrorReason("未找到或找到多个对应项目");
                    errorList.add(error);
                }else {
                    RecordDTO record = records.get(0);
                    if(!Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE_AUDITING.getCode().equals(record.getProjectStatus())) {
                        DeclareEntImportErrorDTO error = new DeclareEntImportErrorDTO();
                        error.setSubjectName(dto.getSubjectName());
                        error.setResponsibleName(dto.getResponsibleName());
                        error.setErrorReason("项目的状态不是\"结题验收审核中\"");
                        errorList.add(error);
                    }
                    declareEntPassIdList.add(record.getEntId());
                }
                // 存在错误,直接返回错误列表
                if(!errorList.isEmpty()){
                    return errorList;
                }
                handleEndInspectionPass(declareId, declareEntPassIdList);
            }
        } catch (IOException e) {
            throw new ValidationException("申报信息", "文件解析出错");
        }
        // 推进项目到中期检查完成阶段
        toEndInspectionCompleteStage(declareProject);
        return null;
    }

    public void toEndInspectionCompleteStage(DeclareProject declareProject) {
        // 推进到中期检查阶段
        declareProject.setProjectStatus(Constants.NEW_PROJECT_FLOW.FINAL_ACCEPTANCE_COMPLETE.getCode());
        // 记录更新人时间
        declareProject.update(SecurityUtils.getUserId());
        declareProjectMapper.updateById(declareProject);
    }

    private void handleEndInspectionPass(String declareId, List<String> declareEntPassIdList) {
        // 通过
        List<DeclareEnt> passList = declareEntMapper.selectBatchIds(declareEntPassIdList);
        // 未通过
        List<DeclareEnt> noPassList = declareEntMapper.selectList(new LambdaQueryWrapper<DeclareEnt>()
                // 当前项目
                .eq(DeclareEnt::getDeclareId, declareId)
                // 排除pass的项目
                .notIn(DeclareEnt::getId, declareEntPassIdList)
                // 中期检查审核中的
                .eq(DeclareEnt::getProjectProgress, Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE_AUDITING.getCode()));
        String userId = SecurityUtils.getUserId();
        // 更新状态
        Stream.concat(
                passList.stream()
                        .peek(x -> {
                            x.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE_PASS.getCode());
                            x.setProjectProgress(Constants.PROJECT_PROGRESS.THIRD_RECEIPT.name());
                            x.update(userId);
                        }),
                noPassList.stream()
                        .peek(x -> x.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE_NO_PASS.getCode()).update(userId))
        ).forEach(declareEntMapper::updateById);
    }

    /**
     * 下发年度总结
     * @param declareId 申报信息ID
     */
    public void sendYearlySummary(String declareId) {
        DeclareProject declareProject = getDeclareProjectFromDatabase(declareId);
        // 检查是否可以下发
        int projectStatus = Integer.parseInt(declareProject.getProjectStatus());
        if(projectStatus < Integer.parseInt(Constants.NEW_PROJECT_FLOW.PROJECT_APPROVE.getCode())) {
            throw new ValidationException("申报信息", "当前申报信息状态不允许下发年度总结");
        }
        declareProject.setYearlySummary(true);
        declareProjectMapper.updateById(declareProject);
    }
    
}
