package com.fengyun.udf.uaa.config.websocket;

import com.alibaba.fastjson.JSON;
import com.fengyun.udf.uaa.dto.request.user.UserBehaviorDTO;
import com.fengyun.udf.util.DateConverter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by gongzuming on 2020/3/23.
 */
@Service
@Slf4j
public class WebSocketService {

    Logger webSocketLog = LoggerFactory.getLogger("ws-log");

    public void receive(List<ReceiveMessage> msgs) {
        String logReturnMsg = "{\"recordTime\":\"{}\",\"ip\":\"{}\",\"userId\":\"{}\",\"params\":\"{}\",\"url\":\"{}\"," +
                "\"behaviorType\":\"{}\",\"behaviorDesc\":\"{}\"}";
        for (ReceiveMessage msg : msgs){

            webSocketLog.info(logReturnMsg, DateConverter.ZonedDateTimeToString(msg.getRecordTime(),"YYYY-MM-dd hh:mm:ss"),msg.getIp(),msg.getUserId(), JSON.toJSONString(msg.getParams()),msg.getUrl(),msg.getBehaviorType(),msg.getBehaviorDesc());
        }
    }

    public void saveUserBehavior(UserBehaviorDTO userBehaviorDTO) {
        String logReturnMsg = "{\"ip\":\"{}\",\"logs\":\"{}\"}";
        webSocketLog.info(logReturnMsg,userBehaviorDTO.getIp(),userBehaviorDTO.getLogs());
    }
}
