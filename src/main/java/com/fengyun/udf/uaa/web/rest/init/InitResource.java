package com.fengyun.udf.uaa.web.rest.init;


import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.service.area.AreaService;
import com.fengyun.udf.uaa.service.area.AreaWorldService;
import com.fengyun.udf.uaa.service.module.ModuleListService;
import com.fengyun.udf.uaa.service.system.DictService;
import com.fengyun.udf.uaa.service.system.PlatformService;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/init")
@Api(value = "初始化数据", tags = "初始化数据")
@Slf4j
public class InitResource extends BaseResource {

    @Autowired
    private AreaService areaService;

    @Autowired
    private AreaWorldService worldService;

    @Autowired
    private DictService dictService;

    @Autowired
    private UserService userService;

    @Autowired
    private PlatformService platformService;

    @Autowired
    private ModuleListService moduleListService;

    @GetMapping("/cache")
    public ResponseEntity init(String dataType) {
        if (StringUtils.isEmpty(dataType) || "world".equals(dataType)) {
            log.info("初始化World数据到缓存");
            worldService.initCacheData();
        }

        if (StringUtils.isEmpty(dataType) || "area".equals(dataType)) {
            log.info("初始化地区数据到缓存");
            areaService.initCacheData();
        }

        if (StringUtils.isEmpty(dataType) || "dict".equals(dataType)) {
            log.info("初始化字典数据到缓存");
            dictService.refreshDictCache();
        }

        if (StringUtils.isEmpty(dataType) || "user".equals(dataType)) {
            log.info("初始化用户数据到缓存");
            userService.initNicknameCache();
        }

        if (StringUtils.isEmpty(dataType) || "platformInfo".equals(dataType)) {
            log.info("初始化平台信息数据到缓存");
            platformService.getPlatformInfo(true);
        }

        if (StringUtils.isEmpty(dataType) || "platformInfo".equals(dataType)) {
            log.info("初始化模块名称到缓存");
            moduleListService.initModuleNameCache();
        }

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), "");
    }

}
