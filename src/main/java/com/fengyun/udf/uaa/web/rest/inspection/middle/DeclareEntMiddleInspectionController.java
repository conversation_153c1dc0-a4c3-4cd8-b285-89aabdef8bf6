package com.fengyun.udf.uaa.web.rest.inspection.middle;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.inspection.middle.*;
import com.fengyun.udf.uaa.dto.response.inspection.middle.*;
import com.fengyun.udf.uaa.service.inspection.middle.DeclareEntMiddleInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 2024/7/22
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Validated
@RestController
@SuppressWarnings("all")
@RequestMapping("/api/customer/declare/inspection/middle")
@Api(value = "项目申报-中期检查-企业端", tags = "项目申报-中期检查-企业端")
public class DeclareEntMiddleInspectionController extends BaseResource {

    @Autowired
    private DeclareEntMiddleInspectionService middleInspectionService;

    @PostMapping("/basic/save")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/basic/save",
            methodDescription = "保存单位基本信息",
            httpMethod = "POST")
    @ApiOperation(value = "保存单位基本信息")
    public ResponseEntity<ReturnResultDTO<?>> saveUnitBasicInfo(
            @Valid @RequestBody UnitBasicInfoSaveDTO dto) {
        middleInspectionService.saveUnitBasicInfo(dto);
        return prepareReturnResult();
    }

    @GetMapping("/basic/detail")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/basic/detail",
            methodDescription = "获取单位基本信息",
            httpMethod = "GET")
    @ApiOperation(value = "获取单位基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<UnitBasicInfoDetailDTO>> getUnitBasicInfoDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getUnitBasicInfoDetail(projectId)
        );
    }

    @PostMapping("/completion/save")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/completion/save",
            methodDescription = "保存项目完成情况",
            httpMethod = "POST")
    @ApiOperation(value = "保存项目完成情况")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectCompletion(
            @Valid @RequestBody ProjectCompletionSaveDTO dto) {
        middleInspectionService.saveProjectCompletion(dto);
        return prepareReturnResult();
    }

    @GetMapping("/completion/detail")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/completion/detail",
            methodDescription = "获取项目完成情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目完成情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectCompletionDetailDTO>> getProjectCompletionDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectCompletionDetail(projectId)
        );
    }

    @PostMapping("/participant/save")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/participant/save",
            methodDescription = "保存项目人员情况",
            httpMethod = "POST")
    @ApiOperation(value = "保存项目人员情况")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectParticipant(
            @Valid @RequestBody ProjectParticipantSaveDTO dto) {
        middleInspectionService.saveProjectParticipant(dto);
        return prepareReturnResult();
    }

    @GetMapping("/participant/detail")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/participant/detail",
            methodDescription = "获取项目人员情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目人员情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectParticipantDetailDTO>> getProjectParticipantDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectParticipantDetail(projectId)
        );
    }

    @PostMapping("/funding/save")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/funding/save",
            methodDescription = "保存项目经费到位与使用情况",
            httpMethod = "POST")
    @ApiOperation(value = "保存项目经费到位与使用情况")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectFunding(
            @Valid @RequestBody ProjectFundingSaveDTO dto) {
        middleInspectionService.saveProjectFunding(dto);
        return prepareReturnResult();
    }

    @GetMapping("/funding/detail")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/funding/detail",
            methodDescription = "获取项目经费到位与使用情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目经费到位与使用情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectFundingDetailDTO>> getProjectFundingDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectFundingDetail(projectId)
        );
    }

    @PostMapping("/attachment/save")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/attachment/save",
            methodDescription = "保存附件",
            httpMethod = "POST")
    @ApiOperation(value = "保存附件")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectAttachment(
            @Valid @RequestBody DeclareEntMiddleAttachmentSaveDTO dto) {
        middleInspectionService.saveProjectAttachment(dto);
        return prepareReturnResult();
    }

    @GetMapping("/attachment/detail")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/attachment/detail",
            methodDescription = "获取附件",
            httpMethod = "GET")
    @ApiOperation(value = "获取附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<DeclareEntMiddleAttachmentDetailDTO>> getAttachmentDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getAttachmentDetail(projectId)
        );
    }

    @GetMapping("/submit")
    @AuditLog(
            method = "/api/customer/declare/inspection/middle/submit",
            methodDescription = "提交中期检查",
            httpMethod = "GET")
    @ApiOperation(value = "提交中期检查")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<?>> submitMiddleInspection(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        middleInspectionService.submitMiddleInspection(projectId);
        return prepareReturnResult();
    }

}
