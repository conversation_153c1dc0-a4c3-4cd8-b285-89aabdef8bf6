package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 企业用户审核表
* </p>
*
* <AUTHOR>
* @since 2022-04-13
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_user_audit")
@ApiModel(value = "UserAudit对象", description = "企业用户审核表")
public class UserAudit extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;
    
    @ApiModelProperty(value = "主键UUID")
    @TableId("ID")
    private String id;
    
    @TableField("LOGIN_NAME")
    private String loginName;
    
    @ApiModelProperty(value = "电话")
    @TableField("TEL")
    private String tel;
    
    @ApiModelProperty(value = "用户名")
    @TableField("NICK_NAME")
    private String nickName;
    
    @ApiModelProperty(value = "邮箱")
    @TableField("EMAIL")
    private String email;
    
    @ApiModelProperty(value = "密码")
    @TableField("PASSWORD")
    private String password;
    
    @TableField("TYPE")
    private String type;
    
    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private String status;
    
    @TableField("IS_SYS")
    private String isSys;
    
    @TableField("IMAGE")
    private String image;

    @ApiModelProperty(value = "注册渠道")
    @TableField("channel")
    private String channel;
    
    @ApiModelProperty(value = "是否订阅")
    @TableField("subscribe")
    private Integer subscribe;
    
    @ApiModelProperty(value = "订阅方式")
    @TableField("subscribe_method")
    private String subscribeMethod;
    
    @ApiModelProperty(value = "邀请人用户id")
    @TableField("inviter")
    private String inviter;
    
    @ApiModelProperty(value = "政策体系")
    @TableField("policy_system")
    private String policySystem;
    
    @ApiModelProperty(value = "附件执照")
    @TableField("attachment_license")
    private String attachmentLicense;
    
    @ApiModelProperty(value = "用户类型：1.企业 2.个人")
    @TableField("user_type")
    private Integer userType;
}
