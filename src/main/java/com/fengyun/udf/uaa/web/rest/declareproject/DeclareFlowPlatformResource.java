package com.fengyun.udf.uaa.web.rest.declareproject;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declareproject.CreateDeclareSubProjectDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareSubProjectShowDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareFlowPlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Api(value = "申报流程管理运营端", tags = "申报流程管理运营端")
@RestController
@RequestMapping({"/api/platform/declare/flow"})
@Validated
@PreAuthorize("@pms.hasPermission('management')")
public class DeclareFlowPlatformResource extends BaseResource {


    @Autowired
    private DeclareFlowPlatformService declareFlowPlatformService;


    @GetMapping("/page")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/flow/page", methodDescription = "流程管理列表", httpMethod = "GET")
    @ApiOperation(value = "流程管理列表")
    public ResponseEntity<ReturnResultDTO<DeclareSubProjectShowDTO>> getDeclareProjectList(Page page, String declareName) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareFlowPlatformService.page(page, declareName));
    }

    @PostMapping({"/add"})
    @CustomBaseException
    @ApiOperation(value = "新增流程管理")
    @AuditLog(method = "/api/platform/declare/flow/add", methodDescription = "新增流程管理", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> add(@RequestBody CreateDeclareSubProjectDTO dto) throws Exception {
        declareFlowPlatformService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/update"})
    @CustomBaseException
    @ApiOperation(value = "更新流程管理")
    @AuditLog(method = "/api/platform/declare/flow/update", methodDescription = "更新流程管理", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody CreateDeclareSubProjectDTO dto) throws Exception {
        declareFlowPlatformService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/get/{id}"})
    @ApiOperation(value = "根据id查询流程管理")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> get(@PathVariable("id") String id) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareFlowPlatformService.getById(id));
    }

    @PostMapping({"/delete/{id}"})
    @ApiOperation(value = "删除流程管理")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> delete(@PathVariable String id) throws Exception {
        declareFlowPlatformService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

}

