package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Created by Administrator on 2021/3/11.
 */
@Data
public class DeclareSubProjectShowDTO extends AbstractDTO {

    private String id;

    @ApiModelProperty("项目id")
    private String declareId;

    @ApiModelProperty("项目名称")
    private String declareName;

    @ApiModelProperty("子类大项id")
    private String bigTypeId;

    @ApiModelProperty("子类大项")
    private String bigType;

    @ApiModelProperty("子类小项id")
    private String smallTypeId;

    @ApiModelProperty("子类小项")
    private String smallType;

    @ApiModelProperty("申报材料")
    private List<FileDTO> fileContent;

    @ApiModelProperty("流程配置")
    private String flowConfigInfo;

    private String flowId;

    @JsonIgnore
    private String fileContents;

    @ApiModelProperty(value="创建日期")
    private ZonedDateTime createdDate;
    @ApiModelProperty(value="创建日期")
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createdDateStr;

}