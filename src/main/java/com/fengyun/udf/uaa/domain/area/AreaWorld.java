package com.fengyun.udf.uaa.domain.area;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("t_area_world")
@Data
public class AreaWorld {

    @TableId(value = "ID")
    private String id;

    @TableField(value = "NUMERIC_CODE")
    private String numericCode;

    @TableField(value = "ALPHA3_CODE")
    private String alpha3Code;

    @TableField(value = "NAME_PINYIN")
    private String namePinyin;

    @TableField(value = "NAME_CN")
    private String nameCn;

    @TableField(value = "NAME_EN")
    private String nameEn;

    @TableField(value = "STATUS")
    private String status;

    @TableField(value = "LEVEL")
    private Integer level;

    @TableField(value = "TYPE")
    private String type;

    @TableField(value = "SEQUENCE")
    private Long sequence;

    @TableField(value = "PARENT_ID")
    private String parentId;

}
