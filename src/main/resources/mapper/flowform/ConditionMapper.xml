<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.flowform.ConditionMapper">

    <select id="checkIsCreated" resultType="java.lang.Boolean">
        SELECT
            count(1) > 0
        FROM
            ${tableName} t
        WHERE
            t.project_id = #{projectId}
            AND
            t.created_id = #{userId}
    </select>
</mapper>
