<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.6.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.fengyun.udf.uaa</groupId>
    <artifactId>uaa</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>uaa</name>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Greenwich.SR1</spring-cloud.version>
        <mybatis-plus-boot-starter.version>3.5.7</mybatis-plus-boot-starter.version>
        <druid-spring-boot-starter.version>1.2.23</druid-spring-boot-starter.version>
        <springfox-swagger.version>2.9.2</springfox-swagger.version>
        <io.swagger.version>1.5.22</io.swagger.version>
        <base.version>swyy-2.0-SNAPSHOT</base.version>
    </properties>

    <repositories>
        <repository>
            <id>com.e-iceblue</id>
            <name>e-iceblue</name>
            <url>https://repo.e-iceblue.cn/repository/maven-public/</url>
        </repository>
    </repositories>


    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus-boot-starter.version}</version>
        </dependency>

        <!--代码生成器-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>fykj-1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.1</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${springfox-swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${springfox-swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${io.swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>${io.swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fengyun.udf.base</groupId>
            <artifactId>core</artifactId>
            <version>${base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fengyun.udf.base</groupId>
            <artifactId>cache</artifactId>
            <version>${base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fengyun.udf.base</groupId>
            <artifactId>security</artifactId>
            <version>${base.version}</version>
        </dependency>
		<dependency>
			<groupId>com.fengyun.udf.base</groupId>
			<artifactId>logger</artifactId>
			<version>${base.version}</version>
		</dependency>
        <dependency>
            <groupId>com.fengyun.udf.base</groupId>
            <artifactId>feign</artifactId>
            <version>${base.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fengyun.udf.base</groupId>
            <artifactId>sms</artifactId>
            <version>${base.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>net.viservice.editor</groupId>
            <artifactId>ueditor-extend-core</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.baidu.ueditor</groupId>
            <artifactId>ueditor</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-json-org</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.15.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
            <version>2.15.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.15</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>e-iceblue</groupId>-->
<!--            <artifactId>spire.xls.free</artifactId>-->
<!--            <version>5.1.0</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/src/main/resources/lib/spire.xls.free-5.1.0.jar</systemPath>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>e-iceblue</groupId>-->
<!--            <artifactId>spire.doc.free</artifactId>-->
<!--            <version>5.1.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>e-iceblue</groupId>
            <artifactId>spire.office.free</artifactId>
            <version>5.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>1.5</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <defaultGoal>spring-boot:run</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-resources</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/classes</outputDirectory>
                            <useDefaultDelimiters>false</useDefaultDelimiters>
                            <delimiters>
                                <delimiter>#</delimiter>
                            </delimiters>
                            <resources>
                                <resource>
                                    <directory>src/main/resources/</directory>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>*.yml</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>src/main/resources/</directory>
                                    <filtering>false</filtering>
                                    <excludes>
                                        <exclude>*.yml</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--mvn com.fengyun.yfb:generator-maven-plugin:1.0:code-->
            <plugin>
                <groupId>com.fengyun.yfb</groupId>
                <artifactId>generator-maven-plugin</artifactId>
                <version>1.0</version>
                <configuration>
                    <!-- 输出目录(默认java.io.tmpdir) -->
                    <outputDir>D:\xxxx\uaa\src\main\java</outputDir>
                    <!-- 是否覆盖同名文件(默认false) -->
                    <fileOverride>true</fileOverride>
                    <!-- mapper.xml 中添加二级缓存配置(默认true) -->
                    <enableCache>true</enableCache>
                    <!-- 开发者名称 -->
                    <author>gzm</author>
                    <!-- 是否开启 ActiveRecord 模式(默认true) -->
                    <activeRecord>false</activeRecord>
                    <!-- 数据源配置，( **必配** ) -->
                    <dataSource>
                        <driverName>com.mysql.jdbc.Driver</driverName>
                        <url>***************************************************************************</url>
                        <username>root</username>
                        <password>YSZ@yfb@2018</password>
                    </dataSource>
                    <strategy>
                        <!-- 生成策略，四种类型，从名称就能看出来含义：
                            nochange(默认),
                            underline_to_camel,(下划线转驼峰)
                            remove_prefix,(去除第一个下划线的前部分，后面保持不变)
                            remove_prefix_and_camel(去除第一个下划线的前部分，后面转驼峰) -->
                        <naming>remove_prefix_and_camel</naming>
                        <fieldNaming>underline_to_camel</fieldNaming>
                        <include>
                            <property>t_app</property>
                            <property>t_app_microservice</property>
                            <property>t_app_microweb</property>
                            <property>t_app_route</property>
                        </include>
                        <!-- 表前缀 -->
                        <tablePrefix>t_</tablePrefix>
                        <!--Entity中的ID生成策略（默认 id_worker）-->
                        <!--<idGenType>uuid</idGenType>-->
                        <superMapperClass>com.baomidou.mybatisplus.core.mapper.BaseMapper</superMapperClass>
                        <superEntityClass>com.fengyun.udf.domain.BaseDomain</superEntityClass>
                        <superEntityClassColumn>
                            <property>version</property>
                            <property>created_id</property>
                            <property>created_date</property>
                            <property>updated_id</property>
                            <property>updated_date</property>
                            <property>deleted</property>
                        </superEntityClassColumn>
                    </strategy>
                    <packageInfo>
                        <!-- 父级包名称，如果不写，下面的service等就需要写全包名(默认com.baomidou) -->
                        <parent>com.fengyun.udf.uaa</parent>
                        <!--service包名(默认service)-->
                        <service>service.app</service>
                        <!--entity包名(默认entity)-->
                        <entity>domain.app</entity>
                        <!--mapper包名(默认mapper)-->
                        <mapper>mapper.app</mapper>
                        <!--xml包名(默认mapper.xml)-->
                        <xml>mapperXml.app</xml>
                        <controller>web.rest.app</controller>
                        <requestDTO>dto.request.app</requestDTO>
                        <responseDTO>dto.response.app</responseDTO>
                    </packageInfo>
                    <template>
                    </template>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.38</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>local,swagger</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <spring.profiles.active>dev,swagger</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

</project>
