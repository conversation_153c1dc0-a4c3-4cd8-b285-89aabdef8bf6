package com.fengyun.udf.uaa.web.rest.operation.system;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.system.CreateMenuDTO;
import com.fengyun.udf.uaa.dto.request.system.UpdateMenuDTO;
import com.fengyun.udf.uaa.dto.response.system.MenuDTO;
import com.fengyun.udf.uaa.dto.response.system.MenuTreeDTO;
import com.fengyun.udf.uaa.service.system.MenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.fengyun.udf.uaa.constant.Constants.YES;

/**
 * Created by zhouyr on 2019/7/4.
 */
@RestController
@RequestMapping("/api")
@Api(value = "菜单管理", tags = "运营平台-系统管理")
@Validated
public class MenuResource extends BaseResource {
    @Autowired
    private MenuService menuService;

    @PostMapping("/menu")
    @CustomBaseException
    @AuditLog(method = "/api/menu", methodDescription = "新增菜单", httpMethod = "POST")
    @ApiOperation(value = "新增菜单")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> addTenantMenu(@RequestBody @Validated CreateMenuDTO createMenuDTO) {
        if (StringUtils.isNotBlank(createMenuDTO.getParentId())
                && !menuService.isMenuExists(createMenuDTO.getParentId(), YES)) {
            throw new ValidationException("parentId", "error.menu.parent.notExists");
        }
        menuService.addMenu(createMenuDTO, YES);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/menu/list")
    @CustomBaseException
    @AuditLog(method = "/api/menu/list", methodDescription = "查询菜单列表", httpMethod = "GET")
    @ApiOperation(value = "查询菜单列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<List<MenuTreeDTO>>> searchTenantMenu() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), menuService.searchOperationMenu(YES));
    }

    @GetMapping("/menu")
    @CustomBaseException
    @AuditLog(method = "/api/menu", methodDescription = "查询菜单详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "租户菜单ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查询租户菜单详情")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<MenuDTO>> getTenantMenu(@NotBlank(message = "{error.menuId.blank}") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), menuService.getMenuDetail(id, YES));
    }

    @PostMapping("/menu/update")
    @CustomBaseException
    @AuditLog(method = "/api/menu/update", methodDescription = "编辑菜单", httpMethod = "Post")
    @ApiOperation(value = "编辑菜单")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> updateTenantMenu(@RequestBody @Validated UpdateMenuDTO updateMenuDTO) {
        if (!menuService.isMenuExists(updateMenuDTO.getMenuId(), YES)) {
            throw new ValidationException("menuId", "error.menu.notExists");
        }
        if (StringUtils.isNotBlank(updateMenuDTO.getParentId())
                && !menuService.isMenuExists(updateMenuDTO.getParentId(), YES)) {
            throw new ValidationException("parentId", "error.menu.parent.notExists");
        }
        menuService.updateMenu(updateMenuDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/menu/delete")
    @CustomBaseException
    @AuditLog(method = "/api/menu", methodDescription = "删除菜单", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "租户菜单ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "删除菜单")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> deleteTenantMenu(@NotBlank(message = "{error.menuId.blank}") String id) {
        if (!menuService.isMenuExists(id, YES)) {
            throw new ValidationException("id", "error.menu.notExists");
        }
        menuService.deleteMenu(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
}
