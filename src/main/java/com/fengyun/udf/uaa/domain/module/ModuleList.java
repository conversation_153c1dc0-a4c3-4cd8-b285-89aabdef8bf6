package com.fengyun.udf.uaa.domain.module;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* <p>
* 模块列表
* </p>
*
* <AUTHOR>
* @since 2022-01-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("m_module_list")
@ApiModel(value = "ModuleList对象", description = "模块列表")
public class ModuleList extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "uuid")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "父级id")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "模块名")
    @TableField("module_name")
    private String moduleName;

    @ApiModelProperty(value = "模块简介")
    @TableField("module_introduction")
    private String moduleIntroduction;

    @ApiModelProperty(value = "模块类型")
    @TableField("module_type")
    private String moduleType;

    @ApiModelProperty(value = "url")
    @TableField("module_url")
    private String moduleUrl;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "是否展示在导航")
    @TableField("is_show_in_nav")
    private String isShowInNav;
}
