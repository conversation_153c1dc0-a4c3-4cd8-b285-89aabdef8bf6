<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.system.MenuMapper">
	<select id="selectListOrderBySeq" resultType="com.fengyun.udf.uaa.dto.response.system.MenuTreeDTO">
		SELECT ID        AS id,
			   PARENT_ID AS parentId,
			   NAME      AS menuName,
			   TYPE      AS menuType,
			   ICON      AS menuIcon,
			   COMPONENT AS menuComponent,
			   HREF      AS menuHref,
			   SEQ       AS menuSort,
			   PERMISSION AS menuPermission,
			   client_type AS clientType,
		       menu_belong AS menuBelong
		FROM t_menu
		WHERE STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
		  AND IS_VISIBLE = '${@com.fengyun.udf.uaa.constant.Constants@YES}'
		  AND IS_OPERATION = #{isOperation}
		  AND MENU_BELONG = #{menuBelong}
		  AND CLIENT_TYPE = #{clientType}
		ORDER BY SEQ
	</select>

	<select id="selectOperationListOrderBySeq" resultType="com.fengyun.udf.uaa.dto.response.system.MenuTreeDTO">
		SELECT ID        AS id,
		PARENT_ID AS parentId,
		NAME      AS menuName,
		TYPE      AS menuType,
		ICON      AS menuIcon,
		COMPONENT AS menuComponent,
		HREF      AS menuHref,
		SEQ       AS menuSort,
		PERMISSION AS menuPermission
		FROM t_menu
		WHERE STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
		AND IS_VISIBLE = '${@com.fengyun.udf.uaa.constant.Constants@YES}'
		AND IS_OPERATION = #{isOperation}
		AND MENU_BELONG = #{menuBelong}
		ORDER BY SEQ
	</select>

	<select id="selectOperationList" resultType="com.fengyun.udf.uaa.dto.response.system.MenuTreeDTO">
		SELECT
			m.ID AS id,
			m.PARENT_ID AS parentId,
			m. NAME AS menuName,
			m.TYPE AS menuType,
			m.ICON AS menuIcon,
			m.COMPONENT AS menuComponent,
			m.HREF AS menuHref,
			m.SEQ AS menuSort,
			m.PERMISSION AS menuPermission
		FROM
			t_menu m
		JOIN t_role_menu rm ON m.ID = rm.MENU_ID
		JOIN t_role r ON rm.ROLE_ID = r.ID
		WHERE
			m.STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
			AND m.IS_VISIBLE = '${@com.fengyun.udf.uaa.constant.Constants@YES}'
			AND rm.ROLE_ID IN
			<foreach collection="roleIds" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		ORDER BY
			m.SEQ
	</select>

	<select id="selectMenuPermissionByUser" resultType="string">
		SELECT DISTINCT
		m.PERMISSION
		FROM
		t_menu m
		JOIN t_role_menu rm ON m.ID = rm.MENU_ID
		JOIN t_user_role ur ON ur.ROLE_ID = rm.ROLE_ID
		WHERE
		m.STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
		AND m.IS_VISIBLE = '${@com.fengyun.udf.uaa.constant.Constants@YES}'
		AND ur.USER_ID = #{userId}
	</select>
</mapper>
