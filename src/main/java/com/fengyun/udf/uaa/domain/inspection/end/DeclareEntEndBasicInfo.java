package com.fengyun.udf.uaa.domain.inspection.end;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeclareEntEndBasicInfo", description = "项目情况介绍")
@TableName(value = "declare_ent_end_basic_info", autoResultMap = true)
public class DeclareEntEndBasicInfo extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @TableField("status")
    private String status;

    /**
     * 审核意见
     */
    @ApiModelProperty("审核意见")
    @TableField("audit_opinion")
    private String auditOpinion;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 课题名称
     */
    @ApiModelProperty("课题名称")
    @TableField("subject_name")
    private String subjectName;

    /**
     * 申报类别
     */
    @ApiModelProperty("申报类别")
    @TableField("declare_type")
    private String declareType;

    /**
     * 研究内容
     */
    @ApiModelProperty("研究内容")
    @TableField("project_no")
    private String projectNo;

    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    @TableField("company_name")
    private String companyName;

    /**
     * 负责人姓名
     */
    @ApiModelProperty("负责人姓名")
    @TableField("principal_name")
    private String principalName;

    /**
     * 负责人电话
     */
    @ApiModelProperty("负责人电话")
    @TableField("principal_tel")
    private String principalTel;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    @TableField("contact_tel")
    private String contactTel;

    /**
     * 是否企业类项目
     */
    @ApiModelProperty("是否企业类项目")
    @TableField("company_type")
    private Boolean companyType;

}
