spring:
  profiles:
    active: dev
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **************************************************************************************************************************************
    username: root
    password: YSZ@yfb@2018
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  redis:
    database: 2
    host: *************
    password: fengyun123456
    port: 9701


logging:
  level:
    ROOT: INFO
    com.fengyun.udf.uaa: DEBUG

eureka:
  client:
    service-url:
      defaultZone: *************************************/eureka/

cube:
  connect-timeout: 2
  network-timeout: 30
  charset: UTF-8
  http:
    tracker-http-port: 80
    anti-steal-token: no
    secret-key: tetrisDFSPass.!23
  tracker-server: *************:22122
  storage-server: *************
  storage-port: 23000
  file-server-path: http://fastdfs-dev.cnsaas.com/
  file-group: group1

oauth2:
  web-client-configurations:
    - access-token-validity-in-seconds: 302400
      refresh-token-validity-in-seconds-for-remember-me: 604800
      client-id: policy_pc
      secret: fengyun
      client-type: pc
    - access-token-validity-in-seconds: -1
      refresh-token-validity-in-seconds-for-remember-me: -1
      client-id: policy_app
      secret: fengyun
      client-type: app

justauth:
  enabled: true
  extend:
    enum-class: com.fengyun.udf.uaa.config.wechat.AuthMySource
    config:
      WECHAT_MP:
        request-class: com.fengyun.udf.uaa.config.wechat.AuthMyWeChatMpRequest
        client-id: wx60cb4688b2d66384
        client-secret: 9e1fc829afe6b5933296efe1533815a6
        redirect-uri: http://szqyfw-h5.cnsaas.com
  cache:
    type: default

sms:
  sms-client: fykj   # fy:公司默认，ali：阿里云  huyi：互亿 fykj:公司
  fykj:
    uri: https://ifs.ranxinjs.com/sms?action=send
    account: 800020
    password: teFr6e
    extno: 1069030
    signName: 【苏州市生物医药产业创新中心】
  register:
    template: 您的验证码是：{smsCaptchaCode}。请不要把验证码泄露给其他人。
    template-param: smsCaptchaCode
  login:
    template: 登录校验码：{smsCaptchaCode}，此校验码只用于登录您的账户，如非本人操作，请忽略该短信。
    template-param: smsCaptchaCode
  restPassword:
    template: 您的短信验证码是{smsCaptchaCode}。您正在通过手机号重置登录密码，如非本人操作，请忽略该短信。
    template-param: smsCaptchaCode
  registerFail:
    template: 您在国家生物药技术创新中心官网提交的账号注册审核未通过。不通过理由为：{name}
    template-param: name
  registerSuccess:
    template: 您在国家生物药技术创新中心官网的账号注册已审核通过。
  orderWork:
    template: "{userName}您好，租赁仪器使用明细已经生成，请您到国家生物技术创新中心平台（https://www.nctib.org.cn/）-个人空间-明细确认模块中，确认近期使用明细，若超过7个工作日未确认系统将自动确认。另请在15个工作日内付款。若有疑问，请邮箱联系：{emailAddr}"
    template-param:
      - userName
      - emailAddr
  orderBack:
    template: 您预约的仪器使用审核已被退回，请您到国家生物药技术创新平台（https://www.nctib.org.cn/）- 个人中心- 我的预约，查看退回原因并修改后提交。
  orderReject:
    template: 您预约的仪器使用审核不通过，请您到国家生物药技术创新平台（https://www.nctib.org.cn/）- 个人中心 - 我的预约，查看不通过原因并重新预约。
  orderPass:
    template: 您预约的仪器使用审核通过，预约成功，请您按时前往。

wx:
  mp:
    appid: wx60cb4688b2d66384
    secret: 9e1fc829afe6b5933296efe1533815a6
    token:
    aesKey:
  miniapp:
    appid: wxfe365693e8dae476
    secret: 5c6651f4b9f211c2543261d45dfcf98f

cache:
  isInit: true