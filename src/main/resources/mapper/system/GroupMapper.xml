<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.system.GroupMapper">
    <select id="selectListOrderBySeq" resultType="com.fengyun.udf.uaa.dto.response.system.GroupTreeDTO">
		SELECT ID       AS id,
			   PARENT_ID AS parentId,
			   NAME     AS groupName,
			   SEQ       AS groupSort
		FROM t_group
		ORDER BY SEQ
	</select>
</mapper>
