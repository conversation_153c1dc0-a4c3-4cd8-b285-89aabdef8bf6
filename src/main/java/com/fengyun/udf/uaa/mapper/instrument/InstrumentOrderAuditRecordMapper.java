package com.fengyun.udf.uaa.mapper.instrument;

import com.fengyun.udf.uaa.domain.instrument.InstrumentOrderAuditRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderAuditRecordDTO;

/**
 * <p>
 * 仪器预约审核记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
public interface InstrumentOrderAuditRecordMapper extends BaseMapper<InstrumentOrderAuditRecord> {

    InstrumentOrderAuditRecordDTO selectAuditUser(Integer orderId);


}
