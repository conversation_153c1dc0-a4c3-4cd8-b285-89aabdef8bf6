package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntMiddleSuzhouUnitDetailDTO", description = "DeclareEntMiddleSuzhouUnitDetailDTO")
public class DeclareEntMiddleSuzhouUnitDetailDTO extends AbstractDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 是否在苏州工业园区注册公司
     */
    @ApiModelProperty("是否在苏州工业园区注册公司")
    private Boolean sipRegister;


    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    private String responsibleName;

    /**
     * 注册时间
     */
    @ApiModelProperty("注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate registerDate;

    /**
     * 注册资本(万元)
     */
    @ApiModelProperty("注册资本(万元)")
    private String registeredCapital;

    /**
     * 法人代表
     */
    @ApiModelProperty("法人代表")
    private String corporateRepresentative;

    /**
     * 注册地址
     */
    @ApiModelProperty("注册地址")
    private String registerAddress;

    /**
     * 经营地址
     */
    @ApiModelProperty("经营地址")
    private String businessAddress;

    /**
     * 是否在苏州工业园区租赁实地场地
     */
    @ApiModelProperty("是否在苏州工业园区租赁实地场地")
    private Boolean sipLease;

    /**
     * 办公面积m2
     */
    @ApiModelProperty("办公面积m2")
    private Integer officeArea;

    /**
     * 公司人数人
     */
    @ApiModelProperty("公司人数人")
    private Integer employerNum;

    /**
     * 社保缴纳人数人
     */
    @ApiModelProperty("社保缴纳人数人")
    private Integer socialInsurancePeopleNum;

}
