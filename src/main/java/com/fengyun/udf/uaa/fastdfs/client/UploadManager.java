package com.fengyun.udf.uaa.fastdfs.client;

import com.fengyun.udf.security.util.SpringSecurityUtils;
import com.fengyun.udf.uaa.config.CubeProperties;
import com.fengyun.udf.uaa.fastdfs.*;
import com.fengyun.udf.uaa.fastdfs.common.NameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Created by dong on 2017/5/2.
 */
public class UploadManager {

    private static Logger logger = LoggerFactory.getLogger(UploadManager.class);

    static {
        try {
            ClientGlobal.init(SpringSecurityUtils.getBean(CubeProperties.class));
        } catch (Exception e) {
            logger.error("FastFS initial Error", e);
        }
    }

    public static StorageClient getStorageClient() throws Exception {
        TrackerClient trackerClient = new TrackerClient();
        TrackerServer trackerServer = trackerClient.getConnection();
        StorageClient storageClient = new StorageClient(trackerServer, null);
        return storageClient;
    }


    public static String upload(UploadFile file, final String author, String groupName) throws Exception {
        logger.debug("File Name: " + file.getName() + " File Length: " + file.getContent().length);
        NameValuePair[] meta_list = new NameValuePair[1];
        meta_list[0] = new NameValuePair("author", author);
        StorageClient storageClient = getStorageClient();
        long startTime = System.currentTimeMillis();
        String[] uploadResults = null;
        uploadResults = storageClient.upload_file(groupName, file.getContent(), file.getExt(), meta_list);
        logger.debug("upload_file time used: " + (System.currentTimeMillis() - startTime) + " ms");
        if (uploadResults == null) {
            logger.error("upload file fail, error code: " + storageClient.getErrorCode());
        }
        String remoteFileName = null;
        if (uploadResults != null && uploadResults.length >= 2) {
            groupName = uploadResults[0];
            remoteFileName = uploadResults[1];
        }
        logger.debug("upload file successfully!!!  " + "group_name: " + groupName + ", remoteFileName:" + " " + remoteFileName);
        return remoteFileName;
    }

    public static FileInfo getFile(String groupName, String remoteFileName) {
        try {
            return getStorageClient().get_file_info(groupName, remoteFileName);
        } catch (IOException e) {
            logger.error("IO Exception: Get File from Fast DFS failed", e);
        } catch (Exception e) {
            logger.error("Non IO Exception: Get File from Fast DFS failed", e);
        }
        return null;
    }

    public static void deleteFile(String groupName, String remoteFileName) throws Exception {
        int result = getStorageClient().delete_file(groupName, remoteFileName);
        logger.info(groupName + remoteFileName + "文件删除结果:" + result);
    }


}
