package com.fengyun.udf.uaa.mapper.inspection.end;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.inspection.end.DeclareEntEndBasicInfo;
import com.fengyun.udf.uaa.dto.request.inspection.end.DeclareEntEndInspectionQueryDTO;
import com.fengyun.udf.uaa.dto.response.inspection.end.DeclareEntEndBasicInfoListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface DeclareEntEndBasicInfoMapper extends BaseMapper<DeclareEntEndBasicInfo> {


    /**
     * 分页查找中期检查审核列表
     * @param page 分页参数
     * @param query 查询参数
     * @return 审核分页列表
     */
    List<DeclareEntEndBasicInfoListDTO> page(
            Page<DeclareEntEndBasicInfoListDTO> page, @Param("condition") DeclareEntEndInspectionQueryDTO query);

}
