package com.fengyun.udf.uaa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fengyun.udf.cache.redis.client.AreaCacheClient;
import com.fengyun.udf.cache.redis.client.DictCacheClient;
import com.fengyun.udf.cache.redis.client.UserNicknameCacheClient;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.sms.AlicomSMSClient;
import com.fengyun.udf.sms.FykjSMSClient;
import com.fengyun.udf.sms.HuyiSMSClient;
import com.fengyun.udf.sms.SMSClient;
import com.fengyun.udf.sms.util.SendUtil;
import com.fengyun.udf.sms.util.SmsTemplate;
import com.fengyun.udf.uaa.config.EmailProperties;
import com.fengyun.udf.uaa.config.SmsProperties;
import com.fengyun.udf.uaa.constant.Tables;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.EnRegister;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.mapper.system.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.DefaultAuthenticator;
import org.apache.commons.mail.Email;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.SimpleEmail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.fengyun.udf.uaa.constant.Constants.*;
import static java.time.format.DateTimeFormatter.BASIC_ISO_DATE;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Slf4j
@Service
public class BaseService {

    private static final String LIKE_APEEND = "%";

    @Autowired
    protected UserMapper userMapper;

    @Autowired
    protected RoleMapper roleMapper;

    @Autowired
    protected MenuMapper menuMapper;

    @Autowired
    protected UserRoleMapper userRoleMapper;

    @Autowired
    protected RoleMenuMapper roleMenuMapper;

    @Autowired
    protected DictCacheClient dictCacheClient;

    @Autowired
    protected AreaCacheClient areaCacheClient;

    @Autowired
    protected UserNicknameCacheClient nicknameCacheClient;

    @Autowired
    protected EnRegisterMapper enRegisterMapper;

    @Autowired
    protected SmsProperties smsProperties;

    @Autowired
    protected HuyiSMSClient huyiSMSClient;

    @Autowired
    protected AlicomSMSClient alicomSMSClient;

    @Autowired
    private FykjSMSClient fykjSMSClient;

    @Autowired
    protected SMSClient smsClient;

    @Autowired
    private EmailProperties emailProperties;

    public boolean isWebUserExistsByTel(final String tel) {
        List<String> status = new ArrayList<>();
        status.add(STATUS_ACTIVE);
        status.add(STATUS_DISABLE);

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        QueryWrapper<User> queryWrapper = new QueryWrapper<User>()
                .eq(Tables.T_USER.TEL.name(), tel)
                .in(Tables.T_USER.STATUS.name(), status)
                .in(Tables.T_USER.TYPE.name(), userTypes);
        long count = userMapper.selectCount(queryWrapper);
        return count >= 1;
    }

    public boolean isWebEnUserExistsByTel(final String tel, final String code) {
        List<String> status = new ArrayList<>();
        status.add(STATUS_ACTIVE);
        status.add(STATUS_DISABLE);

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        QueryWrapper<User> queryWrapper = new QueryWrapper<User>()
                .eq(Tables.T_USER.TEL.name(), tel)
                .ne(Tables.T_USER.LOGIN_NAME.name(), code)
                .in(Tables.T_USER.STATUS.name(), status)
                .in(Tables.T_USER.TYPE.name(), userTypes);
        long count = userMapper.selectCount(queryWrapper);
        return count >= 1;
    }

    public boolean isWebUserExistsByLoginName(final String loginName) {
        List<String> status = new ArrayList<>();
        status.add(STATUS_ACTIVE);
        status.add(STATUS_DISABLE);

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        QueryWrapper<User> queryWrapper = new QueryWrapper<User>()
                .eq(Tables.T_USER.LOGIN_NAME.name(), loginName)
                .in(Tables.T_USER.STATUS.name(), status)
                .in(Tables.T_USER.TYPE.name(), userTypes);
        long count = userMapper.selectCount(queryWrapper);
        return count >= 1;
    }

    public boolean isWebUserExistsByCode(final String creditCode) {
        long int1 = userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getLoginName, creditCode));
        long int2 = enRegisterMapper.selectCount(new LambdaQueryWrapper<EnRegister>().eq(EnRegister::getCreditCode, creditCode));
        return int1 >= 1 || int2 >= 1;
    }

    public static String format(ZonedDateTime dateTime, String format) {
        if (dateTime == null || format == null) {
            return null;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format).withZone(ZoneId.of("Asia/Shanghai"));
        return dateTimeFormatter.format(dateTime);
    }

    public void jsonStrformatCheck(String jsonStr) {
        try {
            JSONObject.parseObject(jsonStr);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ValidationException("error", "JSON格式不正确");
        }

    }

    protected String prepareLikeContent(String content) {
        return LIKE_APEEND + content + LIKE_APEEND;
    }

    protected LocalDateTime parseLocalDateTime(final String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter dateTimeFormatter = BASIC_ISO_DATE;
        if (StringUtils.isNotBlank(format)) {
            dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        }
        return LocalDateTime.parse(dateStr, dateTimeFormatter);
    }

    protected LocalDateTime parseLocalDateTime(final String dateStr, final boolean isStart) {
        return parseLocalDateTime(dateStr, isStart, "yyyy-MM-dd");
    }

    protected LocalDateTime parseLocalDateTime(final String dateStr, final boolean isStart, String format) {
        java.time.LocalDateTime dateTime = null;
        if (StringUtils.isNotBlank(dateStr)) {
            DateTimeFormatter dateTimeFormatter = BASIC_ISO_DATE;
            if (StringUtils.isNotBlank(format)) {
                dateTimeFormatter = DateTimeFormatter.ofPattern(format);
            }
            LocalDate date = LocalDate.parse(dateStr, dateTimeFormatter);
            if (isStart) {
                dateTime = date.atTime(0, 0, 0);
            } else {
                dateTime = date.atTime(23, 59, 59);
            }
        }
        return dateTime;
    }

    protected ZonedDateTime parseZonedDateTime(String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, dateTimeFormatter);
        ZonedDateTime z = ZonedDateTime.of(dateTime, ZoneId.of("Asia/Shanghai"));

        return z;
    }

    protected void checkCurrentUser(String createdId) {
        if (StringUtils.isEmpty(createdId) || !createdId.equals(SecurityUtils.getUserId())) {
            throw new ValidationException("error", "非本人操作");
        }
    }

    protected <T> List<T> parseArray(String text, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(text)) {
            try {
                result = JSON.parseArray(text, clazz);
            } catch (Exception ignored) {
            }
        }

        return result;
    }

    protected String toJSONString(Object o) {
        try {
            return JSON.toJSONString(o);
        } catch (Exception ignored) {
        }

        return null;
    }

    protected <T> T parseObject(String text, Class<T> clazz) {
        try {
            return JSON.parseObject(text, clazz);
        } catch (Exception ignored) {
        }

        return null;
    }

    protected List<String> splitParam(String param) {
        if (StringUtils.isNotEmpty(param)) {
            String[] params = param.split(",");
            List<String> levelList = new ArrayList<>();
            Collections.addAll(levelList, params);

            return levelList;
        }

        return null;
    }

    /**
     * 租户管理用户类型
     *
     * @return
     */
    public List<String> prepareTenantAdminUserType() {
        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.tenant.name());
        return userTypes;
    }

    /**
     * 租户平台用户类型
     *
     * @return
     */
    public List<String> prepareTenantMemberUserType() {
        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());
        return userTypes;
    }

    /**
     * 租户用户类型
     *
     * @return
     */
    public List<String> prepareTenantUserType() {
        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.tenant.name());
        return userTypes;
    }

    /**
     * 运营平台用户类型
     *
     * @return
     */
    public List<String> prepareOperationUserType() {
        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.management.name());
        return userTypes;
    }

    protected void cacheNickname(String userId, String loginName, String nickname) {
        nicknameCacheClient.add(userId, StringUtils.isNotBlank(nickname) ? nickname : loginName);
    }

    protected boolean notNull(String str) {
        return StringUtils.isNotEmpty(str);
    }

    public String getCurrentRole() {
        List<String> roleList = roleMapper.selectRoleByUser(SecurityUtils.getUserId());
        if (CollectionUtils.isNotEmpty(roleList)) {
            return roleList.get(0);
        } else {
            return "";
        }
    }

    @Async
    public void sendSms(String tel, Map<String, String> paramMap, SmsTemplate template) throws Exception {
        if (template != null) {
            //发送短信
            switch (smsProperties.getSmsClient()) {
                case FY:
                    SendUtil.sendSms(tel, template, paramMap, smsClient);
                    break;
                case ALI:
                    SendUtil.sendSms(tel, template, paramMap, alicomSMSClient);
                    break;
                case HUYI:
                    SendUtil.sendSms(tel, template, paramMap, huyiSMSClient);
                    break;
                case FYKJ:
                    SendUtil.sendSms(tel, template, paramMap, fykjSMSClient);
                    break;
            }

        }
    }

    public void sendMail(String mail, String content, String subject) throws EmailException {
        Email email = new SimpleEmail();
        email.setHostName(emailProperties.getHost());
        email.setSmtpPort(emailProperties.getPort());
        email.setStartTLSEnabled(true);
        email.setAuthenticator(new DefaultAuthenticator(emailProperties.getUsername(), emailProperties.getPassword()));
        email.setSSLOnConnect(false);
        email.setDebug(false);
        email.setCharset("UTF-8");
        email.setFrom(emailProperties.getUsername(), emailProperties.getNickname());
        email.addTo(mail);
        email.setSubject(subject);
        email.setMsg(content);
        email.send();
    }

}
