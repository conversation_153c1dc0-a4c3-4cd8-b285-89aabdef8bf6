<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.instrument.InstrumentMapper">

    <sql id="pageCondition">
        <if test="condition.name != null and condition.name != ''">
            and name like concat('%',#{condition.name},'%')
        </if>
        <if test="condition.num != null and condition.num != ''">
            and num like concat('%',#{condition.num},'%')
        </if>
        <if test="condition.model != null and condition.model != ''">
            and model like concat('%',#{condition.model},'%')
        </if>
        <if test="condition.factory != null and condition.factory != ''">
            and factory like concat('%',#{condition.factory},'%')
        </if>
        <if test="condition.address != null and condition.address != ''">
            and address like concat('%',#{condition.address},'%')
        </if>
        <if test="condition.status != null and condition.status != ''">
            and status = #{condition.status}
        </if>
        <if test="condition.instrumentArea != null and condition.instrumentArea != ''">
            and instrument_area = #{condition.instrumentArea}
        </if>
        <if test="condition.keyword != null and condition.keyword != ''">
            and (name like concat('%',#{condition.keyword},'%')
            or num like concat('%',#{condition.keyword},'%')
            or model like concat('%',#{condition.keyword},'%')
            or factory like concat('%',#{condition.keyword},'%')
            or address like concat('%',#{condition.keyword},'%')
            )
        </if>
        <if test="condition.isAllDay != null ">
            and is_all_day = #{condition.isAllDay}
        </if>
    </sql>

    <select id="selectPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentListDTO">
        select
        id, name, num, model, factory, address, status,is_all_day,
        person_num,lease_price_str,test_price_str,remark,tec_content,feature_content,care_content,instrument_area
        from
        in_instrument
        where deleted = 0
        <include refid="pageCondition"></include>
        order by sort,CREATED_DATE desc
    </select>
    <select id="selectShowPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentShowListDTO">
        select
        id, name, num, model, factory, address, status,image,instrument_area,
        person_num
        from
        in_instrument
        where deleted = 0
        <include refid="pageCondition"></include>
        order by sort,CREATED_DATE desc
    </select>
</mapper>
