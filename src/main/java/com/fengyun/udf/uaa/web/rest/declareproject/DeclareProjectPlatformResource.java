package com.fengyun.udf.uaa.web.rest.declareproject;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declareproject.*;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntListDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareProjectShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractListDTO;
import com.fengyun.udf.uaa.dto.response.module.SaveContentDto;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import com.fengyun.udf.uaa.service.declareproject.DeclareProjectService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;
import java.util.Map;


@Api(value = "申报中心管理运营端", tags = "申报中心管理运营端")
@RestController
@RequestMapping({"/api/platform/declare"})
@Validated
@RequiredArgsConstructor
@PreAuthorize("@pms.hasPermission('management')")
public class DeclareProjectPlatformResource extends BaseResource {

    private final DeclareProjectService declareProjectService;

    private final DeclareEntService declareEntService;


    @GetMapping("/page")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/page", methodDescription = "申报项目列表", httpMethod = "GET")
    @ApiOperation(value = "申报项目列表")
    public ResponseEntity<ReturnResultDTO<DeclareProjectShowDTO>> getDeclareProjectList(Page page,
                                                                                        QueryDeclareProjectDTO
                                                                                                declareStatus) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.page(page, declareStatus));
    }

    @PostMapping({"/add"})
    @CustomBaseException
    @ApiOperation(value = "新增申报政策")
    @AuditLog(method = "/api/platform/declare/add", methodDescription = "新增申报政策", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> add(@RequestBody CreateDeclareProjectDTO dto) throws Exception {
        declareProjectService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/addandsubmit"})
    @CustomBaseException
    @ApiOperation(value = "新增申报政策")
    @AuditLog(method = "/api/platform/declare/addandsubmit", methodDescription = "新增并提交申报政策", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> addAndSubmit(@RequestBody CreateDeclareProjectDTO dto) throws Exception {
        declareProjectService.addAndSubmit(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/update"})
    @CustomBaseException
    @ApiOperation(value = "更新申报政策")
    @AuditLog(method = "/api/platform/declare/update", methodDescription = "更新申报政策", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody CreateDeclareProjectDTO dto) throws Exception {
        declareProjectService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/updateandsubmit"})
    @CustomBaseException
    @ApiOperation(value = "更新并提交申报政策")
    @AuditLog(method = "/api/platform/declare/updateandsubmit", methodDescription = "更新并提交申报政策", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> updateAndSubmit(@RequestBody CreateDeclareProjectDTO dto) throws Exception {
        declareProjectService.updateAndSubmit(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/get/{id}"})
    @ApiOperation(value = "根据id查询申报政策详情")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> get(@PathVariable("id") String id) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getById(id));
    }

    @PostMapping({"/delete/{id}"})
    @ApiOperation(value = "删除申报政策")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> delete(@PathVariable String id) throws Exception {
        declareProjectService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/list"})
    @ApiOperation(value = "查询关联政策")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> getDeclareList() throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getDeclareList());
    }

    @PostMapping({"/up/status/{id}"})
    @ApiOperation(value = "上架申报政策")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> up(@PathVariable String id) throws Exception {
        declareProjectService.up(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/down/status/{id}"})
    @ApiOperation(value = "下架申报政策")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> down(@PathVariable String id) throws Exception {
        declareProjectService.down(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/top/{id}"})
    @ApiOperation(value = "置顶申报政策")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> top(@PathVariable String id) throws Exception {
        declareProjectService.top(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/canceltop/{id}"})
    @ApiOperation(value = "取消置顶申报政策")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> cancelTop(@PathVariable String id) throws Exception {
        declareProjectService.cancelTop(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/publish/{id}"})
    @ApiOperation(value = "发布公示")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> publish(@PathVariable String id, @RequestBody SaveContentDto dto) throws Exception {
        declareProjectService.publish(id, dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/establish/{id}"})
    @ApiOperation(value = "项目立项")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> establish(@PathVariable String id) throws Exception {
        declareProjectService.establish(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
    @PostMapping("/import/review")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/import/review", methodDescription = "导入第一轮评审结果", httpMethod = "POST")
    @ApiOperation(value = "导入第一轮评审结果")
    public ResponseEntity<ReturnResultDTO> importReviewRecord(String id,String type, MultipartFile file) throws IOException {
        List<Map<String, String>> errorList = declareProjectService.importReviewDTO(id, type, file.getInputStream());
        if(errorList.size() > 0){
            return prepareReturnResult(ReturnCode.VALIDATION_ERROR.getCode(), errorList);
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/ent/page")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/page", methodDescription = "立项项目企业列表", httpMethod = "POST")
    @ApiOperation(value = "立项项目企业列表")
    public ResponseEntity<ReturnResultDTO<Page<DeclareEntListDTO>>> reviewDeclareEntPage(Page<DeclareEntListDTO> page,
                                                          @ModelAttribute @Validated QueryDeclareEntDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.getSubProjectPage(page, dto));
    }

    @PostMapping("/ent/review")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/review", methodDescription = "评审企业申报项目", httpMethod = "POST")
    @ApiOperation(value = "评审企业申报项目")
    public ResponseEntity<ReturnResultDTO<?>> reviewEnt(@Validated @RequestBody DeclareEntHandlerDTO dto) {
        declareEntService.finalReview(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/ent/contract/audit")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/contract/audit", methodDescription = "合同审核", httpMethod = "POST")
    @ApiOperation(value = "合同审核")
    public ResponseEntity<ReturnResultDTO<?>> auditContract(@Validated @RequestBody DeclareEntHandlerDTO dto) {
        declareEntService.auditContract(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/ent/contract/detail")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/contract/detail", methodDescription = "合同详情", httpMethod = "POST")
    @ApiOperation(value = "合同详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectContractDetailDTO>> contractDetail(
            @Validated @NotBlank(message = "项目id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.contractDetail(id));
    }

    @GetMapping("/ent/contract/page")
    @CustomBaseException
    @AuditLog(method = "/api/platform/declare/ent/contract/page", methodDescription = "合同汇总列表", httpMethod = "POST")
    @ApiOperation(value = "合同汇总列表")
    public ResponseEntity<ReturnResultDTO<Page<ProjectContractListDTO>>> contractPage(
            Page<ProjectContractListDTO> page,
            @ModelAttribute QueryContractDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.contractPage(page, dto));
    }

}
