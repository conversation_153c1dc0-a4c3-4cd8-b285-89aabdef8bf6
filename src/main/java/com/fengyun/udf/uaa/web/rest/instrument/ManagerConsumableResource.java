package com.fengyun.udf.uaa.web.rest.instrument;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;

import com.fengyun.udf.uaa.dto.request.instrument.ConsumableCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.ConsumableUpdateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.ConsumableQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.ConsumableListDTO;
import com.fengyun.udf.uaa.dto.response.instrument.ConsumableDetailDTO;
import com.fengyun.udf.uaa.service.instrument.ConsumableService;
import org.springframework.web.bind.annotation.RestController;
import com.fengyun.udf.resource.BaseResource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-10-11
 */

@RestController
@RequestMapping("/api/manager/consumable")
@Api(value = "管理端-耗材", tags = "管理端-耗材")
@Validated
@Slf4j
@AllArgsConstructor
@PreAuthorize("@pms.hasPermission('management')")
public class ManagerConsumableResource extends BaseResource {
    private final ConsumableService consumableService;

    @PostMapping("/new")
    @CustomBaseException
    @AuditLog(method = "/api/manager/consumable/new", methodDescription = "添加耗材", httpMethod = "POST")
    @ApiOperation(value = "添加耗材")
    public ResponseEntity<ReturnResultDTO> create(@RequestBody @Validated ConsumableCreateDTO dto) {
        consumableService.create(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/edit")
    @CustomBaseException
    @AuditLog(method = "/api/manager/consumable/edit", methodDescription = "修改耗材", httpMethod = "POST")
    @ApiOperation(value = "修改耗材")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody @Validated ConsumableUpdateDTO dto) {
        consumableService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/manager/consumable/view", methodDescription = "耗材详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "耗材ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "耗材详情")
    public ResponseEntity<ReturnResultDTO<ConsumableDetailDTO>> detail(@NotBlank(message = "id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), consumableService.detail(id));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/manager/consumable", methodDescription = "耗材列表", httpMethod = "GET")
    @ApiOperation(value = "耗材列表")
    public ResponseEntity<ReturnResultDTO<Page<ConsumableListDTO>>> page(Page page, ConsumableQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), consumableService.page(page, dto));
    }

    @GetMapping("/delete")
    @CustomBaseException
    @AuditLog(method = "/api/manager/consumable/delete", methodDescription = "耗材删除", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "耗材ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "耗材删除")
    public ResponseEntity<ReturnResultDTO> delete(@NotBlank(message = "id不能为空") String id) {
        consumableService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/status")
    @CustomBaseException
    @AuditLog(method = "/api/manager/consumable/status", methodDescription = "耗材上下架", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "耗材ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "耗材上下架")
    public ResponseEntity<ReturnResultDTO> status(@NotBlank(message = "id不能为空") String id) {
        consumableService.status(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }


}
