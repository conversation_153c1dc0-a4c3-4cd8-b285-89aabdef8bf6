package com.fengyun.udf.uaa.dto.response.inspection.end;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntEndAchievementOtherDetailDTO", description = "DeclareEntEndAchievementOtherDetailDTO")
public class DeclareEntEndAchievementOtherDetailDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 获得时间
     */
    @ApiModelProperty("获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", locale = "zh")
    private LocalDate acquireDate;

    /**
     * 成果概述
     */
    @ApiModelProperty("成果概述")
    private String description;
}
