package com.fengyun.udf.uaa.service.log;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.feign.dto.FeignAuditLogDTO;
import com.fengyun.udf.uaa.domain.log.AuditLog;
import com.fengyun.udf.uaa.dto.response.log.AuditLogDTO;
import com.fengyun.udf.uaa.mapper.log.AuditLogMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by wangg on 2019/7/15.
 */
@Service
@Transactional
public class AuditLogService extends BaseService {

    @Autowired
    private AuditLogMapper auditLogMapper;

    /**
     * 添加访问日志
     *
     * @param feignAuditLogDTO
     */
    public void addLog(FeignAuditLogDTO feignAuditLogDTO) {
        AuditLog auditLog = new AuditLog();
        BeanUtils.copyProperties(feignAuditLogDTO, auditLog);
        auditLog.setId(UUIDGenerator.getUUID());
        auditLogMapper.insert(auditLog);
    }

    /**
     * 查询访问日志列表
     *
     * @param page
     * @param status
     * @param methodDescription
     * @return
     */
    public Page<AuditLogDTO> findLogs(Page page, Integer status, String methodDescription, String tenantId) {
        if (StringUtils.isNotBlank(methodDescription)) {
            prepareLikeContent(methodDescription);
        }
        List<AuditLogDTO> list = auditLogMapper.selectLogs(page, status, methodDescription, tenantId);
        page.setRecords(list);
        return page;
    }
}
