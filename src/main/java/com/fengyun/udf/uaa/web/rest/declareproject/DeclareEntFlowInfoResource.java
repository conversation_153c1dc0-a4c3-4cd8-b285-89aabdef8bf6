package com.fengyun.udf.uaa.web.rest.declareproject;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "流程信息请求地址（无需登录）", tags = "流程信息请求地址(无需登录)")
@RestController
@RequestMapping({"/api/ua/declare/flow"})
@Validated
public class DeclareEntFlowInfoResource extends BaseResource {


    @Autowired
    private DeclareEntService declareEntService;


    @GetMapping("/info")
    @CustomBaseException
    @AuditLog(method = "/api/ua/declare/flow/info", methodDescription = "流程处理-信息展示", httpMethod = "GET")
    @ApiOperation(value = "流程处理-信息展示")
    public ResponseEntity<ReturnResultDTO<DeclareEntShowDTO>> getDeclareProjectList(String declareEntId, String
            dateTime) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.getDetail(declareEntId, dateTime));
    }
}

