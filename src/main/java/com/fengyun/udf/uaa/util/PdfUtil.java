package com.fengyun.udf.uaa.util;

import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;

/**
 * 2022/9/21
 * Pdf工具类
 * <AUTHOR>
 * @version 1.0.0
 */
public class PdfUtil {

    /**
     * 合并PDF
     * @param files 文件列表
     * @param targetPath 生成路径
     * @return 合并结果文件
     * @throws Exception 异常
     */
    public static File merge(List<File> files, String targetPath) throws Exception {
        PDFMergerUtility mergePdf = new PDFMergerUtility();
        for (File f : files) {
            if(f.exists() && f.isFile()){
                // 循环添加要合并的pdf
                mergePdf.addSource(f);
            }
        }
        // 设置合并生成pdf文件名称
        mergePdf.setDestinationFileName(targetPath);
        // 合并pdf
        mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
        return new File(targetPath);
    }

    /**
     * 给文件添加水印(整页水印
     *
     * @param tempFile 需要添加水印的文件
     * @param waterMark 水印文字
     * @param fontSize 字体大小
     * @param color 字体颜色：{r, g, b, 透明度}
     * @param rowSpace 行间距，大中小分别对应150/100/50
     * @param colSpace 列间距，大中小分别对应150/100/50
     * @param theta 水印倾斜度
     * @throws IOException 异常
     */
    public static void addWaterMark(File tempFile, String waterMark, float fontSize, float[] color,
                                    int rowSpace, int colSpace, float theta) throws IOException {
        // 加载PDF文件
        PDDocument document = PDDocument.load(tempFile);
        document.setAllSecurityToBeRemoved(true);

        // 遍历PDF文件，在每一页加上水印
        for (PDPage page : document.getPages()) {
            PDPageContentStream stream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true);

            // 加载水印字体
            PDFont font = PDType0Font.load(document, Files.newInputStream(new ClassPathResource("static/font/Alimama_ShuHeiTi_Bold.ttf").getFile().toPath()), true);

            PDExtendedGraphicsState r = new PDExtendedGraphicsState();

            // 设置透明度
            r.setNonStrokingAlphaConstant(0.1f);
            r.setAlphaSourceFlag(true);
            stream.setGraphicsStateParameters(r);

            // 设置水印字体颜色
            if (!Objects.isNull(color) && color.length == 3) {
                stream.setNonStrokingColor(color[0], color[1], color[2]);
            }
            stream.beginText();
            stream.setFont(font, fontSize);
            stream.newLineAtOffset(0, -15);

            // 获取PDF页面大小
            float pageHeight = page.getMediaBox().getHeight();
            float pageWidth = page.getMediaBox().getWidth();

            // 根据纸张大小添加水印，30度倾斜
            if(rowSpace == 0 && colSpace == 0){
                // 单个水印(居中
                stream.setTextMatrix(Matrix.getRotateInstance(
                        theta,
                        (pageWidth-fontSize*waterMark.length()*(float)Math.cos(theta))/2,
                        (pageHeight-fontSize*(float)Math.sin(theta))/2));
                stream.showText(waterMark);
            } else {
                // 满页水印
                for (int h = 10; h < pageHeight; h = h + rowSpace) {
                    for (int w = -10; w < pageWidth; w = w + colSpace) {
                        stream.setTextMatrix(Matrix.getRotateInstance(0.3, w, h));
                        stream.showText(waterMark);
                    }
                }
            }


            // 结束渲染，关闭流
            stream.endText();
            stream.restoreGraphicsState();
            stream.close();
        }
        document.save(tempFile);
    }

    /**
     * 给文件添加水印(整页水印
     *
     * @param tempFile 需要添加水印的文件
     * @param waterMark 水印文字
     * @param fontSize 字体大小
     * @param color 字体颜色：{r, g, b, 透明度}
     * @throws IOException 异常
     */
    public static void addWaterMark(File tempFile, String waterMark, float fontSize, float[] color) throws IOException {
        addWaterMark(tempFile, waterMark, fontSize, color, 0, 0, 0.3f);
    }

    /**
     * 给文件添加水印(整页水印,默认样式
     *
     * @param tempFile 需要添加水印的文件
     * @param waterMark 水印文字
     * @throws IOException 异常
     */
    public static void addWaterMark(File tempFile, String waterMark) throws IOException {
        addWaterMark(tempFile, waterMark, 64, null, 0, 0, 0.3f);
    }

    /**
     * 复制文件
     *
     * @param oldFile 源文件
     * @param newFile 目标文件
     */
    public static void copy(File oldFile, File newFile){

        try (InputStream in = Files.newInputStream(oldFile.toPath());
             OutputStream out = Files.newOutputStream(newFile.toPath())) {

            byte[] arr = new byte[1024];
            int len;
            while ((len = in.read( arr )) != -1) {
                out.write ( arr, 0, len );
            }

        }catch (Exception e) {
            e.printStackTrace ();
        }

    }

    /**
     * 复制流
     *
     * @param in 输入流
     * @param out 输出流
     */
    public static void copyStream(InputStream in, OutputStream out) {
        try {
            byte[] arr = new byte[1024];
            int len;
            while ((len = in.read( arr )) != -1) {
                out.write ( arr, 0, len );
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
