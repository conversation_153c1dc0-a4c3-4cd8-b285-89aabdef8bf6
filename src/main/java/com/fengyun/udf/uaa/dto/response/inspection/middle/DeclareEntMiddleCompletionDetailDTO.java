package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.uaa.domain.inspection.middle.StageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntMiddleCompletionDetailDTO", description = "DeclareEntMiddleCompletionDetailDTO")
public class DeclareEntMiddleCompletionDetailDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 第一阶段时间
     */
    @ApiModelProperty("第一阶段时间")
    private String firstStageDate;

    @ApiModelProperty("第一阶段信息列表")
    private List<StageInfo> firstStageInfo;
    /**
     * 第一阶段全部完成
     */
    @ApiModelProperty("第一阶段全部完成")
    private Boolean firstAllCompleted;

    /**
     * 第一阶段没有全部完成的原因
     */
    @ApiModelProperty("第一阶段没有全部完成的原因")
    private String firstIncompleteReason;

    /**
     * 第二阶段时间
     */
    @ApiModelProperty("第二阶段时间")
    private String secondStageDate;

    @ApiModelProperty("第二阶段信息列表")
    private List<StageInfo> secondStageInfo;

    /**
     * 第二阶段全部完成
     */
    @ApiModelProperty("第二阶段全部完成")
    private Boolean secondAllCompleted;

    /**
     * 第二阶段没有全部完成的原因
     */
    @ApiModelProperty("第二阶段没有全部完成的原因")
    private String secondIncompleteReason;

    @ApiModelProperty("重大进展")
    private String majorProgress;

    @ApiModelProperty("重大奖项")
    private String majorAwards;

    @ApiModelProperty("与行业龙头企业建立合作情况")
    private String industryLeaderCooperation;

    @ApiModelProperty("知识产权交易或转化" +
            "(如产生知识产权交易或转化，请补充说明转让技术、交易对象、交易金额等)")
    private String ipTransaction;

    @ApiModelProperty("未标明受到国创中心资助原因")
    private String noNctibSupportReason;

}
