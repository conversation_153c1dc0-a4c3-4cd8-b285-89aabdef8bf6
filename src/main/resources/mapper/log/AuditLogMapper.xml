<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.log.AuditLogMapper">
    <select id="selectLogs" resultType="com.fengyun.udf.uaa.dto.response.log.AuditLogDTO">
        SELECT
            t.`NAME` AS tenantName,
            al.METHOD_DESCRIPTION AS methodDescription,
            al.REQUEST_IP AS requestIp,
            al.HTTP_METHOD AS httpMethod,
            u.NICK_NAME AS createdUser,
            al.REQUEST_DURATION AS requestDuration,
            al.CREATED_DATE AS createdDate,
            al.`STATUS` AS status,
            al.EXCEPTION_CONTENT AS exceptionContent
        FROM
            t_audit_log al
        LEFT JOIN t_user u ON u.ID = al.CREATED_ID
        LEFT JOIN t_tenant t ON t.id = al.TENANT_ID
        WHERE 1=1
        <if test="tenantId != null and tenantId != '' ">
            and al.TENANT_ID = #{tenantId}
        </if>
        <if test="status != null and status != '' or status == 0 ">
            and al.STATUS = #{status}
        </if>
        <if test="methodDescription != null and methodDescription != '' ">
            and al.METHOD_DESCRIPTION like #{methodDescription}
        </if>
        ORDER BY
            al.CREATED_DATE DESC
    </select>

    <select id="selectLoginStatistics" parameterType="com.fengyun.udf.uaa.dto.request.statistics.QueryStatisticsOverallDTO" resultType="map">
        SELECT
            METHOD_API as api,
            count(*) as count
        FROM
            `t_audit_log`
        WHERE
            `METHOD_API` IN (
                '/login',
                '/login/wechat-open/login',
                '/login/wechat-open/registry'
            )
        AND `STATUS` = 1
        <if test="createDateBegin != null">
            AND CREATED_DATE &gt;= #{createDateBegin}
        </if>
        <if test="createDateEnd != null">
            AND CREATED_DATE  &lt;= #{createDateEnd}
        </if>
        GROUP BY
            METHOD_API
    </select>

</mapper>
