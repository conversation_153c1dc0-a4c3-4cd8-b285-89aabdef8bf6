package com.fengyun.udf.uaa.service.instrument;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.cache.redis.client.DictCacheClient;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.instrument.InstrumentOrderDetailConfirm;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmListDTO;
import com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderDetailConfirmMapper;
import com.fengyun.udf.uaa.service.BaseService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仪器预约明细确认 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class InstrumentOrderDetailConfirmService extends BaseService {

    private final InstrumentOrderDetailConfirmMapper instrumentOrderDetailConfirmMapper;

    private final DictCacheClient dictCacheClient;

    public void create(InstrumentOrderDetailConfirmCreateDTO dto) {
        InstrumentOrderDetailConfirm instrumentOrderDetailConfirm = new InstrumentOrderDetailConfirm();
        BeanUtils.copyProperties(dto, instrumentOrderDetailConfirm);
        if (instrumentOrderDetailConfirm.getIsSend()) {
            instrumentOrderDetailConfirm.setSendTime(LocalDateTime.now());

            send(instrumentOrderDetailConfirm);
        }
        instrumentOrderDetailConfirm.setIsConfirm(false);
        instrumentOrderDetailConfirm.create(SecurityUtils.getUserId());

        instrumentOrderDetailConfirmMapper.insert(instrumentOrderDetailConfirm);
    }

    public void update(InstrumentOrderDetailConfirmUpdateDTO dto) {
        InstrumentOrderDetailConfirm instrumentOrderDetailConfirm = instrumentOrderDetailConfirmMapper.selectById(dto.getId());
        if (instrumentOrderDetailConfirm == null) {
            throw new ValidationException("error", "不存在");
        }
        if (instrumentOrderDetailConfirm.getIsSend()) {
            throw new ValidationException("error", "已发送无法编辑");
        }
        BeanUtils.copyProperties(dto, instrumentOrderDetailConfirm);
        if (instrumentOrderDetailConfirm.getIsSend()) {
            instrumentOrderDetailConfirm.setSendTime(LocalDateTime.now());

            send(instrumentOrderDetailConfirm);
        }

        instrumentOrderDetailConfirm.update(SecurityUtils.getUserId());
        instrumentOrderDetailConfirmMapper.updateById(instrumentOrderDetailConfirm);
    }

    public void resend(String ids,String type){
        for (String s : ids.split(",")) {
            InstrumentOrderDetailConfirm confirm = instrumentOrderDetailConfirmMapper.selectById(Integer.parseInt(s));
            if(confirm != null){
                if("email".equals(type)){
                    confirm.setContactTel(null);
                } else if ("sms".equals(type)) {
                    confirm.setContactEmail(null);
                }

                send(confirm);
            }
        }
    }

    public void send(InstrumentOrderDetailConfirm instrumentOrderDetailConfirm){
        String email = dictCacheClient.getDict("INSTRUMENT_ORDER_DETAIL_CONFIRM_TEL","email");
        if(StringUtils.isBlank(email)){
            email = Constants.CONTACT_EMAIL;
        }
        //短信
        if(StringUtils.isNotBlank(instrumentOrderDetailConfirm.getContactTel())){
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("emailAddr", email);
            paramMap.put("userName", instrumentOrderDetailConfirm.getUserName());
            try {
                sendSms(instrumentOrderDetailConfirm.getContactTel(), paramMap, smsProperties.getOrderWork());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        //邮箱
        if(StringUtils.isNotBlank(instrumentOrderDetailConfirm.getContactEmail())){
            String mailContent = "%s您好，租赁仪器使用明细已经生成，请您到国家生物技术创新中心平台（https://www.nctib.org.cn/）-个人空间-明细确认模块中，确认近期使用明细，若超过7个工作日未确认系统将自动确认。另请在15个工作日内付款。若有疑问，请邮箱联系：%s。";
            mailContent = String.format(mailContent, instrumentOrderDetailConfirm.getUserName(), email);
            try {
                sendMail(instrumentOrderDetailConfirm.getContactEmail(), mailContent, "明细确认");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public InstrumentOrderDetailConfirmDetailDTO detail(Integer id) {
        InstrumentOrderDetailConfirmDetailDTO dto = new InstrumentOrderDetailConfirmDetailDTO();
        InstrumentOrderDetailConfirm instrumentOrderDetailConfirm = instrumentOrderDetailConfirmMapper.selectById(id);
        if (instrumentOrderDetailConfirm == null) {
            throw new ValidationException("error", "不存在");
        }
        BeanUtils.copyProperties(instrumentOrderDetailConfirm, dto);
        return dto;
    }

    public Page<InstrumentOrderDetailConfirmListDTO> page(Page page, InstrumentOrderDetailConfirmQueryDTO dto) {
        List<InstrumentOrderDetailConfirmListDTO> list = instrumentOrderDetailConfirmMapper.selectPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public void delete(Integer id) {
        InstrumentOrderDetailConfirm instrumentOrderDetailConfirm = instrumentOrderDetailConfirmMapper.selectById(id);
        if (instrumentOrderDetailConfirm == null) {
            throw new ValidationException("error", "不存在");
        }
        if (instrumentOrderDetailConfirm.getIsSend()) {
            throw new ValidationException("error", "已发送无法删除");
        }
        instrumentOrderDetailConfirmMapper.deleteById(id);
    }

    public void confirm(Integer id) {
        InstrumentOrderDetailConfirm instrumentOrderDetailConfirm = instrumentOrderDetailConfirmMapper.selectById(id);
        if (instrumentOrderDetailConfirm == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!instrumentOrderDetailConfirm.getIsSend()) {
            throw new ValidationException("error", "无法确认");
        }
        if (instrumentOrderDetailConfirm.getIsConfirm()) {
            throw new ValidationException("error", "已确认");
        }
        instrumentOrderDetailConfirm.setIsConfirm(true);
        instrumentOrderDetailConfirm.setConfirmTime(LocalDateTime.now());

        instrumentOrderDetailConfirm.update(SecurityUtils.getUserId());
        instrumentOrderDetailConfirmMapper.updateById(instrumentOrderDetailConfirm);
    }

    public void autoConfirm() {
        List<InstrumentOrderDetailConfirm> list = instrumentOrderDetailConfirmMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDetailConfirm>()
            .eq(InstrumentOrderDetailConfirm::getIsSend,true)
                .eq(InstrumentOrderDetailConfirm::getIsConfirm,false)
                .lt(InstrumentOrderDetailConfirm::getSendTime,LocalDateTime.now().plusDays(-9))); //7个工作日自动确认，默认改为9天

        if(CollectionUtils.isNotEmpty(list)){
            for (InstrumentOrderDetailConfirm instrumentOrderDetailConfirm : list) {
                instrumentOrderDetailConfirm.setIsConfirm(true);
                instrumentOrderDetailConfirm.setConfirmTime(LocalDateTime.now());

                instrumentOrderDetailConfirm.update("auto");
                instrumentOrderDetailConfirmMapper.updateById(instrumentOrderDetailConfirm);
            }
        }

    }


}

