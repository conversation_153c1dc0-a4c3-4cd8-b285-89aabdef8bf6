package com.fengyun.udf.uaa.domain.inspection.end;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "附件上传表单")
@TableName(value = "declare_ent_end_attachment", autoResultMap = true)
public class DeclareEntEndAttachment extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 承诺书
     */
    @ApiModelProperty("承诺书")
    @TableField(value = "commitment", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> commitment;

    /**
     * 推荐表
     */
    @ApiModelProperty("推荐表")
    @TableField(value = "recommendation", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> recommendation;

    /**
     * 申报书
     */
    @ApiModelProperty("申报书")
    @TableField(value = "apply_form", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> applyForm;

    /**
     * 执照
     */
    @ApiModelProperty("执照")
    @TableField(value = "license", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> license;

    /**
     * 审计报告
     */
    @ApiModelProperty("审计报告")
    @TableField(value = "audit_report", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> auditReport;

    /**
     * 知识产权
     */
    @ApiModelProperty("知识产权")
    @TableField(value = "intellectual_property", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> intellectualProperty;

    /**
     * 支持情况
     */
    @ApiModelProperty("支持情况")
    @TableField(value = "support_situation", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> supportSituation;

    /**
     * 其他附件
     */
    @ApiModelProperty("其他附件")
    @TableField(value = "other", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> other;

    /**
     * 经费的支出明细账或流水
     */
    @ApiModelProperty("经费的支出明细账或流水")
    @TableField(value = "expenditures_detail", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> expendituresDetail;

    /**
     * 考核指标完成情况及阶段性成果的证明材料
     */
    @ApiModelProperty("考核指标完成情况及阶段性成果的证明材料")
    @TableField(value = "completion_materials", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> completionMaterials;

}
