package com.fengyun.udf.uaa.domain.expertpanel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

@TableName("t_expert_panel")
@Data
public class ExpertPanel {

    @TableId("id")
    private String id;

    @TableField("name")
    private String name;

    @TableField("member")
    private String member;

    @TableField("status")
    private String status;

    @TableField("created_id")
    private String createdId;

    @TableField("updated_id")
    private String updatedId;

    @TableField("created_date")
    private ZonedDateTime createdDate;

    @TableField("updated_date")
    private ZonedDateTime updatedDate;

}