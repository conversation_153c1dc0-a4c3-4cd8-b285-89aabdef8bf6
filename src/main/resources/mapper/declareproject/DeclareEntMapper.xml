<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.declareproject.DeclareEntMapper">
    <select id="list" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.id as entId,
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.project_status AS projectStatus,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.project_progress as projectProgress,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        con.project_basis as projectBasis,
        con.research_content as researchContent,
        per.principal_name as principalName,
        per.tel AS tel
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN	flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        LEFT JOIN flow_form_personnel_situation per ON t_declare_ent.declare_id = per.project_id AND t_declare_ent.created_id = per.CREATED_ID
        <where>
        <if test="role != null and role != ''">
            and t_declare_ent_role.current_role = #{role}
        </if>
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
                    or con.subject_name like concat('%',#{keyWord},'%')
                    or per.principal_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="userId != null and userId != ''">
            and t_declare_ent_role.current_role in(
                select ROLE from t_role where ID in (select ROLE_ID from t_user_role where USER_ID=#{userId}))
        </if>
        <if test="declareId != null and declareId != ''">
            and t_declare_ent.declare_id = #{declareId}
        </if>
        <if test="declareTypeList != null and declareTypeList.length > 0">
            AND t_declare_ent.declare_type IN
            <foreach collection="declareTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        </where>
        order by t_declare_ent.created_date

    </select>


    <select id="listHistroy" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        DISTINCT
        t_declare_ent.id AS entId,
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.project_progress as projectProgress,
        c.select_event as selectEvent,
        u.LOGIN_NAME username,
        r.responsible_name responsibleName,
        con.subject_name subjectName,
        con.project_basis as projectBasis,
        con.research_content as researchContent,
        per.principal_name as principalName,
        per.tel AS tel,
        per.email AS principalMail
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        join (
        select a.* from wf_ru_node_task_history a
        right join (
        select ru_process_id,max(created_date) as created_date from wf_ru_node_task_history where assign_user_id = #{userId}
        GROUP BY ru_process_id) b on a.ru_process_id=b.ru_process_id and a.created_date=b.created_date
        ) c on c.ru_process_id = t_declare_ent.id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit r ON t_declare_ent.declare_id = r.project_id AND t_declare_ent.created_id = r.CREATED_ID
        LEFT JOIN	flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        LEFT JOIN flow_form_personnel_situation per ON t_declare_ent.declare_id = per.project_id AND t_declare_ent.created_id = per.CREATED_ID
        <where> c.assign_user_id = #{userId}
        <if test="keyWord != null and keyWord != ''">
            and (r.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%')
            or per.principal_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareTypeList != null and declareTypeList.length > 0">
            AND t_declare_ent.declare_type IN
            <foreach collection="declareTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        </where>
        order by t_declare_ent.created_date
    </select>

    <select id="countList" resultType="java.lang.Integer">
        SELECT
          count(1)
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        WHERE 1=1
        <if test="role != null and role != ''">
            and t_declare_ent_role.current_role = #{role}
        </if>
        <if test="townShips != null and townShips.size > 0">
            and t_declare_ent.town_city in
            <foreach collection="townShips" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        </if>
        <if test="offices != null and offices.size > 0">
            and t_declare_ent.office in
            <foreach collection="offices" item="office" open="(" separator="," close=")">
                #{office}
            </foreach>
        </if>

        <if test="declareId != null and declareId != ''">
            and t_declare_ent.declare_id = #{declareId}

        </if>


    </select>

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
            a.*,
            a.id AS entId,
            a.close_debate_video AS closeDebateVideo,
            a.close_debate_ppt AS closeDebatePPT,
            b.declare_name,
            b.declare_begin_time,
            b.declare_end_time,
            b.public_flag,
            b.review_template,
            con.subject_name AS subjectName
            , (SELECT tpc.audit_status
               FROM t_project_contract tpc
               WHERE tpc.project_id = a.id
            ) AS contractStatus
            , (SELECT tpr.audit_status
               FROM t_project_receipt tpr
               WHERE tpr.project_id = a.id
                 AND tpr.category = '1'
            ) AS receiptStatus
            , (SELECT dembi.status
               FROM declare_ent_middle_basic_info dembi
               WHERE dembi.project_id = a.id
            ) AS middleInspectionStatus
            , (SELECT deebi.status
               FROM declare_ent_end_basic_info deebi
               WHERE deebi.project_id = a.id
            ) AS endInspectionStatus
            , IFNULL(
                (
                    SELECT tpr.audit_status
                    FROM t_project_receipt tpr
                    WHERE tpr.project_id = a.id
                      AND tpr.category = '2'
                ),
                IF(a.project_status = '13', 0, NULL)
            ) AS secondReceiptStatus
            , IFNULL(
                (
                SELECT tpr.audit_status
                FROM t_project_receipt tpr
                WHERE tpr.project_id = a.id
                AND tpr.category = '3'
                ),
                IF(a.project_status = '17', 0, NULL)
            ) AS thirdReceiptStatus,
            IF(
                b.yearly_summary = 1 AND (a.project_status = 8 OR a.project_status > 10)
                , true
                , false
            ) AS showYearlySummaryEditButton
        FROM
            t_declare_ent a
        LEFT JOIN
            t_declare_project b ON a.declare_id = b.id
        LEFT JOIN
            flow_form_condition con ON a.declare_id = con.project_id AND a.created_id = con.CREATED_ID
        WHERE
            a.created_id = #{params.createdId}
        <if test="params.keyWord != null and params.keyWord != ''">
            and b.declare_name like concat('%',#{params.keyWord},'%')
        </if>
        <if test="params.declareType != null and params.declareType != ''">
            and a.declare_type = #{params.declareType}
        </if>
        <if test="params.projectStatus != null and params.projectStatus != ''">
            <if test='params.projectStatus == "0"'>
                and a.submit_status = #{params.projectStatus}
            </if>
            <if test='params.projectStatus == "1"'>
                and a.current_role='xmcs'
            </if>
            <if test='params.projectStatus == "2"'>
                and a.current_role='zjps'
            </if>
        </if>
        <if test="params.declareStatus != null and params.declareStatus != ''">
            <if test='params.declareStatus == "1"'>
                and a.ent_status='CONFIRM' and a.current_role!='over' and a.current_role!='end'
            </if>
            <if test='params.declareStatus == "2"'>
                and a.ent_status='CANCEL'
            </if>
            <if test='params.declareStatus == "3"'>
                and a.current_role='over'
            </if>
            <if test='params.declareStatus == "4"'>
                and a.current_role='end'
            </if>
        </if>
        order by a.created_date desc
    </select>

    <select id="pageRelation" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO">
        select a.*,b.declare_name,b.declare_begin_time,b.declare_end_time,b.public_flag,
               con.subject_name subjectName,r.responsible_name responsibleName,c.status as expertPanelStatus,
               c.name as expertPanelName,c.member
        from t_declare_ent a
        left join t_declare_project b on a.declare_id=b.id
        LEFT JOIN flow_form_responsible_unit r ON a.declare_id = r.project_id AND a.created_id = r.CREATED_ID
        LEFT JOIN flow_form_condition con ON a.declare_id = con.project_id AND a.created_id = con.CREATED_ID
        left join t_expert_panel c on a.expert_panel_id=c.id
        where a.current_role='over'
        <if test="params.keyWord != null and params.keyWord != ''">
            and (r.responsible_name like concat('%',#{params.keyWord},'%')
            or con.subject_name like concat('%',#{params.keyWord},'%')
            or c.name like concat('%',#{params.keyWord},'%'))
        </if>
        <if test="params.declareType != null and params.declareType != ''">
            and a.declare_type = #{params.declareType}
        </if>
        <if test="params.relationStatus != null and params.relationStatus != ''">
            <if test='params.relationStatus == "0"'>
                and a.expert_panel_id is null
            </if>
            <if test='params.relationStatus == "1"'>
                and a.expert_panel_id is not null
            </if>
        </if>
        order by a.created_date desc
    </select>

    <select id="checkDeclareEntIsCreated" resultType="java.lang.Boolean">
        SELECT
            COUNT(1) > 0
        FROM
            t_declare_ent
        WHERE
            declare_id = #{projectId}
          AND created_id = #{userId}
    </select>

    <select id="scorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.project_status as projectStatus,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.second_expert_panel_id as secondExpertPanelId,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        left join t_expert_score_history on t_declare_ent.id=t_expert_score_history.declare_ent_id and t_expert_score_history.created_id=#{userId}
        left join t_expert_panel on t_declare_ent.expert_panel_id=t_expert_panel.id
        WHERE t_declare_ent.current_role='over' and t_expert_score_history.id is null
        and FIND_IN_SET(#{userId},t_expert_panel.member)>0
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="historyScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.second_expert_panel_id as secondExpertPanelId,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        t_expert_score_history.total_score as totalScore
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        left join t_expert_score_history on t_declare_ent.id=t_expert_score_history.declare_ent_id
        WHERE t_declare_ent.current_role='over' and t_expert_score_history.id is not null
          and t_expert_score_history.created_id=#{userId} and t_expert_score_history.status='1'
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="draftScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.second_expert_panel_id as secondExpertPanelId,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        t_expert_score_history.total_score as totalScore
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        left join t_expert_score_history on t_declare_ent.id=t_expert_score_history.declare_ent_id
        WHERE t_declare_ent.current_role='over' and t_expert_score_history.id is not null
          and t_expert_score_history.created_id=#{userId} and t_expert_score_history.status='0'
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="summaryScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO">
        SELECT
        t_declare_ent.id AS declareEntId,
        t_declare_project.id as declareId,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        d.member,
        d.memberName,
        averageScore,
        t_expert_panel.name AS expertPanelName,
        t_expert_panel.member as allMember
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        right join (select a.id,GROUP_CONCAT(b.created_id) as member,GROUP_CONCAT(d.NICK_NAME) as memberName,
                        CAST(AVG(b.total_score) AS DECIMAL(9,2)) as averageScore
        from t_declare_ent a
        left join t_expert_score_history b on a.id=b.declare_ent_id
        left join t_user d on b.created_id=d.ID
        where b.id is not null and b.score_index ='1' and b.status='1' GROUP BY a.id) d on t_declare_ent.id=d.id
        left join t_expert_panel on t_declare_ent.expert_panel_id=t_expert_panel.id
        WHERE t_declare_ent.current_role='over'
        <if test="keyWord != null and keyWord != ''">
            and (con.subject_name like concat('%',#{keyWord},'%') or memberName like concat('%',#{keyWord},'%')
                or averageScore=#{keyWord} or t_expert_panel.name like concat('%',#{keyWord},'%'))
        </if>
        <if test="responsibleName != null and responsibleName != ''">
            AND
                c.responsible_name like concat('%',#{responsibleName},'%')
        </if>
        <if test="declareTypeList != null and declareTypeList.size > 0">
            AND t_declare_ent.declare_type in
            <foreach collection="declareTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="getExpertGroupById" resultType="Integer">
        select count(1) from t_declare_ent where id!=#{id} and expert_panel_id=#{expertPanelId}
    </select>

    <select id="getSecondExpertGroupById" resultType="Integer">
        select count(1) from t_declare_ent where id!=#{id} and second_expert_panel_id=#{expertPanelId}
    </select>

    <select id="getReviewPassRecords" resultType="com.fengyun.udf.uaa.dto.request.declareproject.RecordDTO">
        select a.id as entId,b.id as projectId,con.subject_name subjectName
             , a.project_status AS projectStatus
             , a.project_progress AS projectProgress
        from t_declare_ent a
        left join t_declare_project b on a.declare_id=b.id
        left join flow_form_condition con
        on a.declare_id = con.project_id and a.created_id = con.CREATED_ID
        left join flow_form_responsible_unit res
        on  a.declare_id = res.project_id and a.created_id = res.CREATED_ID
        where  a.submit_status='1' and
        con.subject_name =#{subjectName}
        and res.responsible_name =#{responsibleName}
        and b.id =#{id};
    </select>

    <select id="getDeclareEntIdByProjectId" resultType="com.fengyun.udf.uaa.domain.declareproject.DeclareEnt">
        select a.* from t_declare_ent a
        where a.declare_id =#{projectID} and a.submit_status='1'

    </select>

    <select id="getMaxProjectNo" resultType="java.lang.String">
        select max(project_no) from t_declare_ent where project_no like concat(#{prefix},'%')
    </select>

    <select id="listFirstPass" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        DISTINCT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.project_status as projectStatus,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.project_progress as projectProgress,
        c.select_event as selectEvent,
        u.LOGIN_NAME username,
        r.responsible_name responsibleName,
        con.subject_name subjectName,
        con.project_basis as projectBasis,
        con.research_content as researchContent
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        join (
        select a.* from wf_ru_node_task_history a
        right join (
        select ru_process_id,max(created_date) as created_date from wf_ru_node_task_history where assign_user_id = #{userId}
        GROUP BY ru_process_id) b on a.ru_process_id=b.ru_process_id and a.created_date=b.created_date
        ) c on c.ru_process_id = t_declare_ent.id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit r ON t_declare_ent.declare_id = r.project_id AND t_declare_ent.created_id = r.CREATED_ID
        LEFT JOIN	flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        WHERE c.assign_user_id = #{userId} and t_declare_ent.project_status &gt; '1' and t_declare_ent.project_status != '3'
        <if test="keyWord != null and keyWord != ''">
            and (r.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        <if test='isUploadedPPTVideo == "1"'>
           and close_debate_ppt is not null and close_debate_video is not null
        </if>
        <if test='isUploadedPPTVideo == "0"'>
            and (close_debate_ppt is null or close_debate_video is null)
        </if>
        order by t_declare_ent.updated_date desc
    </select>
    <select id="listSecondRelation" resultType="java.lang.String">
        select t_declare_ent.id  from t_declare_ent left join t_expert_panel on t_declare_ent.second_expert_panel_id=t_expert_panel.id
        where FIND_IN_SET(#{userId},t_expert_panel.member)>0 and t_declare_ent.second_expert_panel_id is not null
    </select>

    <select id="pageRelation2" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO">
        select a.*,b.declare_name,b.declare_begin_time,b.declare_end_time,b.public_flag,
        con.subject_name subjectName,r.responsible_name responsibleName,c.status as expertPanelStatus,
        c.name as expertPanelName,c.member
        from t_declare_ent a
        left join t_declare_project b on a.declare_id=b.id
        LEFT JOIN flow_form_responsible_unit r ON a.declare_id = r.project_id AND a.created_id = r.CREATED_ID
        LEFT JOIN flow_form_condition con ON a.declare_id = con.project_id AND a.created_id = con.CREATED_ID
        left join t_expert_panel c on a.second_expert_panel_id=c.id
        where a.current_role='over' and a.project_status ='2'
        <if test="params.keyWord != null and params.keyWord != ''">
            and (r.responsible_name like concat('%',#{params.keyWord},'%')
            or con.subject_name like concat('%',#{params.keyWord},'%')
            or c.name like concat('%',#{params.keyWord},'%'))
        </if>
        <if test="params.declareType != null and params.declareType != ''">
            and a.declare_type = #{params.declareType}
        </if>
        <if test="params.relationStatus != null and params.relationStatus != ''">
            <if test='params.relationStatus == "0"'>
                and a.second_expert_panel_id is null
            </if>
            <if test='params.relationStatus == "1"'>
                and a.second_expert_panel_id is not null
            </if>
        </if>
        order by a.created_date desc
    </select>

    <select id="listFirstRelation" resultType="java.lang.String">
        select t_declare_ent.id  from t_declare_ent left join t_expert_panel on t_declare_ent.expert_panel_id=t_expert_panel.id
        where FIND_IN_SET(#{userId},t_expert_panel.member)>0 and t_declare_ent.expert_panel_id is not null
    </select>

    <select id="secondScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        t_declare_ent.second_expert_panel_id as secondExpertPanelId,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        left join t_expert_score_history on t_declare_ent.id=t_expert_score_history.declare_ent_id and t_expert_score_history.created_id=#{userId}
        left join t_expert_panel on t_declare_ent.second_expert_panel_id=t_expert_panel.id and t_declare_ent.project_status='2'
        WHERE t_declare_ent.current_role='over'
        and t_expert_score_history.id is null
        and FIND_IN_SET(#{userId},t_expert_panel.member)>0 and second_expert_panel_id is not null
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="secondHistoryScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        t_expert_score_history.total_score as totalScore
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        left join t_expert_score_history on t_declare_ent.id=t_expert_score_history.declare_ent_id
        WHERE t_declare_ent.current_role='over' and t_expert_score_history.id is not null
        and t_expert_score_history.created_id=#{userId} and t_expert_score_history.status='1'
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="secondDraftScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO">
        SELECT
        t_declare_ent.entprise_name AS entpriseName,
        t_declare_ent.created_date AS createdDate,
        t_declare_ent.updated_date AS updatedDate,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.credit_code AS creditCode,
        t_declare_ent.current_role as node,
        t_declare_ent.pre_role as preNode,
        t_declare_ent.status as status,
        t_declare_ent.id AS declareEntId,
        t_declare_project.declare_name AS declareName,
        t_declare_project.declare_begin_time AS declareBeginTime,
        t_declare_project.declare_end_time AS declareEndTime,
        t_declare_project.id as declareId,
        t_declare_ent_role.id as declareEntRoleId,
        t_declare_ent.declare_type as declareType,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        u.LOGIN_NAME username,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        t_expert_score_history.total_score as totalScore
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN t_declare_ent_role on t_declare_ent.id =t_declare_ent_role.declare_ent_id
        LEFT JOIN t_user u ON u.id = t_declare_ent.created_id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        left join t_expert_score_history on t_declare_ent.id=t_expert_score_history.declare_ent_id
        WHERE t_declare_ent.current_role='over' and t_expert_score_history.id is not null
        and t_expert_score_history.created_id=#{userId} and t_expert_score_history.status='0'
        <if test="keyWord != null and keyWord != ''">
            and (c.responsible_name like concat('%',#{keyWord},'%')
            or con.subject_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="declareType != null and declareType != ''">
            and t_declare_ent.declare_type = #{declareType}
        </if>
        order by t_declare_ent.updated_date desc
    </select>
    <select id="secondSummaryScorePage" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO">
        SELECT
        t_declare_ent.id AS declareEntId,
        t_declare_project.id as declareId,
        t_declare_ent.created_id AS createdId,
        t_declare_ent.project_no as projectNo,
        t_declare_ent.close_debate_video as closeDebateVideo,
        t_declare_ent.close_debate_ppt as closeDebatePPT,
        c.responsible_name responsibleName,
        con.subject_name subjectName,
        d.member,
        d.memberName,
        averageScore,
        t_expert_panel.name AS expertPanelName,
        t_expert_panel.member as allMember
        FROM
        t_declare_ent
        LEFT JOIN t_declare_project ON t_declare_ent.declare_id = t_declare_project.id
        LEFT JOIN flow_form_responsible_unit c ON t_declare_ent.declare_id = c.project_id AND t_declare_ent.created_id = c.CREATED_ID
        LEFT JOIN flow_form_condition con ON t_declare_ent.declare_id = con.project_id AND t_declare_ent.created_id = con.CREATED_ID
        right join (select a.id,GROUP_CONCAT(b.created_id) as member,GROUP_CONCAT(d.NICK_NAME) as memberName,
        CAST(AVG(b.total_score) AS DECIMAL(9,2)) as averageScore
        from t_declare_ent a
        left join t_expert_score_history b on a.id=b.declare_ent_id
        left join t_user d on b.created_id=d.ID
        where b.id is not null and b.score_index ='2' and b.status='1' GROUP BY a.id) d on t_declare_ent.id=d.id
        left join t_expert_panel on t_declare_ent.second_expert_panel_id=t_expert_panel.id
        WHERE t_declare_ent.current_role='over'
        <if test="keyWord != null and keyWord != ''">
            and (con.subject_name like concat('%',#{keyWord},'%') or memberName like concat('%',#{keyWord},'%')
            or averageScore=#{keyWord} or t_expert_panel.name like concat('%',#{keyWord},'%')
            )
        </if>
        <if test="responsibleName != null and responsibleName != ''">
            AND
                c.responsible_name like concat('%',#{responsibleName},'%')
        </if>
        <if test="declareTypeList != null and declareTypeList.size > 0">
            AND t_declare_ent.declare_type in
            <foreach collection="declareTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t_declare_ent.updated_date desc
    </select>

    <select id="getIsRelationFirst" resultType="java.lang.Integer">
        select count(1) from t_declare_ent tde inner join t_expert_panel tep on tde.expert_panel_id = tep.id
        where FIND_IN_SET(#{userId},tep.member)>0;
    </select>

    <select id="getUploadedPPtAndVideoProject" resultType="com.fengyun.udf.uaa.dto.request.declareproject.ImportRecordDTO">
        select con.subject_name subjectName ,res.responsible_name as responsibleName ,a.declare_type as declareType from t_declare_ent a
        left join t_declare_project b on a.declare_id=b.id
        left join flow_form_condition con
        on a.declare_id = con.project_id and a.created_id = con.CREATED_ID
        left join flow_form_responsible_unit res
        on  a.declare_id = res.project_id and a.created_id = res.CREATED_ID
        where  a.project_status ='2'
        <if test="declareType != null and declareType != ''">
            and a.declare_type = #{declareType}
        </if>
        <if test='isUploadedPPTVideo == "1"'>
            and close_debate_ppt is not null and close_debate_video is not null
        </if>
        <if test='isUploadedPPTVideo == "0"'>
            and (close_debate_ppt is null or close_debate_video is null)
        </if>
    </select>
    <select id="pageByDeclareId"
            resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntListDTO">
        SELECT
            de.id AS id,
            de.declare_id AS declareId,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.created_date AS createdDate,
            de.declare_type AS declareType,
            de.created_id AS createdId,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM t_declare_ent de
        LEFT JOIN flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        WHERE
            de.declare_id = #{condition.declareId}
        <if test="condition.projectStatusColl != null and condition.projectStatusColl.length > 0">
            and de.project_status in
            <foreach collection="condition.projectStatusColl" item="projectStatus" open="(" separator="," close=")">
                #{projectStatus}
            </foreach>
        </if>
        <if test="condition.declareTypeList != null and condition.declareTypeList.size > 0">
            AND de.declare_type IN
            <foreach collection="condition.declareTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.subjectName != null and condition.subjectName != ''">
            AND
            con.subject_name LIKE CONCAT('%',#{condition.subjectName},'%')
        </if>
        <if test="condition.responsibleName != null and condition.responsibleName != ''">
            AND
            res.responsible_name LIKE CONCAT('%',#{condition.responsibleName},'%')
        </if>
    </select>
    <select id="getByDeclareIdAndStatus" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntListDTO">
        SELECT
            de.id AS id,
            de.declare_id AS declareId,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.created_date AS createdDate,
            de.declare_type AS declareType,
            de.created_id AS createdId,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM t_declare_ent de
        LEFT JOIN flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        WHERE
            de.declare_id = #{declareId}
        <if test="projectStatus != null and projectStatus != ''">
            AND de.project_status = #{projectStatus}
        </if>
        <if test="projectProgress != null and projectProgress != ''">
            AND de.project_progress = #{projectProgress}
        </if>
    </select>
</mapper>
