package com.fengyun.udf.uaa.web.rest.declareproject;

import cn.hutool.core.util.StrUtil;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.constant.DeclareCons;
import com.fengyun.udf.uaa.dto.request.declareproject.GenerateReceiptDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.SaveReceiptDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.SubmitReceiptDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptDetailDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Objects;

/**
 * 2024/7/22
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(value = "项目申报-项目收据-企业端", tags = "项目申报-项目收据-企业端")
@Validated
@RestController
@SuppressWarnings("all")
@RequestMapping({"/api/declare/receipt"})
public class DeclareEntReceiptCustomerResource extends BaseResource {

    @Autowired
    private DeclareEntService declareEntService;

    @PostMapping("/2pdf")
    @CustomBaseException
    @AuditLog(method = "/api/declare/receipt/2pdf", methodDescription = "生成收据PDF", httpMethod = "POST")
    @ApiOperation(value = "生成收据PDF")
    public ResponseEntity<ReturnResultDTO<?>> generateReceiptPdf(
            @Valid @RequestBody GenerateReceiptDTO dto) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.generateReceiptPdf(dto));
    }

    @PostMapping("/save")
    @CustomBaseException
    @AuditLog(method = "/api/declare/receipt/save", methodDescription = "保存收据信息", httpMethod = "POST")
    @ApiOperation(value = "保存收据信息")
    public ResponseEntity<ReturnResultDTO<?>> saveReceipt(
            @Valid @RequestBody SaveReceiptDTO dto) {
        declareEntService.saveReceipt(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/submit")
    @CustomBaseException
    @AuditLog(method = "/api/declare/receipt/submit", methodDescription = "提交收据信息", httpMethod = "POST")
    @ApiOperation(value = "提交收据信息")
    public ResponseEntity<ReturnResultDTO<?>> submitReceipt(
            @Valid @RequestBody SubmitReceiptDTO dto) {
        if(StrUtil.isBlank(dto.getCategory())) {
            dto.setCategory(DeclareCons.RECEIPT_CATEGORY.FIRST.getCode());
        }
        declareEntService.submitReceipt(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/detail")
    @CustomBaseException
    @AuditLog(method = "/api/declare/receipt/detail", methodDescription = "收据详情", httpMethod = "POST")
    @ApiOperation(value = "收据详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "category", value = "收据次数", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectReceiptDetailDTO>> receiptDetail(
            @Valid @NotBlank(message = "项目id不能为空") String id,
            String category) {
        // 给收据默认赋值第一次
        if(Objects.isNull(DeclareCons.RECEIPT_CATEGORY.getByCode(category))) {
            category = DeclareCons.RECEIPT_CATEGORY.FIRST.getCode();
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.receiptDetail(id, category));
    }
}
