package com.fengyun.udf.uaa.service.declareproject;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.domain.declareproject.DeclareSubProject;
import com.fengyun.udf.uaa.domain.declareproject.ProcessModel;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.declareproject.CreateDeclareSubProjectDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareSubProjectShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.FileDTO;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareSubProjectMapper;
import com.fengyun.udf.uaa.mapper.declareproject.ProcessModelMapper;
import com.fengyun.udf.uaa.mapper.system.UserMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;

@Service
@Transactional
@Slf4j
@RequiredArgsConstructor
public class DeclareFlowPlatformService extends BaseService {

    private final DeclareSubProjectMapper declareSubProjectMapper;

    private final ProcessModelMapper processModelMapper;

    public final UserMapper userMapper;


    /**
     * 流程管理保存
     * @param dto
     */
    public void save(CreateDeclareSubProjectDTO dto) {
        // 判断是否存在流程配置
        LambdaQueryWrapper<DeclareSubProject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeclareSubProject::getDeclareId, dto.getDeclareId())
                .eq(DeclareSubProject::getBigTypeId, dto.getBigTypeId())
                .eq(DeclareSubProject::getSmallTypeId, dto.getSmallTypeId());
        List<DeclareSubProject> existList = declareSubProjectMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(existList)) {
            throw new ValidationException("error", "该申报子项已配置");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        DeclareSubProject entity = new DeclareSubProject();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(UUIDGenerator.getUUID());
        if (CollectionUtils.isNotEmpty(dto.getFileContent())) {
            entity.setFileContent(JSON.toJSONString(dto.getFileContent()));
        }
        String flowId = UUIDGenerator.getUUID();
        entity.setFlowId(flowId);
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        entity.setCreatedId(userId);
        entity.setUpdatedId(userId);
        entity.setCreatedDate(nowTime);
        entity.setUpdatedDate(nowTime);
        declareSubProjectMapper.insert(entity);
        // 新增流程
        ProcessModel processModel = new ProcessModel();
        processModel.setId(flowId);
        processModel.setModel(dto.getFlowConfigInfo());
        processModel.setCreatedId(userId);
        processModel.setUpdatedId(userId);
        processModel.setCreatedDate(nowTime);
        processModel.setUpdatedDate(nowTime);
        processModelMapper.insert(processModel);
    }

    /**
     * 流程管理更新
     * @param dto
     */
    public void update(CreateDeclareSubProjectDTO dto) {
        DeclareSubProject entity = declareSubProjectMapper.selectById(dto.getId());
        if (entity == null) {
            throw new ValidationException("error", "申报子项不存在");
        }
        // 判断是否存在流程配置
        LambdaQueryWrapper<DeclareSubProject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeclareSubProject::getDeclareId, dto.getDeclareId())
                .eq(DeclareSubProject::getBigTypeId, dto.getBigTypeId())
                .eq(DeclareSubProject::getSmallTypeId, dto.getSmallTypeId())
                .ne(DeclareSubProject::getId, dto.getId());
        List<DeclareSubProject> existList = declareSubProjectMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(existList)) {
            throw new ValidationException("error", "该申报子项已配置");
        }
        ProcessModel processModel = processModelMapper.selectById(entity.getFlowId());
        if (processModel == null) {
            throw new ValidationException("error", "流程不存在");
        }
        if (CollectionUtils.isNotEmpty(dto.getFileContent())) {
            entity.setFileContent(JSON.toJSONString(dto.getFileContent()));
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        declareSubProjectMapper.updateById(entity);
        // 更新流程
        processModel.setModel(dto.getFlowConfigInfo());
        processModel.setUpdatedId(SecurityUtils.getUserId());
        processModel.setUpdatedDate(nowTime);
        processModelMapper.updateById(processModel);
    }

    /**
     * 列表查询
     * @param page 分页参数
     * @param declareName 申报名
     * @return
     */
    public Page<DeclareSubProjectShowDTO> page(Page<DeclareSubProjectShowDTO> page, String declareName) {
        List<DeclareSubProjectShowDTO> list = declareSubProjectMapper.page(page, declareName);
        page.setRecords(list);
        return page;
    }

    /**
     * 查询详情
     * @param id 申报子项目Id?
     * @return 申报子项目详情?
     */
    public DeclareSubProjectShowDTO getById(String id) {
        DeclareSubProjectShowDTO dto = declareSubProjectMapper.selectSubProjectDetail(id);
        if (dto == null) {
            throw new ValidationException("error", "申报子项不存在");
        }
        if (StringUtils.isNotEmpty(dto.getFileContents())) {
            dto.setFileContent(JSON.parseArray(dto.getFileContents(), FileDTO.class));
        }
        ProcessModel processModel = processModelMapper.selectById(dto.getFlowId());
        if (StringUtils.isNotBlank(processModel.getModel())) {
            JSONObject jsonObject = JSONObject.parseObject(processModel.getModel(), Feature.OrderedField);
            for (String key : jsonObject.keySet()) {
                String userId = jsonObject.getJSONObject(key).getString("userId");
                if (StringUtils.isNotBlank(userId)) {
                    User user = userMapper.selectById(userId);
                    if (user != null) {
                        jsonObject.getJSONObject(key).put("userName", user.getLoginName());
                    }
                }
            }
            dto.setFlowConfigInfo(jsonObject.toString());
        }
        return dto;
    }

    /**
     * 删除
     * @param id
     */
    public void delete(String id) {
        DeclareSubProject entity = declareSubProjectMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "申报子项不存在");
        }
        declareSubProjectMapper.deleteById(id);
        processModelMapper.deleteById(entity.getFlowId());
    }

}
