<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.declareproject.DeclareSubProjectMapper">
    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareSubProjectShowDTO">
        select t1.*,t2.declare_name,t3.big_type,t4.small_type from t_declare_sub_project t1
        left join t_declare_project t2 on t1.declare_id=t2.id
        left join t_declare_big_type t3 on t1.big_type_id=t3.id
        left join t_declare_small_type t4 on t1.small_type_id=t4.id
        <where>
            <if test="declareName != null and declareName != ''">
                and t2.declare_name like CONCAT('%', #{declareName}, '%')
            </if>
        </where>
        order by created_date desc
    </select>

    <select id="selectSubProject" resultType="Map">
        select b.big_type_id,b.small_type_id,c.big_type,d.small_type from t_declare_ent a
        left join t_declare_sub_project b on a.sub_project_id=b.id
        left join t_declare_big_type c on b.big_type_id=c.id
        left join t_declare_small_type d on b.small_type_id=d.id
        where a.id=#{id}
    </select>

    <select id="selectSubProjectDetail" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareSubProjectShowDTO">
        select t1.*,t1.file_content as fileContents,t2.declare_name,t3.big_type,t4.small_type from t_declare_sub_project t1
        left join t_declare_project t2 on t1.declare_id=t2.id
        left join t_declare_big_type t3 on t1.big_type_id=t3.id
        left join t_declare_small_type t4 on t1.small_type_id=t4.id
        where t1.id=#{id}
    </select>

</mapper>
