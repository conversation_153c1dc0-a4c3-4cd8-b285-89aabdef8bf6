package com.fengyun.udf.uaa.mapper.expertpanel;

import com.fengyun.udf.uaa.domain.expertpanel.ExpertScoreHistory;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ExpertScoreHistoryMapper extends com.baomidou.mybatisplus.core.mapper.BaseMapper<ExpertScoreHistory> {

    List<ExpertScoreDTO> selectScoreDetailList(@Param("declareEntId") String declareEntId);

    List<ExpertScoreDTO> selectSecondScoreDetailList(@Param("declareEntId") String declareEntId);
}
