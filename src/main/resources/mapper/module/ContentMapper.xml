<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.module.ContentMapper">

    <select id="selectContentByOwningIds" resultType="com.fengyun.udf.uaa.dto.response.module.ContentListDto">
        select id,name,owning_module,introduction,sub_date,null as declareEndTime,'1' as type from m_content where owning_module in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="keyWord != null and keyWord != ''">
            and (name like concat('%',#{keyWord},'%') or introduction like concat('%',#{keyWord},'%'))
        </if>
        union all
        select id,declare_name as name,'项目申报' as owning_module,null as introduction,created_date as sub_date,
               declare_end_time as declareEndTime,'2' as type
        from t_declare_project where status='1' and submit_status='1'
        <if test="keyWord != null and keyWord != ''">
            and (declare_name like concat('%',#{keyWord},'%') or content like concat('%',#{keyWord},'%'))
        </if>
    </select>
</mapper>
