package com.fengyun.udf.uaa.mapper.instrument;

import com.fengyun.udf.uaa.domain.instrument.InstrumentOrderExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportListDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
public interface InstrumentOrderExportMapper extends BaseMapper<InstrumentOrderExport> {

    List<InstrumentOrderExportListDTO> selectPage(Page page, @Param("condition")InstrumentOrderExportQueryDTO dto);

}
