package com.fengyun.udf.uaa.config.security.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import lombok.Builder;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Created by wangg on 2019/7/12.
 */
@Builder
public class WebAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private ObjectMapper objectMapper;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        response.setContentType("application/json;charset=UTF-8");
        ReturnResultDTO resultDTO = new ReturnResultDTO(ReturnCode.AuthenticationException.getCode(), exception.getMessage());
        response.getWriter().write(objectMapper.writeValueAsString(resultDTO));
    }
}
