package com.fengyun.udf.uaa.domain.inspection.end;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "承担单位情况")
@TableName(value = "declare_ent_end_responsible_unit", autoResultMap = true)
public class DeclareEntEndResponsibleUnit extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    @TableField("responsible_name")
    private String responsibleName;

    /**
     * 注册时间
     */
    @ApiModelProperty("注册时间")
    @TableField("register_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate registerDate;

    /**
     * 注册资本(万元)
     */
    @ApiModelProperty("注册资本(万元)")
    @TableField("registered_capital")
    private String registeredCapital;

    /**
     * 法人代表
     */
    @ApiModelProperty("法人代表")
    @TableField("corporate_representative")
    private String corporateRepresentative;

    /**
     * 注册地址
     */
    @ApiModelProperty("注册地址")
    @TableField("register_address")
    private String registerAddress;

    /**
     * 经营地址
     */
    @ApiModelProperty("经营地址")
    @TableField("business_address")
    private String businessAddress;

    /**
     * 办公面积m2
     */
    @ApiModelProperty("办公面积m2")
    @TableField("office_area")
    private Double officeArea;

    /**
     * 公司人数人
     */
    @ApiModelProperty("公司人数人")
    @TableField("employer_num")
    private Integer employerNum;

    /**
     * 社保缴纳人数人
     */
    @ApiModelProperty("社保缴纳人数人")
    @TableField("social_insurance_people_num")
    private Integer socialInsurancePeopleNum;

}
