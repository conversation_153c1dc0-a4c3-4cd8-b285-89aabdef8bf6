package com.fengyun.udf.uaa.domain.inspection.middle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeclareEntMiddleChargePrincipal", description = "项目中期检查负责人")
@TableName(value = "declare_ent_middle_charge_principal", autoResultMap = true)
public class DeclareEntMiddleChargePrincipal extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    @TableField("responsible_unit")
    private String responsibleUnit;

    /**
     * 参与单位名称
     */
    @ApiModelProperty("参与单位名称")
    @TableField("participate_unit")
    private String participateUnit;

    //-------------------------------负责人信息-------------------------------

    /**
     * 负责人姓名
     */
    @ApiModelProperty("负责人姓名")
    @TableField("principal_name")
    private String principalName;

    /**
     * 负责人所在单位
     */
    @ApiModelProperty("负责人所在单位")
    @TableField("principal_belonging_company")
    private String principalBelongingCompany;

    /**
     * 职务
     */
    @ApiModelProperty("职务")
    @TableField("principal_position")
    private String principalPosition;

    /**
     * 在本项目中承担主要工作
     */
    @ApiModelProperty("在本项目中承担主要工作")
    @TableField("principal_work")
    private String principalWork;

    /**
     * 备注(如有人员增减，请说明原因)
     */
    @ApiModelProperty("备注(如有人员增减，请说明原因)")
    @TableField("remarks")
    private String remarks;

    /**
     * 是否为国创中心双聘人才
     */
    @ApiModelProperty("是否为国创中心双聘人才")
    @TableField("double_hire")
    private Boolean doubleHire;

}
