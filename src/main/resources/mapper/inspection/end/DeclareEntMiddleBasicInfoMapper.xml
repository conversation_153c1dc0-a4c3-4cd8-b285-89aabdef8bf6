<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.inspection.end.DeclareEntEndBasicInfoMapper">

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.inspection.end.DeclareEntEndBasicInfoListDTO">
        SELECT deebi.*,
        tde.project_status AS projectStatus,
        tde.declare_id AS declareId
        FROM declare_ent_end_basic_info deebi
        LEFT JOIN t_declare_ent tde ON tde.id = deebi.project_id
         <where>
             <if test="condition.keyword != null and condition.keyword != ''">
                 AND deebi.subject_name LIKE CONCAT('%', #{condition.keyword}, '%')
             </if>
             <if test="condition.declareType != null and condition.declareType != ''">
                 AND deebi.declare_type = #{condition.declareType}
             </if>
            <choose>
                <when test="condition.history != null and condition.history">
                     AND EXISTS (SELECT 1 FROM wf_ru_node_task_history wrnth WHERE wrnth.ru_process_id = deebi.id)
                </when>
                <otherwise>
                    AND deebi.`status` = 1
                </otherwise>
            </choose>
         </where>
    </select>

</mapper>
