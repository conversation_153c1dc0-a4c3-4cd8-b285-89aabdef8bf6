package com.fengyun.udf.uaa.mapper.declareproject;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.declareproject.ProjectContract;
import com.fengyun.udf.uaa.dto.request.declareproject.QueryContractDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractListDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Mapper
public interface ProjectContractMapper extends BaseMapper<ProjectContract> {

    /**
     * 合同详情
     * @param id 项目Id
     * @return 合同详情
     */
    ProjectContractDetailDTO detail(String id);

    /**
     * 合同汇总列表
     * @param page 分页参数
     * @param dto 查询条件
     * @return 合同汇总列表
     */
    List<ProjectContractListDTO> page(Page<ProjectContractListDTO> page, @Param("condition") QueryContractDTO dto);
}
