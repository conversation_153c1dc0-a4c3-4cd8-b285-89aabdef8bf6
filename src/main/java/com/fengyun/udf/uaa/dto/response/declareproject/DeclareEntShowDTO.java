package com.fengyun.udf.uaa.dto.response.declareproject;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.constant.DeclareCons;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.sun.xml.bind.v2.runtime.reflect.opt.Const;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

import static com.fengyun.udf.uaa.constant.Constants.NEW_PROJECT_STATUS.MID_INSPECTION;

/**
 * Created by Administrator on 2021/3/11.
 */
@Data
public class DeclareEntShowDTO extends AbstractDTO {

    private String id;

    private String entId;

    @ApiModelProperty("申报项目ID")
    private String declareId;

    @ApiModelProperty("申报项目角色ID")
    private String declareEntRoleId;

    @ApiModelProperty("申报项目流程ID")
    private String declareEntId;

    @ApiModelProperty("申报项目名称")
    private String declareName;

    @ApiModelProperty("申报开始时间")
    private String declareBeginTime;

    @ApiModelProperty("申报结束时间")
    private String declareEndTime;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty(value = "课题名称")
    private String subjectName;

    @ApiModelProperty("承担单位")
    private String responsibleName;

    @ApiModelProperty(value = "立项依据")
    private String projectBasis;

    @ApiModelProperty("研究内容")
    private String researchContent;

    @ApiModelProperty("企业名称")
    private String entpriseName;

    @ApiModelProperty("社会信用代码")
    private String creditCode;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime createdDate;
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createdDateStr;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime updatedDate;

    @ApiModelProperty("创建人")
    private String createdId;

    @ApiModelProperty("审批节点")
    @DictData(target = "nodeStr", codeType = "TDECLARE_NODE")
    private String node;
    private String nodeStr;

    @ApiModelProperty(hidden = true)
    private String status;

    private Boolean returnFlag;

    @ApiModelProperty(hidden = true)
    private String preNode;

    @ApiModelProperty(value = "企业申报状态")
    @DictData(target = "enDeclareStatusValue", codeType = "TDECLARE_STATUS")
    private String enDeclareStatus;
    private String enDeclareStatusValue;

    private String entStatus;

    @ApiModelProperty(value = "当前角色")
    @DictData(target = "currentRoleStr", codeType = "TDECLARE_NODE")
    private String currentRole;
    private String currentRoleStr;

    @ApiModelProperty(value = "提交状态，0-待提交，1-已提交")
    private String submitStatus;

    @ApiModelProperty(value = "上传收据按钮标识，0-不展示，1-展示")
    private String receiptButtonFlag;

    @ApiModelProperty(value = "0-未公示，1-已公示，2-项目立项")
    private String publicFlag;

    @ApiModelProperty(value = "申报类别")
    @DictData(target = "declareTypeStr", codeType = "DECLARE_TYPE_BAK")
    private String declareType;
    private String declareTypeStr;

    @ApiModelProperty(value = "审核结果" )
    @DictData(target = "selectEventStr",codeType = "TDECLARE_EVENT")
    private String selectEvent;
    private String selectEventStr;

    @ApiModelProperty(value = "打分结果" )
    private String totalScore;

    @ApiModelProperty(value = "评审模板")
    private String reviewTemplate;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> reviewTemplates;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty(value = "申报状态")
    @DictData(target = "projectStatusStr",codeType = "NEW_PROJECT_STATUS")
    private String projectStatus;
    private String projectStatusStr;

    @ApiModelProperty(value = "是否显示上传ppt")
    private String isShowDownloadPPT;

    @ApiModelProperty(value = "结辩ppt")
    private String closeDebatePPT;

    @ApiModelProperty(value = "结辩视频")
    private String closeDebateVideo;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> closeDebatePPTs;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> closeDebateVideos;

    @ApiModelProperty(value = "第二次评审专家")
    private String secondExpertPanelId;

    @ApiModelProperty(value = "第几次评审")
    private String numbersReview;

    @ApiModelProperty(value = "是否已上传答辩ppt和视频")
    private String uploadedPPTVideo;

    @ApiModelProperty(value = "项目进度")
    @DictData(target = "projectProgressStr", codeType = "PROJECT_PROGRESS")
    private String projectProgress;
    private String projectProgressStr;

    @ApiModelProperty(value = "合同状态，null-未上传模板,0-首次提交，1-审批中，2-退回修改，3-审批通过，4-审批不通过")
    private String contractStatus;

    @ApiModelProperty(value = "收据状态，null-未上传模板,0-首次提交，1-审批中，2-退回修改，3-审批通过，4-审批不通过")
    private String receiptStatus;

    @ApiModelProperty(value = "项目负责人姓名")
    private String principalName;

    @ApiModelProperty(value = "负责人联系方式")
    private String tel;

    @ApiModelProperty(value = "负责人邮箱")
    private String principalMail;

    @ApiModelProperty(value = "第二次收据状态，null-未上传模板,0-首次提交，1-审批中，2-退回修改，3-审批通过，4-审批不通过")
    private String secondReceiptStatus;

    @ApiModelProperty(value = "中期检查状态，null-未提交,0-保存未提交，1-审批中，2-退回修改，3-审批通过，4-审批不通过")
    private String middleInspectionStatus;

    @ApiModelProperty(value = "结题验收状态，null-未提交,0-保存未提交，1-审批中，2-退回修改，3-审批通过，4-审批不通过")
    private String endInspectionStatus;

    @ApiModelProperty(value = "第三次收据状态，null-未上传模板,0-首次提交，1-审批中，2-退回修改，3-审批通过，4-审批不通过")
    private String thirdReceiptStatus;

    @ApiModelProperty(value = "是否显示年度总结编辑按钮")
    private Boolean showYearlySummaryEditButton;

    public Boolean isShowMiddleInspectionEditButton() {
        // 处于中期检查状态
        return Constants.NEW_PROJECT_STATUS.MID_INSPECTION.getCode().equals(projectStatus)
                // 中期检查表单状态为退回修改
                || DeclareCons.MIDDLE_INSPECTION_STATUS.BACK.getCode().equals(middleInspectionStatus);
    }

    public Boolean isShowMiddleInspectionDetailButton() {
        return StrUtil.isNotBlank(middleInspectionStatus);
    }

    public Boolean isShowEndInspectionEditButton() {
        // 处于中期检查状态
        return Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE.getCode().equals(projectStatus)
                // 中期检查表单状态为退回修改
                || DeclareCons.MIDDLE_INSPECTION_STATUS.BACK.getCode().equals(endInspectionStatus);
    }

    public Boolean isShowEndInspectionDetailButton() {
        return StrUtil.isNotBlank(endInspectionStatus);
    }

}
