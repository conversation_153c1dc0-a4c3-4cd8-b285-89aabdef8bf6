package com.fengyun.udf.uaa.util;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * 2024/2/23
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class BigDecimalUtil {

    public static final BigDecimal TEN_THOUSAND = new BigDecimal(10000);

    public static final BigDecimal ONE_HUNDRED = new BigDecimal(100);

    public static BigDecimal getOrDefaultZero(BigDecimal value) {
        return getOrDefault(value, BigDecimal.ZERO);
    }

    public static BigDecimal getOrDefault(BigDecimal value, @NotNull BigDecimal d) {
        return value == null? d : value;
    }

    public static BigDecimal transferToTenThousand(BigDecimal value) {
        return Optional.ofNullable(value)
                .map(x -> x.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP))
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 用于安全获取BigDecimal字段值，避免null
     * @param value v
     * @return nonNull
     */
    public static BigDecimal safeGet(BigDecimal value) {
        return Optional.ofNullable(value).orElse(BigDecimal.ZERO);
    }

    /**
     * 用于安全获取BigDecimal字段值，避免null
     * @param value v
     * @return nonNull
     */
    public static BigDecimal safeDivide(BigDecimal value, BigDecimal value2) {
        return safeDivide(value,value2, 2);
    }

    /**
     * 用于安全获取BigDecimal字段值，避免null
     * @param value v
     * @return nonNull
     */
    public static BigDecimal safeDivide(BigDecimal value, BigDecimal value2,int scale) {
        BigDecimal value2Safe = safeGet(value2);
        if(value2Safe.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ONE;
        }
        return safeGet(value).divide(value2Safe, scale, RoundingMode.HALF_UP);
    }

    public static BigDecimal safeAdd(BigDecimal ...value) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : value) {
            result = result.add(safeGet(bigDecimal));
        }
        return result;
    }

    public static BigDecimal double2BigDecimal(Double d) {
        return Optional.ofNullable(d).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
    }

}
