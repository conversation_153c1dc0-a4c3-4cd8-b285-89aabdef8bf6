package com.fengyun.udf.uaa.service.area;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fengyun.udf.cache.redis.client.AreaCacheClient;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.area.Area;
import com.fengyun.udf.uaa.dto.response.area.AreaDTO;
import com.fengyun.udf.uaa.mapper.area.AreaMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
@Transactional
public class AreaService {
    private final String world_id_china = "7";

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private AreaCacheClient areaCacheClient;

    public static final String DATA_TYPE_AREA = "area";
    public static final String DATA_TYPE_AREA_NAME = "area_name";

    //包含直辖市 特别行政区
    public List<AreaDTO> getAllProvinces(final String worldId) {
        String world = worldId;
        if (StringUtils.isEmpty(world)) {
            world = world_id_china;
        }

        List<Area> areas = areaMapper.findByLevelAndWorldIdOrderBySequence(1, world);

        return prepareArea(areas);
    }

    private List<AreaDTO> prepareArea(List<Area> areas){
        List<AreaDTO> result = new ArrayList<>();
        if(areas != null && areas.size() > 0){
            for(Area area : areas){
                result.add(prepareArea(area));
            }
        }
        return result;
    }

    private AreaDTO prepareArea(Area area){
        AreaDTO dto = new AreaDTO();
        BeanUtils.copyProperties(area, dto);

        return dto;
    }

    public List<AreaDTO> getCitiessByprovinceId(final String provinceId) {
        final Area area = areaMapper.selectById(provinceId);
        //如果是直辖市，那么返回该直辖市
        if (Constants.TYPE_CITY.equals(area.getType())) {
            List<AreaDTO> result = new ArrayList<>();
            result.add(prepareArea(area));
            return result;
        }
        return prepareArea(areaMapper.findByParentSnOrderBySequence(provinceId));
    }

    public List<AreaDTO> getCountiesByCityid(final String cityId) {
        return prepareArea(areaMapper.findByParentSnOrderBySequence(cityId));
    }

    private void putAreaIntoRedis(Area area){
        areaCacheClient.add(DATA_TYPE_AREA, area.getSn(), area.getName());
    }


    public void initNameCacheData(){
        List<Area> provinces = areaMapper.selectList(new QueryWrapper<Area>().eq("TYPE","P"));
        if(provinces != null && provinces.size() > 0){
            for(Area province : provinces){
                areaCacheClient.add(DATA_TYPE_AREA_NAME, province.getName(), province.getSn());
                List<Area> citys = areaMapper.selectList(new QueryWrapper<Area>().eq("TYPE","C").eq("PARENT_SN",province.getSn()));
                if(citys != null && citys.size() > 0){
                    for(Area city : citys){
                        areaCacheClient.add(DATA_TYPE_AREA_NAME, province.getName()+"-"+city.getName(), city.getSn());
                        List<Area> regions = areaMapper.selectList(new QueryWrapper<Area>().eq("TYPE","D").eq("PARENT_SN",city.getSn()));
                        if(regions != null && regions.size() > 0){
                            for(Area region : regions){
                                areaCacheClient.add(DATA_TYPE_AREA_NAME, province.getName()+"-"+city.getName()+"-"+region.getName(), region.getSn());
                            }
                        }

                    }
                }

            }
        }
    }
    public void initCacheData() {
        List<Area> all = areaMapper.findAll();
        if (all != null && all.size() > 0) {
            for (Area area : all) {
                putAreaIntoRedis(area);
            }
        }
    }
}
