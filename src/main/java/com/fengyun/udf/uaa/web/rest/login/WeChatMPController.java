package com.fengyun.udf.uaa.web.rest.login;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.token.ThirdAuthenticationToken;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.login.WechatOpenLoginDTO;
import com.fengyun.udf.uaa.dto.request.register.SignInWeChatDTO;
import com.fengyun.udf.uaa.service.login.LoginService;
import com.fengyun.udf.uaa.service.register.RegisterService;
import com.fengyun.udf.uaa.service.system.UserService;
import com.fengyun.udf.util.UUIDGenerator;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信公众号集成
 */
@RestController
@RequestMapping("/mp")
@Slf4j
public class WeChatMPController extends BaseResource {

    private AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new WebAuthenticationDetailsSource();

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    protected PasswordEncoder passwordEncoder;

    @Autowired
    private LoginService loginService;

    @Autowired
    private RegisterService registerService;

    @Autowired
    private WxMpService wxMpService;

    @ApiOperation(value = "微信公众号登录认证地址", notes = "微信公众号登录认证地址")
    @GetMapping("/authorize-uri")
    @AuditLog(method = "/login/wechat-open/authorize-uri", methodDescription = "微信公众号登录认证地址", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> authorizeUri(String redirectUri) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), wxMpService.oauth2buildAuthorizationUrl(redirectUri, WxConsts.OAuth2Scope.SNSAPI_BASE, UUIDGenerator.getUUID()));
    }

    @ApiOperation(value = "微信公众号jssdk config", notes = "微信公众号jssdk config")
    @GetMapping("/jssdk-config")
    @AuditLog(method = "/login/wechat-open/jssdk-config", methodDescription = "微信公众号jssdk config", httpMethod = "POST")
    public ResponseEntity<ReturnResultDTO> jssdkConfig(String url) throws WxErrorException {
        WxJsapiSignature wxJsapiSignature = wxMpService.createJsapiSignature(url);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), wxJsapiSignature);
    }

    @PostMapping("/wechat-open/login")
    @CustomBaseException
    @ApiOperation(value = "微信公众号登录系统", notes = "微信公众号登录系统")
    @AuditLog(method = "/login/wechat-open/login", methodDescription = "微信公众号登录系统", httpMethod = "POST", storageMode = {"mysql"})
    public ResponseEntity<ReturnResultDTO> login(HttpServletRequest request, @RequestBody WechatOpenLoginDTO openLoginDTO
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        String openId = null;
        if (StringUtils.isNotBlank(openLoginDTO.getCode()) && StringUtils.isNotBlank(openLoginDTO.getState())) {
            try {
                WxMpOAuth2AccessToken wxMpOAuth2AccessToken = wxMpService.oauth2getAccessToken(openLoginDTO.getCode());
                openId = wxMpOAuth2AccessToken.getOpenId();
            } catch (WxErrorException e) {
                log.error(e.getMessage(), e);
                throw new ValidationException("error", "微信认证失败");
            }
            User user = userService.findByOpenIdAndType(openId, "WECHAT");
            if (user == null) {
                return prepareReturnResult(ReturnCode.SUCCESS.getCode(), openId);
            }
        } else if (StringUtils.isNotBlank(openLoginDTO.getUsername())
                && StringUtils.isNotBlank(openLoginDTO.getPassword())
                && StringUtils.isNotBlank(openLoginDTO.getOpenId())) {
            User user = null;
            openId = openLoginDTO.getOpenId();
            List<String> userTypes = new ArrayList<>();
            userTypes.add(UserType.member.name());
            user = userService.findUserByLoginNameOrTel(openLoginDTO.getUsername(), userTypes);

            if (user == null) {
                throw new ValidationException("username", "error.login.userNotFound");
            }
            if (!passwordEncoder.matches(openLoginDTO.getPassword(), user.getPassword())) {
                throw new ValidationException("password", "用户名或密码错误");
            }
            userService.bindThird(user.getId(), openId, "WECHAT");
        }

        if (StringUtils.isBlank(openId)) {
            throw new ValidationException("error", "参数错误，登录失败");
        }

        try {
            ThirdAuthenticationToken authRequest = new ThirdAuthenticationToken(openId, "WECHAT", "");
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), loginService.getAccessTokenMp(authentication, clientType, openId));
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "error.login.client");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }


    @PostMapping("/wechat-open/registry")
    @CustomBaseException
    @ApiOperation(value = "微信公众号注册", notes = "微信公众号注册")
    @AuditLog(method = "/login/wechat-open/registry", methodDescription = "微信公众号注册", httpMethod = "POST", storageMode = {"mysql"})
    public ResponseEntity<ReturnResultDTO> login(HttpServletRequest request, @RequestBody SignInWeChatDTO signInWeChatDTO
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        String openId = signInWeChatDTO.getOpenId();
        String[] userInfo = registerService.signInUser(signInWeChatDTO);
        userService.bindThird(userInfo[0], openId, "WECHAT");
        try {
            ThirdAuthenticationToken authRequest = new ThirdAuthenticationToken(openId, "WECHAT", "");
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), loginService.getAccessTokenMp(authentication, clientType, openId));
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "error.login.client");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }
}
