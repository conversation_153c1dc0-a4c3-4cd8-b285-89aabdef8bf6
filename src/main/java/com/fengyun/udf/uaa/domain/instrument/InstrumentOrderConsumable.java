package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 预约使用耗材
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
@TableName("in_instrument_order_consumable")
public class InstrumentOrderConsumable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 预约id
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 耗材id
     */
    @TableField("consumable_id")
    private String consumableId;

    /**
     * 使用数量
     */
    @TableField("num")
    private Integer num;


}
