package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 2022/10/20
 * 合同详情
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ProjectReceiptListDTO extends AbstractDTO implements Serializable {

    private static final long serialVersionUID = 1L;

     @ApiModelProperty(value = "项目id")
     private String id;

     @ApiModelProperty(value = "审核状态")
     @DictData(target = "auditStatusStr", codeType = "PROJECT_MATERIAL_STATUS_BACK")
     private String auditStatus;
     private String auditStatusStr;

     @ApiModelProperty(value = "提交日期")
     @JsonFormat(pattern="yyyy-MM-dd HH:mm" ,timezone="GMT+8")
     private ZonedDateTime submitDate;

     @ApiModelProperty("项目名称")
     private String subjectName;

     @ApiModelProperty(value = "项目编号")
     private String projectNo;

     @ApiModelProperty("承担单位")
     private String responsibleName;

     @ApiModelProperty(value = "项目状态")
     @DictData(target = "projectStatusStr",codeType = "NEW_PROJECT_STATUS")
     private String projectStatus;
     private String projectStatusStr;

     @ApiModelProperty(value = "标题")
     private String declareName;

     @ApiModelProperty(value = "申报类别")
     @DictData(target = "declareTypeStr", codeType = "DECLARE_TYPE_BAK")
     private String declareType;
     private String declareTypeStr;

     @ApiModelProperty(value = "收据分类,第一次-1,第二次-2,第三次-3")
     @DictData(target = "categoryName", codeType = "RECEIPT_CATEGORY")
     private String category;

     @ApiModelProperty(value = "收据分类,第一次-1,第二次-2,第三次-3")
     private String categoryName;

}
