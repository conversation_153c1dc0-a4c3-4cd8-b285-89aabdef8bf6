package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
@TableName("t_role")
public class Role extends BaseDomain {
    @TableId("ID")
    private String id;

    @TableField("ROLE")
    private String role;

    @TableField("ROLE_NAME")
    private String roleName;

    @TableField("DESCRIPTION")
    private String roleDesc;

    @TableField("IS_SYS")
    private String isSys;

    @TableField("ROLE_TYPE")
    private String roleType;

    @TableField("IS_DEFAULT")
    private String isDefault;
}
