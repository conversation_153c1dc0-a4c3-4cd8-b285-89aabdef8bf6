package com.fengyun.udf.uaa.web.rest.currentuser;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.response.system.CurrentUserDetailDTO;
import com.fengyun.udf.uaa.dto.response.system.CurrentUserEnDTO;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by wangg on 2019/7/17.
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Api(value = "当前用户信息", tags = "当前用户信息")
public class CurrentUserResource extends BaseResource {

    @Autowired
    private UserService userService;

    @GetMapping("/current-user")
    @CustomBaseException
    @AuditLog(method = "/api/current-user", methodDescription = "查询当前用户信息", httpMethod = "GET")
    @ApiOperation(value = "查询当前用户信息")
    public ResponseEntity<ReturnResultDTO<CurrentUserDetailDTO>> getCurrentUser() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), userService.getCurrentUser());
    }

}
