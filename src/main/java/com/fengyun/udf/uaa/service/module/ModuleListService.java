package com.fengyun.udf.uaa.service.module;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.module.ModuleList;
import com.fengyun.udf.uaa.dto.request.module.SaveModuleListDto;
import com.fengyun.udf.uaa.dto.response.module.ModuleListDetailDto;
import com.fengyun.udf.uaa.dto.response.module.ModuleListShowDto;
import com.fengyun.udf.uaa.dto.response.module.ModuleListTreeDto;
import com.fengyun.udf.uaa.mapper.module.ModuleListMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.system.DictService;
import com.fengyun.udf.uaa.util.BeanUtils;
import com.fengyun.udf.util.TreeBuilder;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/17 11:05
 * @Version 1.0
 */

@Service
public class ModuleListService extends BaseService {

    @Autowired
    private ModuleListMapper moduleListMapper;

    public void save(SaveModuleListDto dto) {

        boolean create = false;
        ModuleList moduleList = moduleListMapper.selectById(dto.getId());
        if (moduleList == null) {
            create = true;
        }
        if (create) {
            List<ModuleList> moduleLists = moduleListMapper.selectList(new LambdaQueryWrapper<ModuleList>()
                    .eq(ModuleList::getModuleName, dto.getModuleName()));
            if (moduleLists.size() > 0) {
                throw new ValidationException("error", "该模块名已存在");
            }
            moduleList = new ModuleList();
            BeanUtils.copyProperties(dto, moduleList);
            moduleList.setId(UUIDGenerator.getUUID());
            moduleList.create(SecurityUtils.getUserId());
            moduleListMapper.insert(moduleList);
        } else {
            dto.setParentId(null);
            BeanUtils.copyPropertiesExcludeNull(dto, moduleList);
            moduleList.update(SecurityUtils.getUserId());
            moduleListMapper.updateById(moduleList);
        }
        dictCacheClient.addDict(Constants.MODULE_NAME, moduleList.getId(), dto.getModuleName());
    }

    public ModuleListDetailDto detail(String id) {
        ModuleList moduleList = moduleListMapper.selectById(id);
        if (moduleList == null) {
            throw new ValidationException("error", "该模块不存在");
        }
        ModuleListDetailDto moduleListDetailDto = new ModuleListDetailDto();
        BeanUtils.copyProperties(moduleList, moduleListDetailDto);
        return moduleListDetailDto;
    }

    public void delete(String id) {
        List<ModuleList> moduleLists = moduleListMapper.selectList(new LambdaQueryWrapper<ModuleList>()
                .eq(ModuleList::getParentId, id));
        if (moduleLists.size() > 0) {
            throw new ValidationException("error", "该模块下存在子模块,无法删除");
        }
        moduleListMapper.deleteById(id);
    }

    public List<ModuleListTreeDto> listTree(String isShowInNav) {
        List<ModuleListTreeDto> modules = moduleListMapper.selectListTree(isShowInNav);
        modules = (List<ModuleListTreeDto>) TreeBuilder.buildListToTree(modules);
        return modules;
    }

    public List<ModuleListShowDto> cascade(String parentId) {
        return moduleListMapper.selectListShow(parentId);
    }

    public void initModuleNameCache() {
        moduleListMapper.selectList(new LambdaQueryWrapper<ModuleList>()
                .select(ModuleList::getModuleName, ModuleList::getId)).forEach(item ->
            dictCacheClient.addDict(Constants.MODULE_NAME, item.getId(), item.getModuleName())
        );
    }
}
