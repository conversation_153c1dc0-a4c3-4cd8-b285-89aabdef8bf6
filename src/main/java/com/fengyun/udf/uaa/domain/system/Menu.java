package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
@TableName("t_menu")
@NoArgsConstructor
@AllArgsConstructor
public class Menu {
    @TableId("ID")
    private String id;

    @TableField("PARENT_ID")
    private String parentId;

    @TableField("NAME")
    private String menuName;

    @TableField("TYPE")
    private String menuType;

    @TableField("ICON")
    private String menuIcon;

    @TableField("COMPONENT")
    private String menuComponent;

    @TableField("HREF")
    private String menuHref;

    @TableField("PERMISSION")
    private String menuPermission;

    @TableField("SEQ")
    private Integer menuSort;

    @TableField("IS_VISIBLE")
    private String isVisible;

    @TableField("STATUS")
    private String status;

    @TableField("IS_OPERATION")
    private String isOperation;

    @TableField("APP_ID")
    private String appId;

    @TableField("MENU_BELONG")
    private String menuBelong;

    @TableField("CLIENT_TYPE")
    private String clientType;
}
