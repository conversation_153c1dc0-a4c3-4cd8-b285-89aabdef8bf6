package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 支出预算
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_spending_budget")
@Api(hidden = true)
public class SpendingBudget extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableField("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "中心支持")
    @TableField("all_support")
    private String allSupport;

    @ApiModelProperty(value = "备注")
    @TableField("all_mark")
    private String allMark;

    @ApiModelProperty(value = "设备购置预算")
    @TableField("equipment_purchase_budget")
    private Double equipmentPurchaseBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("equipment_purchase_support")
    private Double equipmentPurchaseSupport;

    @ApiModelProperty(value = "备注")
    @TableField("equipment_purchase_mark")
    private String equipmentPurchaseMark;

    @ApiModelProperty(value = "设备试制预算")
    @TableField("equipment_trial_budget")
    private Double equipmentTrialBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("equipment_trial_support")
    private Double equipmentTrialSupport;

    @ApiModelProperty(value = "备注")
    @TableField("equipment_trial_mark")
    private String equipmentTrialMark;

    @ApiModelProperty(value = "设备改造预算")
    @TableField("equipment_modification_budget")
    private Double equipmentModificationBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("equipment_modification_support")
    private Double equipmentModificationSupport;

    @ApiModelProperty(value = "备注")
    @TableField("equipment_modification_mark")
    private String equipmentModificationMark;

    @ApiModelProperty(value = "材料费")
    @TableField("material_fee_budget")
    private Double materialFeeBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("material_fee_support")
    private Double materialFeeSupport;

    @ApiModelProperty(value = "备注")
    @TableField("material_fee_mark")
    private String materialFeeMark;

    @ApiModelProperty(value = "差旅费")
    @TableField("travel_expenses_budget")
    private Double travelExpensesBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("travel_expenses_support")
    private Double travelExpensesSupport;

    @ApiModelProperty(value = "备注")
    @TableField("travel_expenses_mark")
    private String travelExpensesMark;

    @ApiModelProperty(value = "劳务费")
    @TableField("labor_fee_budget")
    private Double laborFeeBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("labor_fee_support")
    private Double laborFeeSupport;

    @ApiModelProperty(value = "备注")
    @TableField("labor_fee_mark")
    private String laborFeeMark;

    @ApiModelProperty(value = "其他支出")
    @TableField("other_budget")
    private Double otherBudget;

    @ApiModelProperty(value = "中心支持")
    @TableField("other_support")
    private Double otherSupport;

    @ApiModelProperty(value = "备注")
    @TableField("other_mark")
    private String otherMark;

    @ApiModelProperty(value = "绩效支出")
    @TableField("performance_pay_budget")
    private Double performancePayBudget;

    @ApiModelProperty(value = "中心支出")
    @TableField("performance_pay_support")
    private Double performancePaySupport;

    @ApiModelProperty(value = "备注")
    @TableField("performance_pay_mark")
    private String performancePayMark;

    @ApiModelProperty(value = "备注")
    @TableField("manage_pay_budget")
    private Double managePayBudget;

    @ApiModelProperty(value = "备注")
    @TableField("manage_pay_support")
    private Double managePaySupport;

    @ApiModelProperty(value = "备注")
    @TableField("manage_pay_mark")
    private String managePayMark;

}
