package com.fengyun.udf.uaa.dto.response.instrument;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class InstrumentOrderReceiptDetailDTO extends AbstractDTO {

    @ApiModelProperty("预约id")
    private Integer id;

    @ApiModelProperty("交款单位")
    private String payCompany;

    @ApiModelProperty("收款事由")
    private String payReason;

    @ApiModelProperty("收款方式,INSTRUMENT_ORDER_RECEIPT_PAY_TYPE")
    @DictData(target = "payTypeStr",codeType = "INSTRUMENT_ORDER_RECEIPT_PAY_TYPE")
    private String payType;
    private String payTypeStr;

    @ApiModelProperty("收款时间")
    private String payDate;

    @ApiModelProperty("收款金额（元）")
    private Double payAmount;

    @ApiModelProperty("账户类型")
    private String accountType;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("联系方式")
    private String contactTel;

    @ApiModelProperty("收据文件")
    private String receiptFile;

    @ApiModelProperty("盖章收据")
    private String receiptStampFile;

    @ApiModelProperty("发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;
}
