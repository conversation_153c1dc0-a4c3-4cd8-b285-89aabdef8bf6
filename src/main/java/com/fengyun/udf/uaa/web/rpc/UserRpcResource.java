package com.fengyun.udf.uaa.web.rpc;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 2022/11/7
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@Api(hidden = true)
@RequiredArgsConstructor
public class UserRpcResource {

    private final DeclareEntService declareEntService;

    @GetMapping("/rpc/temp/declare/ent/status/fix")
    @AuditLog(method = "rpc/declare/ent/status/fix", methodDescription = "项目进度手动推进(临时", httpMethod = "GET")
    public void fixStatus() {
        declareEntService.fixStatus();
    }
}
