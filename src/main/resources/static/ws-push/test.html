<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" />
    <title>Spring Boot+WebSocket+广播式</title>
    <script>
        var stompClient = null;

        function setConnected(connected) {
            document.getElementById('connect').disabled = connected;
            document.getElementById('disconnect').disabled = !connected;
            document.getElementById('conversationDiv').style.visibility = connected ? 'visible' : 'hidden';
            $('#response').html();
        }

        function connect() {
            var socket = new SockJS('/ws-push/endpoint-receive'); //1连接SockJS的endpoint，与后台代码中注册的endpoint要一样。
            stompClient = Stomp.over(socket);//2创建STOMP协议的webSocket客户端。
            stompClient.connect({}, function(frame) {//3连接webSocket的服务端。
                setConnected(true);
                console.log('开始进行连接Connected: ' + frame);
            });
        }


        function disconnect() {
            console.log("开始");
            if (stompClient != null) {
                stompClient.disconnect();
            }
            setConnected(false);
            console.log("Disconnected");
        }

        function sendName() {
            var name = $('#name').val();
            //通过stompClient.send（）向地址为"/welcome"的服务器地址发起请求，与@MessageMapping里的地址对应。因为我们配置了registry.setApplicationDestinationPrefixes(Constant.WEBSOCKETPATHPERFIX);所以需要增加前缀/ws-push/
            stompClient.send("/ws-push/push", {}, JSON.stringify([{ 'behaviorDesc': name }]));
        }

        function showResponse(message) {
            var response = $("#response");
            response.html(message);
        }
        function showResponse1(message) {
            var response = $("#response1");
            response.html(message);
        }
    </script>
</head>
<body onload="disconnect();">
<noscript><h2 style="color: #ff0000">貌似你的浏览器不支持websocket</h2></noscript>
<div>
    <div>
        <button id="connect" onclick="connect();">连接</button>
        <button id="disconnect" disabled="disabled" onclick="disconnect();">断开连接</button>
    </div>
    <div id="conversationDiv">
        <label>输入你的名字</label><input type="text" id="name" />
        <button id="sendName" onclick="sendName();">发送</button>
        <p id="response"></p>
        <p id="response1"></p>
    </div>
</div>
<!--<script th:src="@{sockjs.min.js}"></script>
<script th:src="@{stomp.min.js}"></script>
<script th:src="@{jquery.js}"></script>-->
<script src="https://cdn.bootcss.com/sockjs-client/1.1.4/sockjs.min.js"></script>
<script src="https://cdn.bootcss.com/stomp.js/2.3.3/stomp.min.js"></script>
<script src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
</body>
</html>