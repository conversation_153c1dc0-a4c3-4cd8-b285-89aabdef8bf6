package com.fengyun.udf.uaa.web.rest.instrument;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportSendDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportListDTO;
import com.fengyun.udf.uaa.service.instrument.InstrumentOrderExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 * @since 2023-09-14
 */

@RestController
@RequestMapping("/api/customer/instrument-order-export")
@Api(value = "管理端-", tags = "管理端-")
@Validated
@Slf4j
@AllArgsConstructor
public class CustomerInstrumentOrderExportResource extends BaseResource {
    private final InstrumentOrderExportService instrumentOrderExportService;

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order-export/view", methodDescription = "详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderExportDetailDTO>> detail(@NotBlank(message = "id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderExportService.detail(id,false));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order-export", methodDescription = "列表", httpMethod = "GET")
    @ApiOperation(value = "列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderExportListDTO>>> page(Page page, InstrumentOrderExportQueryDTO dto) {
        dto.setIsSend(true);
        dto.setUserId(SecurityUtils.getUserId());
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderExportService.page(page, dto));
    }


}
