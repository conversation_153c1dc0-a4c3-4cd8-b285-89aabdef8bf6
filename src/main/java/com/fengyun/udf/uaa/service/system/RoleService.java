package com.fengyun.udf.uaa.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Tables;
import com.fengyun.udf.uaa.domain.system.Role;
import com.fengyun.udf.uaa.domain.system.RoleMenu;
import com.fengyun.udf.uaa.domain.system.UserRole;
import com.fengyun.udf.uaa.dto.request.system.CreateRoleDTO;
import com.fengyun.udf.uaa.dto.request.system.SearchRoleDTO;
import com.fengyun.udf.uaa.dto.request.system.UpdateRoleDTO;
import com.fengyun.udf.uaa.dto.response.system.RoleDTO;
import com.fengyun.udf.uaa.dto.response.system.RoleListDTO;
import com.fengyun.udf.uaa.mapper.system.RoleMapper;
import com.fengyun.udf.uaa.mapper.system.RoleMenuMapper;
import com.fengyun.udf.uaa.mapper.system.UserRoleMapper;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.fengyun.udf.uaa.constant.Constants.NO;

/**
 * Created by wangg on 2019/7/5.
 */
@Service
@Transactional
public class RoleService {
    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RoleMenuMapper roleMenuMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    /**
     * 根据传入的角色主键， 验证角色是否存在
     *
     * @param roleId 角色主键
     * @return <code>boolean</code>
     */
    public boolean roleIsExisting(String roleId) {
        long count = roleMapper.selectCount(new QueryWrapper<Role>().eq(Tables.T_ROLE.ID.name(), roleId));
        return count > 1;
    }

    public boolean roleIsExisting(List<String> roleIds) {
        long count = roleMapper.selectCount(new QueryWrapper<Role>().in(Tables.T_ROLE.ID.name(), roleIds));
        return count == roleIds.size();
    }


    /**
     * 根据传入的角色主键， 获取角色
     *
     * @param id 角色主键
     * @return
     */
    public Role getRole(final String id) {
        return roleMapper.selectById(id);
    }


    /**
     * 新增角色
     *
     * @param createRoleDTO 新增角色对象参数
     * @see {@link com.fengyun.udf.uaa.dto.request.system.CreateRoleDTO}
     */
    public void addRole(CreateRoleDTO createRoleDTO) {
        Role role = new Role();
        role.setId(UUIDGenerator.getUUID());
        role.setRole(createRoleDTO.getRole());
        role.setRoleName(createRoleDTO.getRoleName());
        role.setRoleDesc(createRoleDTO.getRoleDescription());
        //设置非系统默认
        role.setIsSys(NO);
        role.setRoleType(createRoleDTO.getRoleType());
        role.create(SecurityUtils.getUserId());
        roleMapper.insert(role);

        //给角色添加可以访问的菜单
        for (String menuId : createRoleDTO.getMenuIds()) {
            RoleMenu roleMenu = new RoleMenu();
            roleMenu.setId(UUIDGenerator.getUUID());
            roleMenu.setRoleId(role.getId());
            roleMenu.setMenuId(menuId);
            roleMenuMapper.insert(roleMenu);
        }
    }

    /**
     * 查询角色列表
     *
     * @param searchRoleDTO 查询条件
     * @param page          分页信息
     * @return 角色列表
     */
    public Page<RoleListDTO> searchRole(SearchRoleDTO searchRoleDTO, Page<RoleListDTO> page) {
        //查询角色列表
        List<RoleListDTO> result = roleMapper.searchRole(page, searchRoleDTO);
        //给分页添加结果集
        page.setRecords(result);
        return page;
    }

    /**
     * 查询角色详情
     *
     * @param id 角色主键
     * @return 角色详情
     */
    public RoleDTO getRoleDetail(final String id) {
        //获取角色详情
        Role role = roleMapper.selectById(id);
        //获取角色下的菜单
        QueryWrapper queryWrapper = new QueryWrapper<RoleMenu>().eq(Tables.T_ROLE_MENU.ROLE_ID.name(), id);
        List<RoleMenu> roleMenus = roleMenuMapper.selectList(queryWrapper);

        List<String> menuIds = new ArrayList<>();
        for (RoleMenu roleMenu : roleMenus) {
            menuIds.add(roleMenu.getMenuId());
        }

        //返回结果集
        RoleDTO result = new RoleDTO();
        BeanUtils.copyProperties(role, result);
        result.setMenuIds(menuIds);
        return result;
    }

    /**
     * 更新角色
     *
     * @param updateRoleDTO 更新角色参数对象
     */
    public void updateRole(UpdateRoleDTO updateRoleDTO) {
        //获取角色详情
        Role role = roleMapper.selectById(updateRoleDTO.getRoleId());
        role.setRole(updateRoleDTO.getRole());
        role.setRoleName(updateRoleDTO.getRoleName());
        role.setRoleDesc(updateRoleDTO.getRoleDescription());
        //设置非系统默认
        role.setIsSys(NO);
        role.setRoleType(updateRoleDTO.getRoleType());
        role.update(SecurityUtils.getUserId());
        roleMapper.updateById(role);

        //删除角色已存在的菜单
        QueryWrapper deleteQuery = new QueryWrapper<RoleMenu>().eq(Tables.T_ROLE_MENU.ROLE_ID.name(), role.getId());
        roleMenuMapper.delete(deleteQuery);
        //给角色添加可以访问的菜单
        for (String menuId : updateRoleDTO.getMenuIds()) {
            RoleMenu roleMenu = new RoleMenu();
            roleMenu.setId(UUIDGenerator.getUUID());
            roleMenu.setRoleId(role.getId());
            roleMenu.setMenuId(menuId);
            roleMenuMapper.insert(roleMenu);
        }
    }

    /**
     * 删除角色
     *
     * @param id 角色主键
     */
    public void deleteRole(String id) {
        //删除角色
        roleMapper.deleteById(id);

        //删除角色关联的菜单
        QueryWrapper deleteQuery = new QueryWrapper<RoleMenu>().eq(Tables.T_ROLE_MENU.ROLE_ID.name(), id);
        roleMenuMapper.delete(deleteQuery);

        //删除角色关联的用户
        deleteQuery = new QueryWrapper<UserRole>().eq(Tables.T_ROLE_MENU.ROLE_ID.name(), id);
        userRoleMapper.delete(deleteQuery);
    }

    /**
     * 获取用户角色列表
     *
     * @param userId   用户主键
     * @return 用户角色列表
     */
    public List<String> getUserRoles(String userId) {
        return roleMapper.selectRoleByUser(userId);
    }

    /**
     * 获取用户角色列表
     *
     * @param userId   用户主键
     * @return 用户角色列表
     */
    public List<Role> getUserRoleList(String userId) {
        return roleMapper.getUserRoleList(userId);
    }
}
