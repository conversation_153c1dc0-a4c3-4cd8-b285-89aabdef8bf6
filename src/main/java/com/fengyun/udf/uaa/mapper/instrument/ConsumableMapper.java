package com.fengyun.udf.uaa.mapper.instrument;

import com.fengyun.udf.uaa.domain.instrument.Consumable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.instrument.ConsumableQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.ConsumableListDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 耗材 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface ConsumableMapper extends BaseMapper<Consumable> {

    List<ConsumableListDTO> selectPage(Page page, @Param("condition")ConsumableQueryDTO dto);

}
