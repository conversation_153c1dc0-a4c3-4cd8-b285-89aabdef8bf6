package com.fengyun.udf.uaa.service.expertpanel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.declareproject.DeclareEnt;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertPanel;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertScoreHistory;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertPanelCreateDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertPanelSearchDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.RelationExpertPanelDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertPanelDTO;
import com.fengyun.udf.uaa.dto.response.system.UserDTO;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareEntMapper;
import com.fengyun.udf.uaa.mapper.expertpanel.ExpertPanelMapper;
import com.fengyun.udf.uaa.mapper.expertpanel.ExpertScoreHistoryMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;

@Service
@Transactional
public class ExpertPanelService extends BaseService {

    @Autowired
    private ExpertPanelMapper expertPanelMapper;

    @Autowired
    private DeclareEntMapper declareEntMapper;

    @Autowired
    private ExpertScoreHistoryMapper expertScoreHistoryMapper;

    /**
     * 专家团保存
     * @param dto
     */
    public void save(ExpertPanelCreateDTO dto) {
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertPanel entity = new ExpertPanel();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(UUIDGenerator.getUUID());
        entity.setStatus(Constants.EXPERT_PANEL_STATUS.EXPERT_PANEL_STATUS_0.getCode());
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        entity.setCreatedId(userId);
        entity.setUpdatedId(userId);
        entity.setCreatedDate(nowTime);
        entity.setUpdatedDate(nowTime);
        expertPanelMapper.insert(entity);
    }

    /**
     * 专家团更新
     * @param dto
     */
    public void update(ExpertPanelCreateDTO dto) {
        ExpertPanel entity = expertPanelMapper.selectById(dto.getId());
        if (entity == null) {
            throw new ValidationException("error", "专家团不存在");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        expertPanelMapper.updateById(entity);

    }

    /**
     * 列表查询
     * @param page
     * @param dto
     * @return
     */
    public Page<ExpertPanelDTO> page(Page<ExpertPanelDTO> page, ExpertPanelSearchDTO dto) {
        List<ExpertPanelDTO> list = expertPanelMapper.page(page, dto);
        if (CollectionUtils.isNotEmpty(list)) {
            for (ExpertPanelDTO expertPanelDTO : list) {
                List<String> userIds = splitParam(expertPanelDTO.getMember());
                String userNames = userMapper.searchUserNames(userIds);
                expertPanelDTO.setMemberName(userNames);
            }
        }
        page.setRecords(list);
        return page;
    }

    public List<ExpertPanelDTO> getExpertGroupList() {
        List<ExpertPanelDTO> list = expertPanelMapper.getExpertGroupList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (ExpertPanelDTO expertPanelDTO : list) {
                List<String> userIds = splitParam(expertPanelDTO.getMember());
                String userNames = userMapper.searchUserNames(userIds);
                expertPanelDTO.setMemberName(userNames);
            }
        }
        return list;
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    public ExpertPanelDTO getById(String id) {
        ExpertPanel entity = expertPanelMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "专家团不存在");
        }
        ExpertPanelDTO dto = new ExpertPanelDTO();
        BeanUtils.copyProperties(entity, dto);
        List<String> userIds = splitParam(entity.getMember());
        String userNames = userMapper.searchUserNames(userIds);
        dto.setMemberName(userNames);
        return dto;
    }

    /**
     * 删除
     * @param id
     */
    public void delete(String id) {
        expertPanelMapper.deleteById(id);
    }

    public List<UserDTO> getExpertList(){
        return userMapper.getExpertList();
    }

    public void relationExpertGroup(RelationExpertPanelDTO dto) {
        DeclareEnt declareEnt = declareEntMapper.selectById(dto.getId());
        if (declareEnt == null) {
            throw new ValidationException("error", "申报信息不存在");
        }
        declareEnt.setExpertPanelId(dto.getExpertPanelId());
        declareEntMapper.updateById(declareEnt);
        // 更新状态
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertPanel expertPanel = expertPanelMapper.selectById(dto.getExpertPanelId());
        if (expertPanel == null) {
            throw new ValidationException("error", "专家团不存在");
        }
        expertPanel.setStatus(Constants.EXPERT_PANEL_STATUS.EXPERT_PANEL_STATUS_1.getCode());
        expertPanel.setUpdatedId(SecurityUtils.getUserId());
        expertPanel.setUpdatedDate(nowTime);
        expertPanelMapper.updateById(expertPanel);
    }

    public void cancelRelationExpertGroup(String id) {
        DeclareEnt declareEnt = declareEntMapper.selectById(id);
        if (declareEnt == null) {
            throw new ValidationException("error", "申报信息不存在");
        }
        // 判断该申报是否已经有人评分过
        LambdaQueryWrapper<ExpertScoreHistory> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ExpertScoreHistory::getDeclareEntId, id);
        List<ExpertScoreHistory> histories = expertScoreHistoryMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(histories)) {
            throw new ValidationException("error", "已有专家评分，不能取消关联");
        }
        String oldExpertPanelId = declareEnt.getExpertPanelId();
        declareEnt.setExpertPanelId(null);
        declareEntMapper.updateById(declareEnt);
        // 判断之前的专家团是否有关联，没有则更新之前专家团为未关联
        if (StringUtils.isNotBlank(oldExpertPanelId)) {
            int count = declareEntMapper.getExpertGroupById(declareEnt.getId(), oldExpertPanelId);
            if (count == 0) {
                ExpertPanel expertPanel = expertPanelMapper.selectById(oldExpertPanelId);
                if (expertPanel == null) {
                    throw new ValidationException("error", "专家团不存在");
                }
                expertPanel.setStatus(Constants.EXPERT_PANEL_STATUS.EXPERT_PANEL_STATUS_0.getCode());
                expertPanelMapper.updateById(expertPanel);
            }
        }
    }

    public void secondRelationExpertGroup(RelationExpertPanelDTO dto) {
        DeclareEnt declareEnt = declareEntMapper.selectById(dto.getId());
        if (declareEnt == null) {
            throw new ValidationException("error", "申报信息不存在");
        }
        ExpertPanel expertPanel = expertPanelMapper.selectById(dto.getExpertPanelId());
        if (expertPanel == null) {
            throw new ValidationException("error", "专家团不存在");
        }
        String members = expertPanel.getMember();
        if(StringUtils.isEmpty(members)){
            throw new ValidationException("error", "专家团不存在专家");
        }
        String [] membersArr = members.split(",");
        for(String member:membersArr){
            // 如果专家A参与了B项目的第一次评审,那这个专家A不能参加C项目的第二次评审
           Integer integer = declareEntMapper.getIsRelationFirst(member);
           if(integer > 0){
               throw new ValidationException("error", "此专家已参加过第一轮评审！");
           }
        }

//        declareEnt.setExpertPanelId(dto.getExpertPanelId());
        declareEnt.setSecondExpertPanelId(dto.getExpertPanelId());
        declareEntMapper.updateById(declareEnt);
        // 更新状态
        ZonedDateTime nowTime = ZonedDateTime.now();

        expertPanel.setStatus(Constants.EXPERT_PANEL_STATUS.EXPERT_PANEL_STATUS_1.getCode());
        expertPanel.setUpdatedId(SecurityUtils.getUserId());
        expertPanel.setUpdatedDate(nowTime);
        expertPanelMapper.updateById(expertPanel);
    }

    public void cancelSecondRelationExpertGroup(String id) {
        DeclareEnt declareEnt = declareEntMapper.selectById(id);
        if (declareEnt == null) {
            throw new ValidationException("error", "申报信息不存在");
        }
        // 判断该申报是否已经有人评分过
        LambdaQueryWrapper<ExpertScoreHistory> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ExpertScoreHistory::getDeclareEntId, id);
//        List<ExpertScoreHistory> histories = expertScoreHistoryMapper.selectList(wrapper);
//        if (CollectionUtils.isNotEmpty(histories)) {
//            throw new ValidationException("error", "已有专家评分，不能取消关联");
//        }
        String oldExpertPanelId = declareEnt.getSecondExpertPanelId();
        declareEnt.setSecondExpertPanelId(null);
        declareEntMapper.updateById(declareEnt);
        // 判断之前的专家团是否有关联，没有则更新之前专家团为未关联
        if (StringUtils.isNotBlank(oldExpertPanelId)) {
            int count = declareEntMapper.getSecondExpertGroupById(declareEnt.getId(), oldExpertPanelId);
            if (count == 0) {
                ExpertPanel expertPanel = expertPanelMapper.selectById(oldExpertPanelId);
                if (expertPanel == null) {
                    throw new ValidationException("error", "专家团不存在");
                }
                expertPanel.setStatus(Constants.EXPERT_PANEL_STATUS.EXPERT_PANEL_STATUS_0.getCode());
                expertPanelMapper.updateById(expertPanel);
            }
        }
    }

}
