package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by wangg on 2019/7/5.
 */
@Data
@TableName("t_user_role")
public class UserRole {

    @TableId("ID")
    private String id;

    @TableField("USER_ID")
    private String userId;

    @TableField("ROLE_ID")
    private String roleId;
}
