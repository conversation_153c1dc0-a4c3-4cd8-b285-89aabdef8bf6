package com.fengyun.udf.uaa.constant;

import com.fengyun.udf.exception.ValidationException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 2024/7/17
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class DeclareCons {

    @Getter
    @AllArgsConstructor
    public enum MIDDLE_INSPECTION_STATUS {
        // 保存
        SAVE("0", "保存"),
        SUBMIT("1", "审核中"),
        BACK("2", "退回修改"),
        PASS("3", "审批通过"),
        REJECT("4", "审批不通过");
        /**
         * code
         */
        private final String code;
        /**
         * 描述
         */
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum MIDDLE_INSPECTION_AUDIT_OPERATION {
        // 退回修改
        BACK("back", "退回修改", MIDDLE_INSPECTION_STATUS.BACK.code),
        PASS("pass", "审批通过", MIDDLE_INSPECTION_STATUS.PASS.code),
        REJECT("reject", "审批不通过", MIDDLE_INSPECTION_STATUS.REJECT.code);
        /**
         * code
         */
        private final String code;
        /**
         * 描述
         */
        private final String desc;
        /**
         * 对应状态code
         */
        private final String statusCode;

        public static MIDDLE_INSPECTION_AUDIT_OPERATION getByCode(String code) {
            return Arrays.stream(MIDDLE_INSPECTION_AUDIT_OPERATION.values())
                    .filter(x -> x.getCode().equals(code))
                    .findFirst()
                    .orElseThrow(() -> (new ValidationException("中期检查", "审核操作错误")));
        }
    }

    @Getter
    @AllArgsConstructor
    public enum RECEIPT_CATEGORY {
        // 保存
        FIRST("1", "第一次"),
        SECOND("2", "第二次"),
        THIRD("3", "第三次"),
        ;
        /**
         * code
         */
        private final String code;
        /**
         * 描述
         */
        private final String desc;

        public static RECEIPT_CATEGORY getByCode(String code) {
            return Arrays.stream(RECEIPT_CATEGORY.values())
                    .filter(x -> x.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
    }
}
