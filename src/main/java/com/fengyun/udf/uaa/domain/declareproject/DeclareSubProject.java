package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * Created by Administrator on 2021/3/11.
 */
@TableName("t_declare_sub_project")
@Data
public class DeclareSubProject {

    private String id;

    @TableField("declare_id")
    private String declareId;

    @TableField("big_type_id")
    private String bigTypeId;

    @TableField("small_type_id")
    private String smallTypeId;

    @TableField("flow_id")
    private String flowId;

    @TableField("file_content")
    private String fileContent;

    @TableField("created_id")
    private String createdId;

    @TableField("updated_id")
    private String updatedId;

    @TableField("created_date")
    private ZonedDateTime createdDate;

    @TableField("updated_date")
    private ZonedDateTime updatedDate;

}