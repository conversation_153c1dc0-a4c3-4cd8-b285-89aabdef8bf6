package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_platform")
public class Platform extends BaseDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 变量名
     */
    @TableField("name")
    private String name;

    /**
     * 变量标题
     */
    @TableField("title")
    private String title;

    /**
     * 变量提示
     */
    @TableField("tip")
    private String tip;

    /**
     * 变量类型
     */
    @TableField("type")
    private String type;

    /**
     * 变量值
     */
    @TableField("value")
    private String value;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否必填
     */
    @TableField("required")
    private Integer required;

}
