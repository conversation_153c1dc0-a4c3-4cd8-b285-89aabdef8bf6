<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.flowform.ShenbaoMapper">

    <select id="selectByIdDetail" resultType="com.fengyun.udf.uaa.dto.response.flowform.ShenbaoDetailDto">
        SELECT
            s.id ID,
            p.declare_name declareName,
            s.`name` `name`,
            u.TEL tel,
            s.files files,
            s.CREATED_DATE createdDate
        FROM
            flow_form_shenbao s
        LEFT JOIN
            t_user u ON u.ID = s.CREATED_ID
        LEFT JOIN
            t_declare_project p ON s.project_id = p.id
        WHERE
            s.id = #{id}
    </select>
    <select id="selectPageList" resultType="com.fengyun.udf.uaa.dto.response.flowform.ShenbaoDetailDto">
        SELECT
            s.id ID,
            p.declare_name declareName,
            s.`name` `name`,
            u.TEL tel,
            s.CREATED_DATE createdDate
        FROM
            flow_form_shenbao s
        LEFT JOIN
            t_user u ON u.ID = s.CREATED_ID
        LEFT JOIN
            t_declare_project p ON s.project_id = p.id
        WHERE
            1=1
            <if test="dto.key != null and dto.key != ''">
            and p.declare_name like CONCAT('%',#{dto.key},'%')
            </if>
    </select>
</mapper>
