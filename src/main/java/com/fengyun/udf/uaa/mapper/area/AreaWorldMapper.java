package com.fengyun.udf.uaa.mapper.area;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.domain.area.AreaWorld;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AreaWorldMapper extends BaseMapper<AreaWorld> {

    List<AreaWorld> findAllByLevelAndParentIdAndStatusNotOrderBySequenceAsc(@Param("level") int level, @Param("parentId") String parentId, @Param("status") String status);

    List<AreaWorld> findAllByLevelAndStatusNotOrderBySequenceAsc(@Param("level") int level, @Param("status") String status);

    List<AreaWorld> selectByPrimaryKeys(List<String> ids);

    List<AreaWorld> findAll();
}