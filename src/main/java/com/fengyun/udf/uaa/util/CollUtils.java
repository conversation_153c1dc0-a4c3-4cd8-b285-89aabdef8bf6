package com.fengyun.udf.uaa.util;

import com.alibaba.fastjson.JSON;

import java.util.List;

/**
 * 2022/10/20
 * Collection工具类
 * <AUTHOR>
 * @version 1.0.0
 */
public class CollUtils {

    /**
     * 从List<A> copy到List<B>
     * @param list List<B>
     * @param clazz B
     * @return List<B>
     */
    public static <T> List<T> copyList(List<?> list, Class<T> clazz){
        String old = JSON.toJSONString(list);
        return JSON.parseArray(old, clazz);
    }
}
