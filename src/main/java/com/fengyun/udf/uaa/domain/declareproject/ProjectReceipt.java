package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_project_receipt")
@ApiModel(value = "ProjectReceipt对象", description = "项目收据对象")
public class ProjectReceipt extends ProjectMaterial {

    @ApiModelProperty(value = "收据编号")
    @TableField("receipt_no")
    private Long receiptNo;

    @ApiModelProperty(value = "收据分类")
    @TableField("category")
    private String category;

    @ApiModelProperty(value = "交款单位")
    @TableField("company")
    private String company;

    @ApiModelProperty(value = "收款事由")
    @TableField("reason")
    private String reason;

    @ApiModelProperty(value = "收款时间")
    @TableField("date")
    private LocalDate date;

    @ApiModelProperty(value = "收款金额(元)")
    @TableField("amount")
    private Double amount;

    @ApiModelProperty(value = "联系人")
    @TableField("contacts")
    private String contacts;

    @ApiModelProperty(value = "联系电话")
    @TableField("contact_num")
    private String contactNum;

    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty(value = "开户名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "开户行")
    @TableField("bank")
    private String bank;

    @ApiModelProperty(value = "银行账号")
    @TableField("account")
    private String account;

    @ApiModelProperty(value = "账户类型")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "收据模板")
    @TableField("template")
    private String template;

    @ApiModelProperty(value = "收据")
    @TableField("receipt")
    private String receipt;

}
