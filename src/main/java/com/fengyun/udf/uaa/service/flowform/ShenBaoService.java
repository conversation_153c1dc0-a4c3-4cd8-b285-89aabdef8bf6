package com.fengyun.udf.uaa.service.flowform;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.domain.flowform.Shenbao;
import com.fengyun.udf.uaa.dto.request.flowform.CreateShenBaoDto;
import com.fengyun.udf.uaa.dto.request.flowform.QueryShenbaoDto;
import com.fengyun.udf.uaa.dto.response.flowform.ShenbaoDetailDto;
import com.fengyun.udf.uaa.mapper.flowform.ShenbaoMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class ShenBaoService extends BaseService {

    @Autowired
    private ShenbaoMapper shenbaoMapper;

    public void save(CreateShenBaoDto dto) {
        String userId = SecurityUtils.getUserId();
        boolean isCreate = false;
        Shenbao shenbao = shenbaoMapper.selectById(dto.getId());
        if (shenbao == null) {
            shenbao = new Shenbao();
            isCreate = true;
        }

        if (isCreate) {
            BeanUtils.copyProperties(dto, shenbao);
            shenbao.setId(UUIDGenerator.getUUID());
            shenbao.create(userId);
            shenbaoMapper.insert(shenbao);
        } else  {
            BeanUtils.copyProperties(dto, shenbao);
            shenbao.update(userId);
            shenbaoMapper.updateById(shenbao);
        }
    }

    public ShenbaoDetailDto ShenbaoDetail(String id) {
        ShenbaoDetailDto shenbao = shenbaoMapper.selectByIdDetail(id);
        if (shenbao == null ) {
            throw new ValidationException("error", "该申报信息不存在,请稍后重试");
        }
        return shenbao;
    }

    public Page page(Page page, QueryShenbaoDto dto) {
        List<ShenbaoDetailDto> list = shenbaoMapper.selectPageList(page, dto);
        page.setRecords(list);
        return page;
    }

}
