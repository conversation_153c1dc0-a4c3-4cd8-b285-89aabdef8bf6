package com.fengyun.udf.uaa.web.rest.logout;

import cn.hutool.core.map.MapUtil;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.request.RefreshTokenDto;
import com.fengyun.udf.uaa.service.login.LoginService;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.web.bind.annotation.*;

/**
 * Created by wangg on 2019/7/12.
 */
@RestController
@RequestMapping("/api")
@Api(value = "登出系统", tags = "登出系统")
public class LogoutResource extends BaseResource {

    private static final String BASIC_ = "Basic ";

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private ClientDetailsService clientDetailsService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Autowired
    private LoginService loginService;

    @Autowired
    private UserService userService;

    @PostMapping("/logout")
    @CustomBaseException
    @ApiOperation(value = "登出系统", notes = "登出系统")
    @AuditLog(method = "/api/logout", methodDescription = "登出系统", httpMethod = "POST", storageMode = {"file", "mysql"})
    //@PreAuthorize("@pms.hasPermission('')")
    public ResponseEntity<ReturnResultDTO> logout(@RequestHeader(value = HttpHeaders.AUTHORIZATION, required = false) String token) {
//        if (StringUtils.isNotBlank(token)) {
//            String tokenValue = token.replace(OAuth2AccessToken.BEARER_TYPE, StrUtil.EMPTY).trim();
//            OAuth2AccessToken accessToken = tokenStore.readAccessToken(tokenValue);
//            if (accessToken != null && StringUtils.isNotBlank(accessToken.getValue())) {
//                tokenStore.removeAccessToken(accessToken);
//                OAuth2RefreshToken refreshToken = accessToken.getRefreshToken();
//                tokenStore.removeRefreshToken(refreshToken);
//            }
//        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/logout-mp")
    @CustomBaseException
    @ApiOperation(value = "公众号登出系统", notes = "公众号登出系统")
    @AuditLog(method = "/api/logout-mp", methodDescription = "公众号登出系统", httpMethod = "POST", storageMode = {"file", "mysql"})
    //@PreAuthorize("@pms.hasPermission('')")
    public ResponseEntity<ReturnResultDTO> logoutMp(@RequestHeader(value = HttpHeaders.AUTHORIZATION, required = false) String token) {
        if (StringUtils.isNotBlank(token)) {
            String userId = SecurityUtils.getUserId();
            if (StringUtils.isNotBlank(userId)) {
                userService.removeThird(userId, "WECHAT");
            }
//            String tokenValue = token.replace(OAuth2AccessToken.BEARER_TYPE, StrUtil.EMPTY).trim();
//            OAuth2AccessToken accessToken = tokenStore.readAccessToken(tokenValue);
//            if (accessToken != null && StringUtils.isNotBlank(accessToken.getValue())) {
//                tokenStore.removeAccessToken(accessToken);
//                OAuth2RefreshToken refreshToken = accessToken.getRefreshToken();
//                tokenStore.removeRefreshToken(refreshToken);
//            }
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/refresh-token")
    @ApiOperation(value = "刷新token", notes = "刷新token")
    @AuditLog(method = "/api/refresh-token", methodDescription = "刷新token", httpMethod = "POST", storageMode = {"file", "mysql"})
    public ResponseEntity<ReturnResultDTO> refreshToken(@RequestHeader(value = "clientType", required = false) String clientType
            , @RequestBody RefreshTokenDto dto) {
        ClientDetails client = loginService.getClient(clientType);
        if (client == null) {
            throw new ValidationException("error", "无client信息");
        }

        TokenRequest tokenRequest = new TokenRequest(MapUtil.newHashMap(), client.getClientId(), client.getScope(), "refresh_token");

        OAuth2AccessToken token = authorizationServerTokenServices.refreshAccessToken(dto.getRefreshToken(), tokenRequest);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), token);
    }

    @PostMapping("/token-check")
    @ApiOperation(value = "token验证是否过期", notes = "token验证是否过期")
    public ResponseEntity<ReturnResultDTO> checkToken() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
}
