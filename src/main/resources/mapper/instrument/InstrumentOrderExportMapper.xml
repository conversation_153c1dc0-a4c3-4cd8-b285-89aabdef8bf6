<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderExportMapper">

    <select id="selectPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportListDTO">
        select
            oe.id,
            oe.user_id,
            oe.user_type,
            oe.user_name,
            oe.amount,
            oe.is_send,
            oe.send_time,
            oe.is_confirm,
            oe.confirm_time,
            oe.excel_file,
            oe.created_date,
            oe.contact_tel,
            oe.contact_email,
            u.tel
        from
        in_instrument_order_export oe left join t_user u on oe.user_id = u.id
        where 1=1
        <if test="condition.isSend != null">
            and oe.is_send = #{condition.isSend}
        </if>
        <if test="condition.userId != null and condition.userId != ''">
            and oe.user_id = #{condition.userId}
        </if>
        order by oe.created_date

    </select>
</mapper>
