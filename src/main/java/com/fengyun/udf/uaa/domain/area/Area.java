package com.fengyun.udf.uaa.domain.area;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("t_area")
@Data
public class Area {

    @TableId(value = "SN")
    private String sn;

    @TableField(value = "CODE")
    private String code;

    @TableField(value = "NAME")
    private String name;

    @TableField(value = "SHORT_NAME")
    private String shortName;

    @TableField(value = "LEVEL")
    private Integer level;

    @TableField(value = "TYPE")
    private String type;

    @TableField(value = "SEQUENCE")
    private String sequence;

    @TableField(value = "STATUS")
    private String status;

    @TableField(value = "PARENT_SN")
    private String parentSn;

    @TableField(value = "WORLD_ID")
    private String worldId;

}
