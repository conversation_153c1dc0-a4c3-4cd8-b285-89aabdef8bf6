package com.fengyun.udf.uaa.service.system;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Tables;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.certification.CertificationInfo;
import com.fengyun.udf.uaa.domain.system.*;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.system.*;
import com.fengyun.udf.uaa.dto.request.user.UpdateUserInfoDTO;
import com.fengyun.udf.uaa.dto.request.user.UpdateUserSubscribeDTO;
import com.fengyun.udf.uaa.dto.response.certification.ShowCertificationInfoDto;
import com.fengyun.udf.uaa.dto.response.system.*;
import com.fengyun.udf.uaa.mapper.certification.CertificationInfoMapper;
import com.fengyun.udf.uaa.mapper.system.MenuMapper;
import com.fengyun.udf.uaa.mapper.system.UserGroupMapper;
import com.fengyun.udf.uaa.mapper.system.UserRoleMapper;
import com.fengyun.udf.uaa.mapper.system.UserThirdMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.DateConverter;
import com.fengyun.udf.util.TreeBuilder;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import static com.fengyun.udf.uaa.constant.Constants.*;

/**
 * Created by wangg on 2019/7/5.
 */
@Service
@Transactional
public class UserService extends BaseService {
    @Autowired
    protected PasswordEncoder passwordEncoder;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private UserGroupMapper userGroupMapper;

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserThirdMapper userThirdMapper;

    @Autowired
    private CertificationInfoMapper certificationInfoMapper;


    public User getUserById(String id) {
        return userMapper.selectById(id);
    }

    public User getUser(String id) {
        QueryWrapper queryWrapper = new QueryWrapper<User>().eq(Tables.T_USER.ID.name(), id)
                .eq(Tables.T_USER.IS_SYS.name(), NO);

        return userMapper.selectOne(queryWrapper);
    }

    public User getEffectiveUser(String id) {
        QueryWrapper queryWrapper = new QueryWrapper<User>().eq(Tables.T_USER.ID.name(), id)
                .ne(Tables.T_USER.STATUS.name(), STATUS_DELETED);

        return userMapper.selectOne(queryWrapper);
    }

    /**
     * 根据用户主键，验证用户是否存在
     *
     * @param userId 用户主键a
     * @return
     */
    public boolean isUserExists(String userId) {
        QueryWrapper queryWrapper = new QueryWrapper<User>().eq(Tables.T_USER.ID.name(), userId)
                .eq(Tables.T_USER.IS_SYS.name(), NO);
        long count = userMapper.selectCount(queryWrapper);
        return count == 1;
    }

    /**
     * 根据登录名获取用户
     *
     * @param loginName
     * @return
     */
    public User findByLoginName(String loginName, List<String> userTypes) {
        return userMapper.selectOne(
                new QueryWrapper<User>()
                        .eq(Tables.T_USER.LOGIN_NAME.name(), loginName)
                        .eq(Tables.T_USER.STATUS.name(), STATUS_ACTIVE)
                        .in(Tables.T_USER.TYPE.name(), userTypes));
    }

    /**
     * 根据登录名手机号获取用户
     *
     * @param username
     * @param userTypes
     * @return
     */
    public User findUserByLoginNameOrTel(String username, List<String> userTypes) {
        return userMapper.findUserByLoginNameOrTel(username, userTypes);
    }

    /**
     * 根据手机号码登录
     *
     * @param tel
     * @return
     */
    public User findByTel(String tel, List<String> userTypes) {
        if(StringUtils.isBlank(tel)) {
            return null;
        }
        return userMapper.selectOne(
                new QueryWrapper<User>()
                        .eq(Tables.T_USER.TEL.name(), tel)
                        .eq(Tables.T_USER.STATUS.name(), STATUS_ACTIVE)
                        .in(Tables.T_USER.TYPE.name(), userTypes));
    }

    /**
     * 添加用户
     *
     * @param createUserDTO 添加用户参数对象
     * @see {@link com.fengyun.udf.uaa.dto.request.system.CreateUserDTO}
     */
    public void addUser(CreateUserDTO createUserDTO) {
        String tel = createUserDTO.getTel();
        List<String> userTypes;

        if (UserType.management.name().equals(createUserDTO.getUserType())) {
            userTypes = prepareOperationUserType();

        } else if (UserType.tenant.name().equals(createUserDTO.getUserType())) {
            userTypes = prepareTenantUserType();
        } else {
            throw new ValidationException("error", "账号类型不正确");
        }
        if (StringUtils.isNotEmpty(tel)) {
            User user = findByTel(tel, userTypes);
            if (user != null) {
                throw new ValidationException("error", "手机号已存在");
            }
        }
        String loginName = createUserDTO.getLoginName();
        if (StringUtils.isNotEmpty(loginName)) {
            User user = findByLoginName(loginName, userTypes);
            if (user != null) {
                throw new ValidationException("error", "登录名已存在");
            }
        }

        //添加用户
        User user = new User();
        user.setId(UUIDGenerator.getUUID());
        user.setLoginName(createUserDTO.getLoginName());
        user.setType(createUserDTO.getUserType());
        user.setPassword(passwordEncoder.encode(createUserDTO.getPassword()));
        user.setTel(createUserDTO.getTel());
        user.setNickName(createUserDTO.getNickName());
        user.setStatus(createUserDTO.getStatus());
        //运营平台添加的用户，除了preload数据，都为非系统默认用户，可以查询编辑删除
        //租户平台添加的用户，除了超级管理员，都为非系统默认用户，可以查询编辑删除
        user.setIsSys(NO);
        user.setPolicySystem(createUserDTO.getPolicySystem());
        user.create(SecurityUtils.getUserId());
        userMapper.insert(user);

        //给用户添加部门
        //        addGroup(createUserDTO.getGroupIds(), user.getTenantId(), user.getId());


        //给用户添加角色
        addRole(createUserDTO.getRoleIds(), user.getId());

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
    }

    private void addRole(List<String> roleIds, String userId) {
        if (roleIds != null && roleIds.size() > 0) {
            for (String roleId : roleIds) {
                UserRole userRole = new UserRole();
                userRole.setId(UUIDGenerator.getUUID());
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRoleMapper.insert(userRole);
            }
        }
    }

    private void addGroup(List<String> groupIds, String tenantId, String userId) {
        if (groupIds != null && groupIds.size() > 0) {
            for (String groupId : groupIds) {
                UserGroup userGroup = new UserGroup();
                userGroup.setId(UUIDGenerator.getUUID());
                userGroup.setUserId(userId);
                userGroup.setGroupId(groupId);
                userGroupMapper.insert(userGroup);
            }
        }
    }

    /**
     * 查询当前租户的用户列表
     *
     * @param dto  查询条件
     * @param page 分页信息
     * @return 用户列表
     */
    public Page<UserListDTO> searchUser(SearchUserDTO dto, Page<UserListDTO> page) {
        //用户名设置为模糊查询
        if (StringUtils.isNotBlank(dto.getLoginName())) {
            dto.setLoginName(prepareLikeContent(dto.getLoginName()));
        }
        //查询用户列表
        List<UserListDTO> result = userMapper.searchUser(page, dto);

        //给分页添加结果集
        page.setRecords(result);
        return page;
    }

    /**
     * 查询当前租户的用户列表
     *
     * @param dto  查询条件
     * @param page 分页信息
     * @return 用户列表
     */
    public Page<UserListDTO> searchTenantUser(SearchUserDTO dto, Page<UserListDTO> page) {
        //用户名设置为模糊查询
        if (StringUtils.isNotBlank(dto.getLoginName())) {
            dto.setLoginName(prepareLikeContent(dto.getLoginName()));
        }
        if (StringUtils.isNotBlank(dto.getCreateDateBeginStr())) {
            dto.setCreateDateBegin(DateConverter.parse(dto.getCreateDateBeginStr()));
        }

        if (StringUtils.isNotBlank(dto.getCreateDateEndStr())) {
            dto.setCreateDateEnd(DateConverter.parse(dto.getCreateDateEndStr()));
        }
        //查询用户列表
        List<UserListDTO> result = userMapper.searchUser(page, dto);
        //给分页添加结果集
        page.setRecords(result);
        return page;
    }


    /**
     * 获取用户详情
     *
     * @param id 用户主键
     * @return 用户详情
     */
    public UserDetailDTO getUserDetail(String id) {
        //获取用户信息
        User user = getUser(id);
        //获取用户的角色信息
        List<UserRole> userRoles = userRoleMapper.selectList(new QueryWrapper<UserRole>().eq(Tables.T_USER_ROLE
                .USER_ID.name(), id));
        List<String> roleIds = new ArrayList<>();
        for (UserRole userRole : userRoles) {
            roleIds.add(userRole.getRoleId());
        }

        //获取用户的部门信息
        //        queryWrapper = new QueryWrapper<UserGroup>().and(userGroupQueryWrapper -> {
        //            return userGroupQueryWrapper
        //                    .eq(Tables.T_USER_GROUP.USER_ID.name(), id)
        //                    .eq(Tables.T_USER_GROUP.TENANT_ID.name(), tenantId);
        //        });
        //        List<UserGroup> userGroups = userGroupMapper.selectList(queryWrapper);
        //        List<String> groupIds = new ArrayList<>();
        //        for (UserGroup userGroup : userGroups) {
        //            groupIds.add(userGroup.getGroupId());
        //        }

        //设置返回对象
        UserDetailDTO result = new UserDetailDTO();
        if (user != null) {
            BeanUtils.copyProperties(user, result);
            result.setRoleIds(roleIds);
            //            result.setGroupIds(groupIds);
        }
        return result;
    }

    public void resetUserPassword(ResetUserPasswordDTO dto) {
        User user = getUser(dto.getUserId());
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.update(SecurityUtils.getUserId());
        userMapper.updateById(user);
    }

    /**
     * 编辑用户
     *
     * @param updateUserDTO 更新用户参数对象
     * @see {@link com.fengyun.udf.uaa.dto.request.system.UpdateUserDTO}
     */
    public void updateUser(UpdateUserDTO updateUserDTO) {
        //更新用户
        User user = getUser(updateUserDTO.getUserId());


        List<String> userTypes = new ArrayList<>();
        if (UserType.management.name().equals(updateUserDTO.getUserType())) {
            userTypes = prepareOperationUserType();
        } else if (UserType.tenant.name().equals(updateUserDTO.getUserType())) {
            userTypes = prepareTenantUserType();
        } else if (UserType.member.name().equals(updateUserDTO.getUserType())) {
            userTypes = prepareTenantMemberUserType();
        }

        String tel = updateUserDTO.getTel();
        if (StringUtils.isEmpty(user.getTel())) {
            User user1 = findByTel(tel, userTypes);
            if (user1 != null) {
                throw new ValidationException("error", "手机号已存在");
            }
        }

        String loginName = updateUserDTO.getLoginName();
        if (StringUtils.isEmpty(user.getLoginName()) || !user.getLoginName().equals(loginName)) {
            User user1 = findByLoginName(loginName, userTypes);
            if (user1 != null) {
                throw new ValidationException("error", "登录名已存在");
            }
        }

        user.setLoginName(updateUserDTO.getLoginName());
        if (StringUtils.isNotBlank(updateUserDTO.getPassword())) {
            user.setPassword(passwordEncoder.encode(updateUserDTO.getPassword()));
        }
        user.setTel(updateUserDTO.getTel());
        user.setNickName(updateUserDTO.getNickName());
        user.setStatus(updateUserDTO.getStatus());
        user.setPolicySystem(updateUserDTO.getPolicySystem());
        user.update(SecurityUtils.getUserId());
        userMapper.updateById(user);

        //删除用户的角色信息
        QueryWrapper deleteWrapper = new QueryWrapper<UserRole>().eq(Tables.T_USER_ROLE.USER_ID.name(), user.getId());
        userRoleMapper.delete(deleteWrapper);


        //删除用户的部门信息
        //        deleteWrapper = new QueryWrapper<UserGroup>().and(userGroupQueryWrapper -> {
        //            return userGroupQueryWrapper
        //                    .eq(Tables.T_USER_GROUP.USER_ID.name(), user.getId())
        //                    .eq(Tables.T_USER_GROUP.TENANT_ID.name(), SecurityUtils.getTenantId());
        //        });
        //        userGroupMapper.delete(deleteWrapper);
        //        //给用户添加部门
        //        addGroup(updateUserDTO.getGroupIds(), user.getTenantId(), user.getId());

        //给用户添加角色
        addRole(updateUserDTO.getRoleIds(), user.getId());

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
    }

    /**
     * 逻辑删除用户
     *
     * @param id 用户主键
     */
    public void deleteUser(String id) {
        User user = getUser(id);
        user.setStatus(STATUS_DELETED);
        user.update(SecurityUtils.getUserId());
        userMapper.updateById(user);
    }

    /**
     * 修改当前用户昵称
     *
     * @param dto
     */
    public void updateCurrentUserInfo(UpdateUserInfoDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }
        user.setNickName(dto.getNickName());

        user.update(userId);
        userMapper.updateById(user);

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
    }

    /**
     * 修改当前用户头像
     *
     * @param dto
     */
    public void updateCurrentUserPhoto(UpdateUserInfoDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }
        user.setImage(dto.getImage());

        user.update(userId);
        userMapper.updateById(user);
    }

    /**
     * 修改当前用户姓名
     *
     * @param dto
     */
    public void updateCurrentUserName(UpdateUserInfoDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }
        user.setNickName(dto.getFullName());

        user.update(userId);
        userMapper.updateById(user);

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
    }

    /**
     * 修改当前用户出生日期
     *
     * @param dto
     */
    public void updateCurrentUserBirthday(UpdateUserInfoDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }

        user.update(userId);
        userMapper.updateById(user);
    }

    /**
     * 修改当前用户公司信息
     *
     * @param dto
     */
    public void updateCurrentUserCompany(UpdateUserInfoDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }

        user.update(userId);
        userMapper.updateById(user);
    }

    /**
     * 修改当前用户联系电话
     *
     * @param dto
     */
    public void updateCurrentUserContactNumber(UpdateUserInfoDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }

        user.update(userId);
        userMapper.updateById(user);
    }

    /**
     * 修改密码
     *
     * @param dto
     */
    public void changePassword(ChangePasswordDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }
        if (StringUtils.isBlank(dto.getPassword())) {
            throw new ValidationException("error", "密码不能为空");
        }
        if (StringUtils.isBlank(dto.getOldPassword())) {
            throw new ValidationException("error", "旧密码不能为空");
        }
        String p = user.getPassword();
        boolean b = passwordEncoder.matches(dto.getOldPassword(), p);
        if (!b) {
            throw new ValidationException("error", "旧密码不正确");
        }

        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.update(userId);
        userMapper.updateById(user);
    }

    public void changePasswordNew(ChangePasswordDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (StringUtils.isBlank(dto.getPassword())) {
            throw new ValidationException("error", "密码不能为空");
        }
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }
        if (!UserType.expert.name().equals(user.getType())) {
            throw new ValidationException("error", "用户类型错误");
        }
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.update(userId);
        userMapper.updateById(user);
    }

    public void changeAgreementFile(ChangeAgreementFileDTO dto) {
        String userId = SecurityUtils.getUserId();
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ValidationException("error", "用户不存在");
        }
        user.setAgreementFile(dto.getAgreementFile());
        user.update(userId);
        userMapper.updateById(user);
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    public CurrentUserDetailDTO getCurrentUser() {
        String userId = SecurityUtils.getUserId();
        CurrentUserDetailDTO result = new CurrentUserDetailDTO();
        //获取用户信息
        User user = userMapper.selectById(userId);
        result.setId(user.getId());
        result.setLoginName(user.getLoginName());
        result.setNickName(user.getNickName());
        result.setTel(user.getTel());
        result.setType(user.getType());
        result.setImage(user.getImage());
        result.setAgreementFile(user.getAgreementFile());
        result.setUserType(user.getUserType());
        result.setEmail(user.getEmail());
        result.setOrderState(user.getOrderState());

        //获取用户角色信息
        List<Role> roleList = roleService.getUserRoleList(userId);
        List<String> roles = new ArrayList<>();
        List<String> roleIds = new ArrayList<>();
        for (Role role : roleList) {
            roles.add(role.getRole());
            roleIds.add(role.getId());
        }
        result.setRoles(roles);


        List<MenuTreeDTO> menus = null;
        //根据用户角色获取用户菜单
        if (roleIds != null && roleIds.size() > 0) {
            menus = menuMapper.selectOperationList(roleIds);
        }

        if (menus != null) {
            menus = (List<MenuTreeDTO>) TreeBuilder.buildListToTree(menus);
        }
        result.setMenus(menus);

        CertificationInfo certificationInfo = certificationInfoMapper.selectOne(new LambdaQueryWrapper<CertificationInfo>()
                .eq(BaseDomain::getCreatedId, SecurityUtils.getUserId()));
        if (certificationInfo != null) {
            ShowCertificationInfoDto info = new ShowCertificationInfoDto();
            info.setAttachment(parseArray(certificationInfo.getAttachment(), AttachmentDTO.class));
            info.setOrgName(certificationInfo.getOrgName());
            info.setId(certificationInfo.getId());
            result.setCertificationInfo(info);
        }

        EnRegister enRegister = enRegisterMapper.selectOne(new LambdaQueryWrapper<EnRegister>()
                .eq(EnRegister::getUserId, userId));
        if (enRegister != null) {
            EnUserInfo enUserInfo = new EnUserInfo();
            BeanUtils.copyProperties(enRegister, enUserInfo);
            result.setEnUserInfo(enUserInfo);
        }

        return result;
    }

    /**
     * 修改用户密码
     *
     * @param user
     * @param password
     */
    public void updatePassword(User user, String password) {
        user.setPassword(passwordEncoder.encode(password));
        user.update(SecurityUtils.getUserId());
        userMapper.updateById(user);
    }

    public void updateUserSubscribe(UpdateUserSubscribeDTO dto) {
        User user = userMapper.selectById(dto.getUserId());
        if (user != null) {
            user.setSubscribe(dto.getSubscribe());
            user.setSubscribeMethod(dto.getSubscribeMethod());
            userMapper.updateById(user);
        }
    }


    /**
     * 绑定第三方登录用户
     *
     * @param userId
     * @param openId
     * @param type
     */
    public void bindThird(String userId, String openId, String type) {
        UserThird ut = userThirdMapper.selectOne(new QueryWrapper<UserThird>().lambda()
                .eq(UserThird::getThirdKey, openId)
                .eq(UserThird::getThirdType, type));

        if (ut != null) {
            ut.setUserId(userId);
            ut.update(userId);
            userThirdMapper.updateById(ut);
        } else {
            ut = new UserThird();
            ut.setId(UUIDGenerator.getUUID());
            ut.setUserId(userId);
            ut.setThirdKey(openId);
            ut.setThirdType(type);
            ut.create(userId);
            userThirdMapper.insert(ut);
        }
    }

    /**
     * 解绑用户
     *
     * @param userId
     * @param type
     */
    public void removeThird(String userId, String type) {
        userThirdMapper.delete(new QueryWrapper<UserThird>().lambda()
                .eq(UserThird::getUserId, userId)
                .eq(UserThird::getThirdType, type));
    }

    /**
     * 根据第三方登录用户
     *
     * @param userId
     * @param type
     * @return
     */
    public UserThird findByUserIdAndType(String userId, String type) {
        UserThird ut = userThirdMapper.selectOne(new QueryWrapper<UserThird>().lambda()
                .eq(UserThird::getUserId, userId)
                .eq(UserThird::getThirdType, type));
        if (ut != null) {
            return ut;
        }
        return null;
    }

    /**
     * 根据id获取登录用户
     *
     * @param openId
     * @param type
     * @return
     */
    public User findByOpenIdAndType(String openId, String type) {
        UserThird ut = userThirdMapper.selectOne(new QueryWrapper<UserThird>().lambda()
                .eq(UserThird::getThirdKey, openId)
                .eq(UserThird::getThirdType, type));
        if (ut != null) {
            return getUserById(ut.getUserId());
        }
        return null;
    }

    /**
     * 获取用户列表
     *
     * @param dto
     * @param page
     * @return
     */
    public Page<UserMemberListDTO> searchMemberUser(SearchMemberUserDTO dto, Page page) {
        if (StringUtils.isNotBlank(dto.getLoginName())) {
            dto.setLoginName(prepareLikeContent(dto.getLoginName()));
        }
        List<UserMemberListDTO> result = userMapper.selectMemberUser(page, dto);
        page.setRecords(result);
        return page;
    }

    public void initNicknameCache() {
        nicknameCacheClient.deleteAll();
        List<UserListDTO> ls = userMapper.findAllActiveUser();
        for (UserListDTO user : ls) {
            cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
        }
    }

    public EnRegister queryUserEnRegInfo(String id) {
        return enRegisterMapper.selectOne(new LambdaQueryWrapper<EnRegister>().eq(EnRegister::getUserId, id));
    }

    public void getRegisteredUserList(OutputStream outputStream) {
        List<UserRegisterInfoList> info = userMapper.queryRegisterUserList();
        EasyExcel.write(outputStream, UserRegisterInfoList.class).sheet("sheet1").doWrite(info);
    }

    public Page<RegisterUserList> queryRegisterUserList(QueryRegisterUserDto dto, Page page) {
        List<RegisterUserList> list = userMapper.queryRegisterUserPage(dto, page);
        page.setRecords(list);
        return page;
    }

    public User findByCodeTel(String creditCode, String tel) {
        return userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getTel, tel)
                .eq(User::getLoginName, creditCode)
                .eq(User::getType, UserType.member.name()));
    }
}
