package com.fengyun.udf.uaa.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fengyun.udf.uaa.constant.Roles;
import com.fengyun.udf.uaa.constant.Tables;
import com.fengyun.udf.uaa.domain.system.Menu;
import com.fengyun.udf.uaa.domain.system.Role;
import com.fengyun.udf.uaa.domain.system.RoleMenu;
import com.fengyun.udf.uaa.dto.request.system.CreateMenuDTO;
import com.fengyun.udf.uaa.dto.request.system.UpdateMenuDTO;
import com.fengyun.udf.uaa.dto.response.system.MenuDTO;
import com.fengyun.udf.uaa.dto.response.system.MenuTreeDTO;
import com.fengyun.udf.uaa.mapper.system.MenuMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.TreeBuilder;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.fengyun.udf.uaa.constant.Constants.*;

/**
 * Created by zhouyr on 2019/7/5.
 */
@Service
@Transactional
public class MenuService extends BaseService {
    @Autowired
    private MenuMapper menuMapper;

    public Menu getMenu(String id) {
        return menuMapper.selectById(id);
    }

    /**
     * 验证菜单节点是否存在
     *
     * @param id 租户菜单主键ID
     * @return boolean
     */
    public boolean isMenuExists(final String id, final String isOperation) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<Menu>()
                .eq(Tables.T_MENU.ID.name(), id)
                .eq(Tables.T_MENU.IS_OPERATION.name(), isOperation);
        long count = menuMapper.selectCount(queryWrapper);
        return count == 1;
    }

    /**
     * 添加租户菜单
     *
     * @param createMenuDTO 添加租户菜单参数对象
     * @param isOperation   是否是运营平台
     */
    public void addMenu(CreateMenuDTO createMenuDTO, final String isOperation) {
        Menu menu = new Menu();
        menu.setId(UUIDGenerator.getUUID());
        menu.setParentId(createMenuDTO.getParentId());
        menu.setMenuName(createMenuDTO.getMenuName());
        menu.setMenuType(createMenuDTO.getMenuType());
        menu.setMenuIcon(createMenuDTO.getMenuIcon());
        menu.setMenuSort(createMenuDTO.getMenuSort());
        menu.setMenuComponent(createMenuDTO.getMenuComponent());
        menu.setMenuHref(createMenuDTO.getMenuHref());
        menu.setMenuPermission(createMenuDTO.getMenuPermission());
        menu.setIsVisible(YES);
        menu.setStatus(STATUS_ACTIVE);
        menu.setIsOperation(isOperation);

        if (YES.equals(isOperation)) {
            menu.setAppId(null);
            menu.setMenuBelong(MENU_BELONG.management.name());
            menu.setClientType(CLIENT_TYPE.PC.name());
        } else {
            menu.setAppId(createMenuDTO.getAppId());
            menu.setMenuBelong(createMenuDTO.getMenuBelong());
            menu.setClientType(createMenuDTO.getClientType());
        }
        menuMapper.insert(menu);
        if (YES.equals(isOperation)) {
            //超级管理员角色拥有所有菜单
            Role adminRole = roleMapper.selectOne(new QueryWrapper<Role>().lambda()
                    .eq(Role::getRole, Roles.ROLE_SYS_ADMIN.name())
                    .eq(Role::getRoleType, ROLE_TYPE.management));
            if (adminRole != null) {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setId(UUIDGenerator.getUUID());
                roleMenu.setMenuId(menu.getId());
                roleMenu.setRoleId(adminRole.getId());
                roleMenuMapper.insert(roleMenu);
            }
        }

    }

    /**
     * 查询运营管理平台菜单树形结构
     *
     * @param isOperation 是否是运营平台
     * @return 运营平台菜单树形结构
     */
    public List<MenuTreeDTO> searchOperationMenu(final String isOperation) {
        List<MenuTreeDTO> treeList = menuMapper.selectOperationListOrderBySeq(isOperation, MENU_BELONG.management.name());
        treeList = (List<MenuTreeDTO>) TreeBuilder.buildListToTree(treeList);
        return treeList;
    }

    /**
     * 查询租户菜单详情
     *
     * @param id          租户菜单主键
     * @param isOperation 是否是运营平台
     * @return 租户菜单详情
     */
    public MenuDTO getMenuDetail(String id, final String isOperation) {
        //查询有效且可见的租户菜单
        Menu menu = menuMapper.selectOne(new QueryWrapper<Menu>().eq(Tables.T_MENU.ID.name(), id)
                .eq(Tables.T_MENU.STATUS.name(), STATUS_ACTIVE)
                .eq(Tables.T_MENU.IS_OPERATION.name(), isOperation)
                .eq(Tables.T_MENU.IS_VISIBLE.name(), YES));

        //返回对象
        MenuDTO menuDTO = new MenuDTO();
        if (menu != null) {
            BeanUtils.copyProperties(menu, menuDTO);
        }
        return menuDTO;
    }

    /**
     * 更新租户菜单
     *
     * @param updateMenuDTO 更新租户菜单对象
     */
    public void updateMenu(UpdateMenuDTO updateMenuDTO) {
        Menu menu = getMenu(updateMenuDTO.getMenuId());
        menu.setParentId(updateMenuDTO.getParentId());
        menu.setMenuName(updateMenuDTO.getMenuName());
        menu.setMenuType(updateMenuDTO.getMenuType());
        menu.setMenuIcon(updateMenuDTO.getMenuIcon());
        menu.setMenuSort(updateMenuDTO.getMenuSort());
        menu.setMenuComponent(updateMenuDTO.getMenuComponent());
        menu.setMenuHref(updateMenuDTO.getMenuHref());
        menu.setMenuPermission(updateMenuDTO.getMenuPermission());
        menuMapper.updateById(menu);
    }

    /**
     * 逻辑删除租户菜单
     *
     * @param id 租户菜单主键
     */
    public void deleteMenu(String id) {
        Menu menu = getMenu(id);
        menu.setStatus(STATUS_DELETED);
        menuMapper.updateById(menu);

        List<Menu> menus = menuMapper.selectList(new QueryWrapper<Menu>().eq(Tables.T_MENU.PARENT_ID.name(), id));
        if (menus != null && menus.size() > 0) {
            for (Menu children : menus) {
                deleteMenu(children.getId());
            }
        }
    }

    /**
     * 获取用户所有菜单权限
     *
     * @param userId 用户主键
     * @return 用户所有菜单权限
     */
    public List<String> getUserMenuPermissions(String userId) {
        return menuMapper.selectMenuPermissionByUser(userId);
    }
}
