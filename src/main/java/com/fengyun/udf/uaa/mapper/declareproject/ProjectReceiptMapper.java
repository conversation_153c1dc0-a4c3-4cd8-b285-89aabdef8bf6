package com.fengyun.udf.uaa.mapper.declareproject;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.declareproject.ProjectReceipt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.declareproject.QueryContractDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Repository
public interface ProjectReceiptMapper extends BaseMapper<ProjectReceipt> {

    /**
     * 分页查询收据
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页详情
     */
    List<ProjectReceiptListDTO> page(Page<ProjectReceiptListDTO> page,@Param("condition") QueryContractDTO dto);

    /**
     * 收据详情
     * @param id 项目ID
     * @param category 收据次数
     * @return 收据详情
     */
    ProjectReceiptDetailDTO detail(@Param("id") String id, @Param("category") String category);

    /**
     * 获取当前最大的编号
     * @param id 项目Id
     * @param category 收据次数
     * @return 最大的编号
     */
    Long maxNo(@Param("id") String id, @Param("category") String category);
}
