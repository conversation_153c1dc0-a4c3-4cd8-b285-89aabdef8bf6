package com.fengyun.udf.uaa.mapper.module;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fengyun.udf.uaa.domain.module.ModuleList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.response.module.ModuleListShowDto;
import com.fengyun.udf.uaa.dto.response.module.ModuleListTreeDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 模块列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Repository
public interface ModuleListMapper extends BaseMapper<ModuleList> {

    List<ModuleListTreeDto> selectListTree(@Param("isShowInNav") String isShowInNav);

    List<ModuleListShowDto> selectListShow(String condition);
}
