package com.fengyun.udf.uaa.dto.response.expertpanel;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.ZonedDateTime;

@ApiModel("专家评分模板")
@Data
@EqualsAndHashCode(callSuper = false)
public class ExpertScoreTemplateDTO extends AbstractDTO {
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板配置
     */
    @ApiModelProperty(value = "模板配置")
    private String templateConfig;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态，0-未关联，1-已关联")
    @DictData(codeType = "EXPERT_SCORE_TEMPLATE_STATUS", target = "statusStr")
    private String status;
    private String statusStr;

    @ApiModelProperty(value="创建日期")
    private ZonedDateTime createdDate;
    @ApiModelProperty(value="创建日期")
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createdDateStr;
}
