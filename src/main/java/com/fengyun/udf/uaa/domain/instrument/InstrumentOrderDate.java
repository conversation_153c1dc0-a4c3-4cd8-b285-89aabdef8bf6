package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 预约时间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Getter
@Setter
@TableName("in_instrument_order_date")
public class InstrumentOrderDate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 预约id
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 预约日期
     */
    @TableField("order_date")
    private LocalDate orderDate;

    /**
     * 预约时间
     */
    @TableField("order_time")
    private String orderTime;


}
