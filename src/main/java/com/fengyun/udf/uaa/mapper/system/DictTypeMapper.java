package com.fengyun.udf.uaa.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.system.DictType;
import com.fengyun.udf.uaa.dto.request.system.SearchDictTypeDTO;
import com.fengyun.udf.uaa.dto.response.system.DictTypeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DictTypeMapper extends BaseMapper<DictType> {
    List<DictTypeDTO> searchDictType(Page<DictTypeDTO> page, @Param("param") final SearchDictTypeDTO searchDictTypeDTO);
}
