package com.fengyun.udf.uaa.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.system.Role;
import com.fengyun.udf.uaa.dto.request.system.SearchRoleDTO;
import com.fengyun.udf.uaa.dto.response.system.RoleListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wangg on 2019/7/5.
 */
@Repository
public interface RoleMapper extends BaseMapper<Role> {
    List<RoleListDTO> searchRole(Page<RoleListDTO> page, @Param("condition") SearchRoleDTO searchRoleDTO);

    List<String> selectRoleByUser(final @Param("userId") String userId);

    List<Role> findByTenantId(@Param("tenantId") String tenantId);

    List<Role> findByTenantIdAndIdentity(@Param("tenantId") String tenantId, @Param("identity") String identity);

    List<Role> getUserRoleList(final @Param("userId") String userId);

}
