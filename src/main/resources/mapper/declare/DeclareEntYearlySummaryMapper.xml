<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.declare.DeclareEntYearlySummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fengyun.udf.uaa.domain.declare.DeclareEntYearlySummary">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id
    </sql>

    <resultMap id="DeclareEntYearlySummaryDetailDTOResultMap"
               type="com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryDetailDTO">
        <id column="id" property="id" />
        <result column="updatedId" property="updatedId" />
        <result column="updatedDate" property="updatedDate" />
        <result column="attachment" property="attachment"
                typeHandler="com.fengyun.udf.uaa.dto.AttachmentDTO$AttachmentListTypeHandler"/>
        <result column="createdId" property="createdId" />
        <result column="createdDate" property="createdDate" />
        <result column="projectId" property="projectId" />
        <result column="projectStatus" property="projectStatus" />
        <result column="projectNo" property="projectNo" />
        <result column="declareType" property="declareType" />
        <result column="declareName" property="declareName" />
        <result column="responsibleName" property="responsibleName" />
        <result column="subjectName" property="subjectName" />
    </resultMap>

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryListDTO">
        SELECT
            deys.id AS id,
            deys.UPDATED_ID AS updatedId,
            deys.UPDATED_DATE AS updatedDate,
            deys.CREATED_ID AS createdId,
            deys.CREATED_DATE AS createdDate,
            deys.submit_at AS submitAt,
            deys.project_id AS projectId,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.created_date AS createdDate,
            de.declare_type AS declareType,
            de.created_id AS createdId,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM declare_ent_yearly_summary deys
        LEFT JOIN
        t_declare_ent de ON de.id = deys.project_id
        LEFT JOIN
        flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN
        t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN
        flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        <where>
            <if test="condition.declareName != null and condition.declareName != '' ">
                AND dp.declare_name LIKE concat('%',#{condition.declareName},'%')
            </if>
            <if test="condition.declareType != null and condition.declareType != '' ">
                AND de.declare_type = #{condition.declareType}
            </if>
            <if test="condition.subjectName != null and condition.subjectName != '' ">
                AND con.subject_name LIKE concat('%',#{condition.subjectName},'%')
            </if>
            <if test="condition.responsibleName != null and condition.responsibleName != '' ">
                AND res.responsible_name LIKE concat('%',#{condition.responsibleName},'%')
            </if>
            <if test="condition.submitAtStart != null">
                AND deys.submit_at >= #{condition.submitAtStart}
            </if>
            <if test="condition.submitAtEnd != null">
                AND deys.submit_at &lt;= #{condition.submitAtEnd}
            </if>
        </where>
    </select>
    <select id="detail" resultMap="DeclareEntYearlySummaryDetailDTOResultMap">
        SELECT deys.id              AS id,
               deys.UPDATED_ID      AS updatedId,
               deys.UPDATED_DATE    AS updatedDate,
               deys.attachment      AS attachment,
               deys.CREATED_ID      AS createdId,
               deys.CREATED_DATE    AS createdDate,
               deys.project_id      AS projectId,
               de.project_status    AS projectStatus,
               de.project_no        AS projectNo,
               de.created_date      AS createdDate,
               de.declare_type      AS declareType,
               de.created_id        AS createdId,
               dp.declare_name      AS declareName,
               de.project_progress  AS projectProgress,
               res.responsible_name as responsibleName,
               con.subject_name     AS subjectName
        FROM declare_ent_yearly_summary deys
        LEFT JOIN
             t_declare_ent de ON de.id = deys.project_id
        LEFT JOIN
             flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN
             t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN
             flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        WHERE deys.project_id = #{projectId}
    </select>

</mapper>
