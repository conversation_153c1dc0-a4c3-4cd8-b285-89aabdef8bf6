<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderMapper">

    <select id="selectPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderListDTO">
        select
        io.id,
        io.instrument_id,
        io.lease_type,
        io.order_date,
        io.order_time,
        io.en_name,
        io.contact,
        io.contact_tel,
        io.contact_email,
        io.address,
        io.skilled_use,
        io.status,
        io.evaluate_time,
        io.settlement_status,
        DATE_FORMAT(io.created_date, '%Y-%m-%d %H:%i:%s') as createdDate,
        i.name as instrumentName,
        i.model,
        iow.amount as amount,
        iow.status as workStatus,
        ior.send_time as receiptSendTime,
        ior.receipt_stamp_file,
        ior.receipt_file,
        io.evaluate_content,
        io.invoice_file,
        io.send_invoice_time,
        io.order_num
        from
        in_instrument_order io
        join in_instrument i on io.instrument_id = i.id
        left join in_instrument_order_work iow on iow.id = io.id <if test="condition.userId != null and condition.userId != ''"> and iow.status != 'draft' </if>
        left join in_instrument_order_receipt ior on ior.id = io.id and ior.send_time is not null
        where 1=1
        <if test="condition.keyword != null and condition.keyword != ''">
            and (i.name like concat('%',#{condition.keyword},'%')
            or io.en_name like concat('%',#{condition.keyword},'%')
            or io.contact like concat('%',#{condition.keyword},'%')
            )
        </if>
        <if test="condition.instrumentName != null and condition.instrumentName != ''">
            and i.name like concat('%',#{condition.instrumentName},'%')
        </if>
        <if test="condition.enName != null and condition.enName != ''">
            and io.en_name like concat('%',#{condition.enName},'%')
        </if>
        <if test="condition.contact != null and condition.contact != ''">
            and io.contact like concat('%',#{condition.contact},'%')
        </if>
        <if test="condition.status != null and condition.status != ''">
            and io.status = #{condition.status}
        </if>
        <if test="condition.skilledUse != null ">
            and io.skilled_use = #{condition.skilledUse}
        </if>
        <if test="condition.userId != null and condition.userId != ''">
            and io.created_id = #{condition.userId}
        </if>
        <if test="condition.startDate != null || condition.endDate != null ">
           and EXISTS (SELECT iiod.order_id FROM in_instrument_order_date iiod WHERE io.id = iiod.order_id
            <if test="condition.startDate != null ">
                and iiod.order_date &gt;= #{condition.startDate}
            </if>
            <if test="condition.endDate != null ">
                and iiod.order_date &lt; #{condition.endDate}
            </if>
            )
        </if>
        <if test="condition.settlementStatus != null and condition.settlementStatus != ''">
            and io.settlement_status = #{condition.settlementStatus}
        </if>
        <if test="condition.leaseType != null and condition.leaseType != ''">
            and io.lease_type = #{condition.leaseType}
        </if>
        <if test="condition.invoiceStatus != null and condition.invoiceStatus == 'YES'">
            and io.send_invoice_time is not null
        </if>
        <if test="condition.invoiceStatus!= null and condition.invoiceStatus == 'NO'">
            and io.send_invoice_time is null
        </if>
        <if test="condition.workStatus != null and condition.workStatus == 'final_commit'">
            and iow.status = 'final_commit'
        </if>
        <if test="condition.workStatus != null and condition.workStatus == 'final_commit_no'">
            and iow.status != 'final_commit'
        </if>
        <if test="condition.ids != null and condition.ids.size > 0 ">
            AND io.id in
            <foreach collection="condition.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="condition.applyStartDate != null ">
            and io.created_date &gt;= #{condition.applyStartDate}
        </if>
        <if test="condition.applyEndDate != null ">
            and io.created_date &lt; #{condition.applyEndDate}
        </if>

        <choose>
            <when test="condition.export != null and condition.export == 'YES'">
                order by io.en_name,io.created_date desc
            </when>
            <otherwise>
                order by io.created_date desc
            </otherwise>
        </choose>

    </select>

    <select id="selectExportList" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDTO">
        SELECT
        io.id,
        io.instrument_id,
        io.lease_type,
        io.order_date,
        io.order_time,
        io.en_name,
        io.contact,
        io.contact_tel,
        io.contact_email,
        io.address,
        io.skilled_use,
        io.sign_agreement,
        io.use_sample_num,
        io.sample_name,
        io.sample_package,
        io.sample_spec,
        io.sample_deposit,
        io.sample_feature,
        io.test_project,
        io.test_condition,
        io.test_method,
        io.content,
        io.remark,
        io.STATUS,
        io.settlement_status,
        io.order_num,
        DATE_FORMAT(io.created_date,'%Y-%m-%d %H:%i:%s') as createdDate,
        i.`name` AS instrumentName,
        iow.lease_face,
        iow.lease_price,
        iow.test_face,
        iow.test_price,
        iow.amount,
        case iow.`status` when 'final_commit' then '已发送' ELSE '未发送' END AS workStatus
        FROM
        in_instrument_order io
        JOIN in_instrument i ON i.id = io.instrument_id
        JOIN in_instrument_order_work iow ON iow.id = io.id

        where iow.`status` != 'draft'
        <if test="userId != null and userId != '' ">
            and io.created_id = #{userId}
        </if>
        <if test="condition.orderStartDate != null || condition.orderEndDate != null ">
            and EXISTS (SELECT iiod.order_id FROM in_instrument_order_date iiod WHERE io.id = iiod.order_id
            <if test="condition.orderStartDate != null ">
                and iiod.order_date &gt;= #{condition.orderStartDate}
            </if>
            <if test="condition.orderEndDate != null ">
                and iiod.order_date &lt; #{condition.orderEndDate}
            </if>
            )
        </if>
        order by io.created_date
    </select>

    <select id="selectOrderConsumable" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDTO">
        SELECT
            c.`name` as consumableName,
            c.unit_price as consumableUnitPrice,
            ic.num as consumableNum
        FROM
            in_instrument_order o
                JOIN in_instrument_order_consumable ic ON o.id = ic.order_id
                JOIN in_consumable c ON ic.consumable_id = c.id
        WHERE
            o.id = #{id} order by c.`name`
    </select>


    <select id="selectOrderDateTime" resultType="string">
        SELECT
            iod.order_time
        FROM
            in_instrument_order io
                JOIN in_instrument_order_date iod ON io.id = iod.order_id
        WHERE
            io.instrument_id = #{instrumentId}
          AND io.`status` IN ( 'pre', 'pass' )
          AND iod.order_date = #{orderDate}
    </select>

    <select id="selectNextOrderNum" resultType="string">
        select max(order_num)  from in_instrument_order where order_num LIKE CONCAT(#{date}, '%')
    </select>
</mapper>
