package com.fengyun.udf.uaa.service.declareproject;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.interceptor.CodeDecorator;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.constant.DeclareCons;
import com.fengyun.udf.uaa.domain.declareproject.*;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.*;
import com.fengyun.udf.uaa.dto.request.flowform.json.ProjectParticipantsDto;
import com.fengyun.udf.uaa.dto.response.declareproject.*;
import com.fengyun.udf.uaa.fastdfs.ClientGlobal;
import com.fengyun.udf.uaa.fastdfs.client.UploadFile;
import com.fengyun.udf.uaa.fastdfs.client.UploadManager;
import com.fengyun.udf.uaa.mapper.declareproject.*;
import com.fengyun.udf.uaa.mapper.flowform.PersonnelSituationMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.file.FileService;
import com.fengyun.udf.uaa.util.CollUtils;
import com.fengyun.udf.util.ExcelExportUtil;
import com.fengyun.udf.util.UUIDGenerator;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class DeclareEntService extends BaseService {

    private final DeclareEntMapper declareEntMapper;

    private final DeclareProjectMapper declareProjectMapper;

    private final DeclareEntRoleMapper declareEntRoleMapper;

    private final DeclareFlowService declareFlowService;

    private final RuNodeTaskHistoryMapper ruNodeTaskHistoryMapper;

    private final CodeDecorator codeDecorator;

    private final PersonnelSituationMapper personnelSituationMapper;

    private final ProjectContractMapper projectContractMapper;

    private final ProjectReceiptMapper projectReceiptMapper;

    private final FileService fileService;

    public DeclareEntDetailDTO getDetail(String id, String dataTime) {
        return getDetail(id);
    }

    public DeclareEntDetailDTO getDetail(String id) {
        DeclareEntDetailDTO declareEntDetailDTO = new DeclareEntDetailDTO();
        DeclareEnt declareEnt = declareEntMapper.selectById(id);
        if (declareEnt == null) {
            throw new ValidationException("error", "对象不存在，请确认数据");
        }
        DeclareProject declareProject = declareProjectMapper.selectById(declareEnt.getDeclareId());
        if (declareProject == null) {
            throw new ValidationException("error", "申报政策不存在，请确认数据");
        }

        BeanUtils.copyProperties(declareProject, declareEntDetailDTO);
        DeclareEntInfoDTO declareEntInfoDTO = new DeclareEntInfoDTO();
        BeanUtils.copyProperties(declareEnt, declareEntInfoDTO);
        declareEntDetailDTO.setNode(declareEnt.getCurrentRole());
        declareEntDetailDTO.setDeclareEntInfoDTO(declareEntInfoDTO);
        declareEntDetailDTO.setCreatedDate(declareEnt.getCreatedDate());
        declareEntDetailDTO.setCloseDebatePPTs(JSON.parseArray(declareEnt.getCloseDebatePPT(), AttachmentDTO.class));
        declareEntDetailDTO.setCloseDebatePPT(null);
        declareEntDetailDTO.setCloseDebateVideos(JSON.parseArray(declareEnt.getCloseDebateVideo(), AttachmentDTO.class));
        declareEntDetailDTO.setCloseDebateVideo(null);
        return declareEntDetailDTO;
    }

    public Page<DeclareEntShowDTO> findList(Page<DeclareEntShowDTO> page, String role,
                                            String keyWord, String declareId, String declareType) {
        String userId = SecurityUtils.getUserId();
        String[] declareTypeList = null;
        if(StringUtils.isNotBlank(declareType)) {
            declareTypeList = declareType.split(",");
        }
        List<DeclareEntShowDTO> list = declareEntMapper.list(page, role, keyWord, userId, declareId, declareTypeList);
        list.forEach(a -> {
            a.setReturnFlag(Constants.TDECLARE_STATUS.CANCEL.name().equals(a.getStatus()));
            a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            a.setCloseDebatePPT(null);
            a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            a.setCloseDebateVideo(null);
        });
        page.setRecords(list);
        return page;
    }

    public void exportDeclareProjectList(HttpServletResponse response, String keyWord, String declareType) {
        String userId = SecurityUtils.getUserId();
        String[] declareTypeList = null;
        if(StringUtils.isNotBlank(declareType)) {
            declareTypeList = declareType.split(",");
        }
        List<DeclareEntShowDTO> list = declareEntMapper.list(null, "", keyWord, userId, "", declareTypeList);
        codeDecorator.decorate(list);
        //导出数据
        String[] rowsName = new String[]{"序号", "申报类别", "项目名称", "单位名称", "提交申报时间", "立项依据", "研究内容"};
        List<Object[]> dataList = new ArrayList<>();
        Object[] objs = null;
        int i = 1;
        for (DeclareEntShowDTO dto : list) {
            objs = new Object[rowsName.length];
            objs[0] = i;
            objs[1] = dto.getDeclareTypeStr();
            objs[2] = dto.getSubjectName();
            objs[3] = dto.getResponsibleName();
            objs[4] = dto.getCreatedDateStr();
            objs[5] = dto.getProjectBasis();
            objs[6] = dto.getResearchContent();
            i++;
            dataList.add(objs);
        }
        ExcelExportUtil excelExportUtil = new ExcelExportUtil("审核列表", rowsName, dataList, response);
        try {
            excelExportUtil.export();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportAllDeclareProjectList(HttpServletResponse response, String keyWord, String declareType) {
        String userId = SecurityUtils.getUserId();
        // 查询待审核
        String[] declareTypeList = null;
        if(StringUtils.isNotBlank(declareType)) {
            declareTypeList = declareType.split(",");
        }
        List<DeclareEntShowDTO> list = declareEntMapper.list(null, "", keyWord, userId, "", declareTypeList);
        // 查询历史审核
        List<DeclareEntShowDTO> historyList = declareEntMapper.listHistroy(null, keyWord, declareTypeList, userId);
        Set<String> idSet = historyList.stream().map(DeclareEntShowDTO::getEntId).collect(Collectors.toSet());
        codeDecorator.decorate(list);
        codeDecorator.decorate(historyList);
        //导出数据
//        String[] rowsName = new String[]{"序号", "申报类别", "项目名称", "单位名称", "提交申报时间", "审核状态", "立项依据", "研究内容"};
        String[] rowsName = new String[]{"序号", "申报类别", "项目名称", "单位名称", "项目负责人", "负责人联系方式",
                "负责人邮箱", "提交申报时间", "审核状态"};
        List<Object[]> dataList = new ArrayList<>();
        Object[] objs;
        int i = 1;
        for (DeclareEntShowDTO dto : list) {
            // 去重
            if(idSet.contains(dto.getEntId())){
                continue;
            }
            objs = new Object[rowsName.length];
            objs[0] = i;
            objs[1] = dto.getDeclareTypeStr();
            objs[2] = dto.getSubjectName();
            objs[3] = dto.getResponsibleName();
            objs[4] = dto.getPrincipalName();
            objs[5] = dto.getTel();
            objs[6] = dto.getPrincipalMail();
            objs[7] = dto.getCreatedDateStr();
            objs[8] = "待审批";

//            objs[6] = dto.getProjectBasis();
//            objs[7] = dto.getResearchContent();
            i++;
            dataList.add(objs);
        }
        for (DeclareEntShowDTO dto : historyList) {
            objs = new Object[rowsName.length];
            objs[0] = i++;
            objs[1] = dto.getDeclareTypeStr();
            objs[2] = dto.getSubjectName();
            objs[3] = dto.getResponsibleName();
            objs[4] = dto.getPrincipalName();
            objs[5] = dto.getTel();
            objs[6] = dto.getPrincipalMail();
            objs[7] = dto.getCreatedDateStr();
            objs[8] = "历史审批";
//            objs[6] = dto.getProjectBasis();
//            objs[7] = dto.getResearchContent();
            dataList.add(objs);
        }
        ExcelExportUtil excelExportUtil = new ExcelExportUtil("审核列表", rowsName, dataList, response);
        try {
            excelExportUtil.export();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportProjectPerson(HttpServletResponse response, String projectId){
        List<Map<String, String>> list = personnelSituationMapper.selectPersonList(projectId);
        //导出数据
        String[] rowsName = new String[]{"序号", "项目名称", "单位名称", "姓名", "性别", "出生年月", "职务", "受教育程度",
                "毕业院校/专业", "重要业绩概述", "在本项目中承担的要工作"};
        List<Object[]> dataList = new ArrayList<>();
        Object[] objs = null;
        int i = 1;
        for (Map<String, String> map : list) {
            List<ProjectParticipantsDto> projectParticipantsDtos =
                    parseArray(map.get("project_participants").toString(), ProjectParticipantsDto.class);
            for (ProjectParticipantsDto dto : projectParticipantsDtos) {
                objs = new Object[rowsName.length];
                objs[0] = i;
                objs[1] = map.get("subject_name").toString();
                objs[2] = map.get("responsible_name").toString();
                objs[3] = dto.getName();
                objs[4] = "";
                if (StringUtils.isNotBlank(dto.getGender())) {
                    if ("1".equals(dto.getGender())) {
                        objs[4] = "男";
                    } else {
                        objs[4] = "女";
                    }
                }
                objs[5] = dto.getBirthDate();
                objs[6] = dto.getPosition();
                objs[7] = dto.getEducation();
                objs[8] = dto.getGraduatedSchool();
                objs[9] = dto.getPerformance();
                objs[10] = dto.getWork();
                i++;
                dataList.add(objs);
            }
        }
        ExcelExportUtil excelExportUtil = new ExcelExportUtil("项目主要参加人员情况", rowsName, dataList, response);
        try {
            excelExportUtil.export();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Page<DeclareEntOverShowDTO> findOverList(Page page, String keyWord) {
        LambdaQueryWrapper<DeclareProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(DeclareProject::getCreatedDate);
        if (StringUtils.isNotBlank(keyWord)) {
            queryWrapper.like(DeclareProject::getDeclareName, keyWord);
        }
        IPage iPage = declareProjectMapper.selectPage(page, queryWrapper);
        List<DeclareProject> declareProjects = iPage.getRecords();
        List<DeclareEntOverShowDTO> resultList = new ArrayList<>();

        for (DeclareProject a : declareProjects) {
            DeclareEntOverShowDTO showDTO = new DeclareEntOverShowDTO();
            BeanUtils.copyProperties(a, showDTO);
            LambdaQueryWrapper<DeclareEnt> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(DeclareEnt::getDeclareId, a.getId());
            List<DeclareEnt> declareEnts = declareEntMapper.selectList(queryWrapper1);
            showDTO.setDeclareAllNum(declareEnts.size() + "");
            showDTO.setDeclareOverNum("0");
            showDTO.setDeclareId(a.getId());
            if (CollectionUtils.isNotEmpty(declareEnts)) {
                long count = declareEnts.stream().filter((declareEnt) -> "over".equals(declareEnt.getCurrentRole()))
                        .count();
                showDTO.setDeclareOverNum(count + "");
            }
            resultList.add(showDTO);
        }

        page.setRecords(resultList);
        return page;
    }

    public void handler(DeclareEntHandlerDTO declareEntHandlerDTO) {
        DeclareEnt declareEnt = declareEntMapper.selectById(declareEntHandlerDTO.getDeclareEntId());
        declareFlowService.next(declareEnt, declareEntHandlerDTO.getEvent(), declareEntHandlerDTO.getRemark(),
                declareEntHandlerDTO.getDeclareEntRoleId());
        declareEntMapper.updateById(declareEnt);
    }

    public Page<DeclareEntShowDTO> getHistoryList(Page<DeclareEntShowDTO> page, String keyWord, String declareType) {
        String[] declareTypeList = null;
        if(StringUtils.isNotBlank(declareType)) {
            declareTypeList = declareType.split(",");
        }
        List<DeclareEntShowDTO> resultList = declareEntMapper.listHistroy(page, keyWord, declareTypeList, SecurityUtils
                .getUserId());
        resultList.forEach(a -> {
            if (StringUtils.isNotEmpty(a.getCloseDebatePPT())) {
                a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            }
            if (StringUtils.isNotEmpty(a.getCloseDebateVideo())) {
                a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            }
        });
        page.setRecords(resultList);
        return page;
    }

    public Page<RuNodeTaskHistoryShowDTO> getHistoryNodeList(Page page, String declareEntId) {
        if (StringUtils.isBlank(declareEntId)) {
            throw new ValidationException("error", "请填写流转对象ID");
        }
        LambdaQueryWrapper<RuNodeTaskHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuNodeTaskHistory::getRuProcessId, declareEntId);
        queryWrapper.orderByDesc(BaseDomain::getCreatedDate);
        IPage iPage = ruNodeTaskHistoryMapper.selectPage(page, queryWrapper);
        List<RuNodeTaskHistory> records = iPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        List<RuNodeTaskHistoryShowDTO> showDTOs = new ArrayList<>();
        records.forEach(a -> {
            RuNodeTaskHistoryShowDTO m = new RuNodeTaskHistoryShowDTO();
            BeanUtils.copyProperties(a, m);
            showDTOs.add(m);
        });
        page.setRecords(showDTOs);
        return page;
    }

    public void backFlow(String id){
        DeclareEnt declareEnt = declareEntMapper.selectById(id);
        if (declareEnt == null) {
            throw new ValidationException("error", "申报信息不存在");
        }
        declareEnt.setStatus(Constants.TDECLARE_STATUS.CANCEL.name());
        declareEnt.setEntStatus(Constants.TDECLARE_STATUS.CANCEL.name());
        declareEnt.setCurrentRole("enterprise");
        declareEntMapper.updateById(declareEnt);
        // 修改entrole
        Map<String, String> entRoleMap = new HashMap<>();
        entRoleMap.put("declare_ent_id", id);
        entRoleMap.put("current_role", "enterprise");
        declareEntRoleMapper.updateEntRoleByEntId(entRoleMap);
        // 修改审批记录表
        LambdaQueryWrapper<RuNodeTaskHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RuNodeTaskHistory::getRuProcessId, id)
                .orderByDesc(RuNodeTaskHistory::getCreatedDate);
        List<RuNodeTaskHistory> taskHistories = ruNodeTaskHistoryMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(taskHistories)) {
            RuNodeTaskHistory ruNodeTaskHistory = taskHistories.get(0);
            ruNodeTaskHistory.setSelectEvent("back");
            ruNodeTaskHistoryMapper.updateById(ruNodeTaskHistory);
        }
    }

    public Page<DeclareEntShowDTO> getFirstPassList(Page<DeclareEntShowDTO> page, String keyWord, String declareType,String isUploadedPPTVideo) {
        List<DeclareEntShowDTO> resultList = declareEntMapper.listFirstPass(page, keyWord, declareType, SecurityUtils
                .getUserId(),isUploadedPPTVideo);
        resultList.forEach(a -> {
            if (StringUtils.isNotEmpty(a.getCloseDebatePPT())) {
                a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            }
            if (StringUtils.isNotEmpty(a.getCloseDebateVideo())) {
                a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            }
            if(!StringUtils.isEmpty(a.getCloseDebatePPT()) && !StringUtils.isEmpty(a.getCloseDebateVideo())){
                a.setUploadedPPTVideo("1");
            }else {
                a.setUploadedPPTVideo("0");
            }
        });
        page.setRecords(resultList);
        return page;
    }

    public Page<String> getSecondRelationList(Page<String> page, String type) {
        List<String> resultList = declareEntMapper.listSecondRelation(page,type,SecurityUtils
                .getUserId());
        page.setRecords(resultList);
        return page;
    }

    /**
     * 项目管理列表
     * @param page 分页参数
     * @param dto 筛选条件
     * @return 申报项目企业申报列表
     */
    public Page<DeclareEntListDTO> getSubProjectPage(Page<DeclareEntListDTO> page, QueryDeclareEntDTO dto) {
        dto.processQuery();
        if(StringUtils.isNotBlank(dto.getProjectStatus())){
            dto.setProjectStatusColl(dto.getProjectStatus().split(","));
        }
        List<DeclareEntListDTO> list = declareEntMapper.pageByDeclareId(page, dto);
        page.setRecords(list);
        return page;
    }

    /**
     * 项目评审
     * @param dto 评审结果
     */
    public void finalReview(DeclareEntHandlerDTO dto) {
        String event = dto.getEvent();
        Arrays.stream(dto.getDeclareEntId().split(",")).forEach(x -> {
            DeclareEnt declareEnt = declareEntMapper.selectById(x);
            if(Objects.isNull(declareEnt)) {
                throw new ValidationException("","申报项目不存在");
            }
            switch (event) {
                case "pass":
                    declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_REVIEW_PASS.getCode());
                    // 直接进入合同上传阶段
                    sendContractSubmit(x);
                    break;
                case "reject":
                    declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_REVIEW_NO_PASS.getCode());
                    break;
                default:
                    declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_REVIEW_PENDING.getCode());
            }
            // 设置更新时间
            declareEnt.update(SecurityUtils.getUserId());
            declareEntMapper.updateById(declareEnt);
            // 建立历史节点信息
            declareFlowService.createHistory(Constants.FINAL_REVIEW, declareEnt.getId(), dto.getRemark(), event);
        });
    }

    /**
     * 下发合同提交
     * @param projectId 项目Id
     */
    private void sendContractSubmit(String projectId) {
        // 获取项目
        DeclareEnt declareEnt = declareEntMapper.selectById(projectId);
        if(declareEnt == null) {
            throw new ValidationException("", "项目不存在");
        }
        // 新建合同信息
        ProjectContract projectContract = new ProjectContract();
        projectContract.setId(UUIDGenerator.getUUID());
        projectContract.setProjectId(projectId);
        projectContract.create(SecurityUtils.getUserId());
        // 项目进度更新(合同提交
        declareEnt.setProjectProgress(Constants.PROJECT_PROGRESS.CONTRACT_SUBMIT.name());
        declareEntMapper.updateById(declareEnt);
        projectContractMapper.insert(projectContract);
    }

    /**
     * 上传合同
     * @param dto 合同内容
     */
    public void uploadContract(UploadContractDTO dto) {
        String projectId = dto.getId();
        ProjectContract projectContract = projectContractMapper.selectOne(new LambdaQueryWrapper<ProjectContract>()
                .eq(ProjectContract::getProjectId, projectId));
        if(projectContract == null) {
            throw new ValidationException("", "当前状态不允许上传合同");
        }
        String auditStatus = projectContract.getAuditStatus();
        // 判断审核状态(初次提交和退回才可上传
        if(!("0".equals(auditStatus) || Constants.EN_DECLARE_STATUS.BACK.getCode().equals(auditStatus))) {
            throw new ValidationException("","当前状态不允许上传合同");
        }
        // 合同信息
        projectContract.setContract(JSON.toJSONString(dto.getAttachment()));
        // 审批中
        projectContract.setAuditStatus(Constants.EN_DECLARE_STATUS.APPROVE.getCode());
        projectContract.setSubmitDate(ZonedDateTime.now());
        projectContract.update(SecurityUtils.getUserId());
        projectContractMapper.updateById(projectContract);
        // 创建历史
        declareFlowService.createHistory(Constants.CONTRACT_ASSIGN_ROLE.SUBMIT.getDesc(),
                projectContract.getId(),
                "",
                // 判断是首次还是退回修改
                Constants.EN_DECLARE_STATUS.BACK.getCode().equals(auditStatus) ? "back" : "start");
    }

    /**
     * 审核合同
     * @param dto 审核信息
     */
    public void auditContract(DeclareEntHandlerDTO dto) {
        String event = dto.getEvent();
        // 兼容多选
        Arrays.stream(dto.getDeclareEntId().split(",")).forEach(x -> {
            ProjectContract projectContract = projectContractMapper.selectOne(new LambdaQueryWrapper<ProjectContract>()
                    .eq(ProjectContract::getProjectId, x));
            if(projectContract == null) {
                throw new ValidationException("","申报项目合同信息不存在");
            }
            // 合同审核
            switch (event) {
                case "pass":
                    projectContract.setAuditStatus(Constants.EN_DECLARE_STATUS.APPROVE_PASS.getCode());
                    // 更新项目进度
                    sendReceiptSubmit(x, DeclareCons.RECEIPT_CATEGORY.FIRST.getCode());
                    break;
                case "reject":
                    projectContract.setAuditStatus(Constants.EN_DECLARE_STATUS.APPROVE_REJECT.getCode());
                    break;
                case "back":
                    projectContract.setAuditStatus(Constants.EN_DECLARE_STATUS.BACK.getCode());
                    break;
                default:
                    break;
            }
            // 设置意见
            projectContract.setAuditRemark(dto.getRemark());
            // 设置更新时间
            projectContract.update(SecurityUtils.getUserId());
            projectContractMapper.updateById(projectContract);
            // 创建历史
            declareFlowService.createHistory(Constants.CONTRACT_ASSIGN_ROLE.AUDIT.getDesc(),
                    projectContract.getId(),
                    dto.getRemark(),
                    dto.getEvent());
        });
    }

    /**
     * 合同详情
     * @param id 项目ID
     * @return 合同详情
     */
    public ProjectContractDetailDTO contractDetail(String id) {
        ProjectContractDetailDTO dto = projectContractMapper.detail(id);
        if(dto == null) {
            throw new ValidationException("","暂无合同详情");
        }
        dto.setContract(JSON.parseArray(dto.getContractStr(), AttachmentDTO.class));
        dto.setContractStr(null);
        dto.setHistory(getTaskHistory(dto.getId()));
        return dto;
    }

    /**
     * 合同汇总列表
     * @param page 分页参数
     * @param dto 查询条件
     * @return 合同汇总列表
     */
    public Page<ProjectContractListDTO> contractPage(Page<ProjectContractListDTO> page, QueryContractDTO dto) {
        dto.processQuery();
        List<ProjectContractListDTO> list = projectContractMapper.page(page, dto);
        page.setRecords(list);
        return page;
    }

    /**
     * 下发收据提交
     * @param projectId 项目ID
     */
    private void sendReceiptSubmit(String projectId, String category) {
        // 获取项目
        DeclareEnt declareEnt = declareEntMapper.selectById(projectId);
        if(declareEnt == null) {
            throw new ValidationException("", "项目不存在");
        }
        // 获取项目合同信息
        ProjectReceipt projectReceipt = getReceiptFromDataBase(projectId, category);
        if(projectReceipt != null) {
            throw new ValidationException("", "已下发收据提交");
        }
        // 新建合同信息
        projectReceipt = new ProjectReceipt();
        projectReceipt.setId(UUIDGenerator.getUUID());
        projectReceipt.setCategory(category);
        projectReceipt.setProjectId(projectId);
        projectReceipt.setReason("2023年国家生物药技术创新中心技术攻关资金\n" +
                "（细胞疗法“揭榜挂帅”第一笔支持资金）");
        projectReceipt.create(SecurityUtils.getUserId());
        // 项目进度更新(合同提交
        if(DeclareCons.RECEIPT_CATEGORY.FIRST.getCode().equals(category)) {
            declareEnt.setProjectProgress(Constants.PROJECT_PROGRESS.FIRST_RECEIPT.name());
            declareEntMapper.updateById(declareEnt);
        }
        projectReceiptMapper.insert(projectReceipt);
    }

    /**
     * 提交收据
     * @param dto 提交收据模型
     */
    public void saveReceipt(SaveReceiptDTO dto) {
        ProjectReceipt projectReceipt = getReceiptFromDataBase(dto.getId(), dto.getCategory());

        // 判断收据是否允许修改
        isMaterialEditable(projectReceipt);
        BeanUtils.copyProperties(dto,projectReceipt,"id");
        projectReceipt.update(SecurityUtils.getUserId());
        projectReceiptMapper.updateById(projectReceipt);
    }

    /**
     * 提交收据
     * @param dto 提交收据信息
     */
    public void submitReceipt(SubmitReceiptDTO dto) {
        ProjectReceipt projectReceipt = getReceiptFromDataBase(dto.getId(), dto.getCategory());

        // 判断是否允许修改
        isMaterialEditable(projectReceipt);
        projectReceipt.setReceipt(JSON.toJSONString(dto.getReceipt()));
        // 进入审批状态
        projectReceipt.setAuditStatus(Constants.EN_DECLARE_STATUS.APPROVE.getCode());
        projectReceipt.setSubmitDate(ZonedDateTime.now());
        projectReceiptMapper.updateById(projectReceipt);
        // 创建历史
        declareFlowService.createHistory(Constants.RECEIPT_ASSIGN_ROLE.SUBMIT.getDesc(),
                projectReceipt.getId(),
                "",
                // 判断是首次还是退回修改
                Constants.EN_DECLARE_STATUS.BACK.getCode().equals(projectReceipt.getAuditStatus()) ? "back" : "start");
    }

    /**
     * 审核收据
     * @param dto 审核结果内容
     */
    public void auditReceipt(DeclareEntHandlerDTO dto) {
        ProjectReceipt projectReceipt = getReceiptFromDataBase(dto.getDeclareEntId(), dto.getCategory());

        if(Objects.isNull(projectReceipt)) {
            throw new ValidationException("","申报项目收据信息不存在");
        }
        // 审核
        handleAuditOperation(dto.getEvent(), projectReceipt);
        // 设置意见
        projectReceipt.setAuditRemark(dto.getRemark());
        // 设置更新时间
        projectReceipt.update(SecurityUtils.getUserId());
        projectReceiptMapper.updateById(projectReceipt);
        // 创建历史
        declareFlowService.createHistory(Constants.RECEIPT_ASSIGN_ROLE.AUDIT.getDesc(),
                projectReceipt.getId(),
                dto.getRemark(),
                dto.getEvent());
    }

    private void handleAuditOperation(String event, ProjectReceipt projectReceipt) {
        switch (event) {
            case "pass":
                projectReceipt.setAuditStatus(Constants.EN_DECLARE_STATUS.APPROVE_PASS.getCode());
                handleReceiptAuditPass(projectReceipt);
                break;
            case "reject":
                projectReceipt.setAuditStatus(Constants.EN_DECLARE_STATUS.APPROVE_REJECT.getCode());
                break;
            case "back":
                projectReceipt.setAuditStatus(Constants.EN_DECLARE_STATUS.BACK.getCode());
                break;
            default:
                break;
        }
    }

    private void handleReceiptAuditPass(ProjectReceipt projectReceipt) {
        // 更新项目进度
        DeclareEnt declareEnt = declareEntMapper.selectById(projectReceipt.getProjectId());
        // 更新为合同阶段结束
        // 区分一二三次合同提交
        if(Constants.PROJECT_PROGRESS.FIRST_RECEIPT.name().equals(declareEnt.getProjectProgress())
                && DeclareCons.RECEIPT_CATEGORY.FIRST.getCode().equals(projectReceipt.getCategory())) {
            declareEnt.setProjectProgress(Constants.PROJECT_PROGRESS.FIRST_RECEIPT_PASS.name());
        }else if(Constants.PROJECT_PROGRESS.SECOND_RECEIPT.name().equals(declareEnt.getProjectProgress())
                && DeclareCons.RECEIPT_CATEGORY.SECOND.getCode().equals(projectReceipt.getCategory())) {
            declareEnt.setProjectProgress(Constants.PROJECT_PROGRESS.SECOND_RECEIPT_PASS.name());
        }else if(Constants.PROJECT_PROGRESS.THIRD_RECEIPT.name().equals(declareEnt.getProjectProgress())
                && DeclareCons.RECEIPT_CATEGORY.THIRD.getCode().equals(projectReceipt.getCategory())) {
            declareEnt.setProjectProgress(Constants.PROJECT_PROGRESS.THIRD_RECEIPT_PASS.name());
        }else {
            throw new ValidationException("收据审核", "该项目状态不允许审核通过");
        }
        declareEnt.update(SecurityUtils.getUserId());
        declareEntMapper.updateById(declareEnt);
    }

    /**
     * 收据详情
     * @param id 项目Id
     * @return 收据详情
     */
    public ProjectReceiptDetailDTO receiptDetail(String id, String category) {
        ProjectReceiptDetailDTO dto = projectReceiptMapper.detail(id, category);
        if(dto == null) {
            DeclareEnt declareEnt = declareEntMapper.selectById(id);
            // 自动下发第二次收据提交逻辑
            if(Constants.NEW_PROJECT_STATUS.MID_INSPECTION_PASS.getCode().equals(declareEnt.getProjectStatus())
                    && DeclareCons.RECEIPT_CATEGORY.SECOND.getCode().equals(category)) {
                sendReceiptSubmit(id, category);
                dto = projectReceiptMapper.detail(id, category);
            }else if(Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE_PASS.getCode().equals(declareEnt.getProjectStatus())
                    && DeclareCons.RECEIPT_CATEGORY.THIRD.getCode().equals(category)) {
                sendReceiptSubmit(id, category);
                dto = projectReceiptMapper.detail(id, category);
            }else {
                throw new ValidationException("","暂无收据详情");
            }
        }
        dto.setReceipt(JSON.parseObject(dto.getReceiptStr(), AttachmentDTO.class));
        dto.setReceiptStr(null);
        dto.setTemplate(JSON.parseObject(dto.getTemplateStr(), AttachmentDTO.class));
        dto.setTemplateStr(null);
        dto.setHistory(getTaskHistory(dto.getId()));
        return dto;
    }

    /**
     * 分页查找收据
     * @param page 分页参数
     * @param dto 查询条件
     * @return 收据page
     */
    public Page<ProjectReceiptListDTO> receiptPage(Page<ProjectReceiptListDTO> page, QueryContractDTO dto) {
        dto.processQuery();
        List<ProjectReceiptListDTO> list = projectReceiptMapper.page(page, dto);
        page.setRecords(list);
        return page;
    }

    /**
     * 生成收据pdf
     * @param dto 保存收据DTO
     * @return 附件信息
     * @throws Exception 生成pdf异常
     */
    public AttachmentDTO generateReceiptPdf(GenerateReceiptDTO dto) throws Exception {
        ProjectReceipt projectReceipt = getReceiptFromDataBase(dto.getId(), dto.getCategory());

        // 判断是否允许修改
        isMaterialEditable(projectReceipt);
        BeanUtils.copyProperties(dto,projectReceipt,"id");
        // 生成收据编号
        generateReceiptNo(projectReceipt);
        // 获取收据模板
        Workbook book = new Workbook();
        try(InputStream in = new ClassPathResource("static/templates/receipt.xlsx").getInputStream()){
            book.loadFromStream(in);
        }
        Worksheet sheet = book.getWorksheets().get(0);
        // 收据编号
        sheet.get(2, 19).setText(String.valueOf(projectReceipt.getReceiptNo()));
        // 收款时间 非必填判断
        if(dto.getDate() != null) {
            // 年
            sheet.get(3, 16).setText(String.valueOf(dto.getDate().getYear()));
            // 月
            sheet.get(3, 19).setText(String.valueOf(dto.getDate().getMonthValue()));
            // 日
            sheet.get(3, 21).setText(String.valueOf(dto.getDate().getDayOfMonth()));
        }
        // 格式化金额并分隔为单个字符串
        BigDecimal amount = BigDecimal.valueOf(dto.getAmount()).setScale(2, RoundingMode.DOWN);
        List<String> amountList = Arrays.stream(amount.toString().replace(".", "").split(""))
                .map(String::valueOf).collect(Collectors.toList());
        // 小写金额
        for (int i = 0; i < amountList.size() && i < 9; i++) {
            sheet.get(6, 22-i).setText(amountList.get(amountList.size() - 1 - i));
        }
        // 人民币标识
        sheet.get(6, 22 - amountList.size()).setText("￥");
        // 大写金额
        for (int i = 0; i < 10; i++) {
            sheet.get(7, 21 - i * 2).setText(amountList.size() - i > 0
                    ? Constants.n2cMap.get(amountList.get(amountList.size()-1-i)) : "/");
        }
        // 联系人
        sheet.get(8, 3).setText(dto.getContacts());
        // 收款单位
        sheet.get(8, 18).setText(dto.getCompany());
        // 联系电话
        sheet.get(9, 3).setText(dto.getContactNum());
        // 邮箱
        sheet.get(10, 3).setText(dto.getEmail());
        // 开户名
        sheet.get(11, 3).setText(dto.getName());
        // 开户行
        sheet.get(12, 3).setText(dto.getBank());
        // 银行账号
        sheet.get(13, 3).setText(dto.getAccount());
        // 账户类型
        sheet.get(14, 3).setText(dto.getType());
        // 保存为字节数组用于上传
        byte[] bytes;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            sheet.saveToPdfStream(out);
            bytes = out.toByteArray();
        }
        // 上传文件
        UploadFile uploadfile = new UploadFile("收据", bytes, "pdf");
        String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
        // 异步删除旧的PDF文件
        fileService.asyncDeleteFile(projectReceipt.getTemplate());
        // 保存并返回生成的模板
        AttachmentDTO attachmentDTO = new AttachmentDTO();
        attachmentDTO.setUrl(ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
        attachmentDTO.setSize((long) bytes.length);
        attachmentDTO.setName("收据.pdf");
        projectReceipt.setTemplate(JSON.toJSONString(attachmentDTO));
        projectReceipt.update(SecurityUtils.getUserId());
        projectReceiptMapper.update(projectReceipt, new LambdaUpdateWrapper<ProjectReceipt>()
                .set(ProjectReceipt::getTemplate,JSON.toJSONString(attachmentDTO))
                .eq(ProjectReceipt::getId,projectReceipt.getId()));
        return attachmentDTO;
    }

    /**
     * 同步锁收据编号生成(避免并发请求生成重复编号
     * @param projectReceipt 项目收据信息
     */
    private synchronized void generateReceiptNo(ProjectReceipt projectReceipt) {
        if(projectReceipt.getReceiptNo() != null && projectReceipt.getReceiptNo() > 0) {
            return;
        }
        // 查找当前最大的收据编号
        Long no = projectReceiptMapper.maxNo(projectReceipt.getProjectId(), projectReceipt.getCategory());
        if(no == null) {
            no = 0L;
        }
        no %= 1000L;
        no++;
        // 年份
        int year = DateUtil.thisYear();
        // 申报序号
        DeclareEnt declareEnt = declareEntMapper.selectById(projectReceipt.getProjectId());
        DeclareProject declareProject = declareProjectMapper.selectById(declareEnt.getDeclareId());
        int projectNo = Integer.parseInt(declareProject.getDeclareNo());
        // 第几次收据
        int category = Integer.parseInt(projectReceipt.getCategory());
        // 收据编号
        Long receiptNo = year * 10000000L + projectNo * 100000L + category * 1000L + no;
        projectReceipt.setReceiptNo(receiptNo);
        // 保存收据编号
        projectReceiptMapper.update(null, new LambdaUpdateWrapper<ProjectReceipt>()
                .set(ProjectReceipt::getReceiptNo, projectReceipt.getReceiptNo())
                .eq(ProjectReceipt::getId, projectReceipt.getId()));
    }

    /**
     * 获取审核记录
     * @param processId 流程Id
     * @return 审核记录列表
     */
    private List<RuNodeTaskHistoryShowDTO> getTaskHistory(String processId) {
        // 不想写sql了 翻一下吧 需要优化的时候再写xml
        List<RuNodeTaskHistory> historyList = ruNodeTaskHistoryMapper.selectList(new LambdaQueryWrapper<RuNodeTaskHistory>()
                .eq(RuNodeTaskHistory::getRuProcessId, processId)
                // 创建时间倒叙
                .orderByDesc(RuNodeTaskHistory::getCreatedDate));
        return CollUtils.copyList(historyList, RuNodeTaskHistoryShowDTO.class);
    }

    /**
     * 判断项目材料是否允许修改
     * @param material 项目材料
     */
    private void isMaterialEditable(ProjectMaterial material) {
        // 判断是否下发材料提交
        if (material == null) {
            throw new ValidationException("", "当前状态不允许修改");
        }
        // 判断材料状态是否允许修改
        if (!(Constants.EN_DECLARE_STATUS.SUBMIT.getCode().equals(material.getAuditStatus())
                || Constants.EN_DECLARE_STATUS.BACK.getCode().equals(material.getAuditStatus()))) {
            throw new ValidationException("", "当前状态不允许修改");
        }
    }

    /**
     * 手动切换状态
     */
    public void fixStatus() {
        // 所有通过评审的项目
        Set<String> declareEntIdSet = declareEntMapper.selectList(new LambdaQueryWrapper<DeclareEnt>()
                        .select(DeclareEnt::getId)
                        .eq(DeclareEnt::getProjectStatus, Constants.NEW_PROJECT_STATUS.FINAL_REVIEW_PASS.getCode()))
                        .stream()
                        .map(DeclareEnt::getId)
                        .collect(Collectors.toSet());
        // 已处理的项目
        Set<String> handledIdList = projectContractMapper.selectList(new LambdaQueryWrapper<ProjectContract>()
                        .in(ProjectContract::getProjectId, declareEntIdSet)
                        .select(ProjectMaterial::getProjectId))
                        .stream()
                        .map(ProjectMaterial::getProjectId)
                        .collect(Collectors.toSet());
        // 取差集
        declareEntIdSet.removeAll(handledIdList);
        // 手动进入合同阶段
        declareEntIdSet.forEach(this::sendContractSubmit);
    }

    private ProjectReceipt getReceiptFromDataBase(String projectId, String category) {
        return projectReceiptMapper.selectOne(new LambdaQueryWrapper<ProjectReceipt>()
                .eq(ProjectReceipt::getProjectId, projectId)
                // 收据次数
                .eq(ProjectReceipt::getCategory, category));
    }
}
