package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

@Data
@TableName("t_user_third")
public class UserThird extends BaseDomain{

    @TableId("ID")
    private String id;

    @TableField("user_id")
    private String userId;

    @TableField("third_key")
    private String thirdKey;//第三方标记

    @TableField("third_type")
    private String thirdType;//第三方类型 wechat

}
