package com.fengyun.udf.uaa.service.instrument;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.instrument.Instrument;
import com.fengyun.udf.uaa.domain.instrument.InstrumentConsumable;
import com.fengyun.udf.uaa.domain.instrument.InstrumentForbiddenTime;
import com.fengyun.udf.uaa.domain.system.DictValue;
import com.fengyun.udf.uaa.dto.request.instrument.*;
import com.fengyun.udf.uaa.dto.response.instrument.*;
import com.fengyun.udf.uaa.mapper.instrument.ConsumableMapper;
import com.fengyun.udf.uaa.mapper.instrument.InstrumentConsumableMapper;
import com.fengyun.udf.uaa.mapper.instrument.InstrumentForbiddenTimeMapper;
import com.fengyun.udf.uaa.mapper.instrument.InstrumentMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.holiday.HolidayService;
import com.fengyun.udf.uaa.service.system.DictService;
import com.fengyun.udf.util.UUIDGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仪器 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class InstrumentService extends BaseService {

    private final InstrumentMapper instrumentMapper;
    private final InstrumentForbiddenTimeMapper forbiddenTimeMapper;
    private final InstrumentConsumableMapper instrumentConsumableMapper;
    private final ConsumableMapper consumableMapper;
    private final DictService dictService;
    private final HolidayService holidayService;
    private final InstrumentOrderService instrumentOrderService;

    public void create(InstrumentCreateDTO dto) {
        Instrument instrument = new Instrument();
        BeanUtils.copyProperties(dto, instrument);
        instrument.setId(UUIDGenerator.getUUID());
        instrument.setDeleted(false);
        instrument.create(SecurityUtils.getUserId());

        instrumentMapper.insert(instrument);

        //仪器耗材
        createConsumable(instrument.getId(), dto.getConsumableList());
    }

    private void createConsumable(String id, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (String s : list) {
                InstrumentConsumable instrumentConsumable = new InstrumentConsumable();
                instrumentConsumable.setInstrumentId(id);
                instrumentConsumable.setConsumableId(s);
                instrumentConsumableMapper.insert(instrumentConsumable);
            }
        }
    }

    public void update(InstrumentUpdateDTO dto) {
        Instrument instrument = instrumentMapper.selectById(dto.getId());
        if (instrument == null) {
            throw new ValidationException("error", "不存在");
        }
        BeanUtils.copyProperties(dto, instrument);
        instrument.update(SecurityUtils.getUserId());

        instrumentMapper.updateById(instrument);

        //仪器耗材
        instrumentConsumableMapper.delete(new LambdaQueryWrapper<InstrumentConsumable>().eq(InstrumentConsumable::getInstrumentId, dto.getId()));
        createConsumable(instrument.getId(), dto.getConsumableList());
    }

    public InstrumentDetailDTO detail(String id) {
        InstrumentDetailDTO dto = new InstrumentDetailDTO();
        Instrument instrument = instrumentMapper.selectById(id);
        if (instrument == null) {
            throw new ValidationException("error", "不存在");
        }
        BeanUtils.copyProperties(instrument, dto);
        List<ConsumableListDTO> list = instrumentOrderService.getInstrumentConsumable(id);
        dto.setList(list);
        return dto;
    }

    public Page<InstrumentListDTO> page(Page page, InstrumentQueryDTO dto) {
        List<InstrumentListDTO> list = instrumentMapper.selectPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public List<InstrumentListDTO> export(InstrumentQueryDTO dto) {
        List<InstrumentListDTO> list = instrumentMapper.selectPage(null, dto);
        List<DictValue> dictValues = dictService.getByCode("INSTRUMENT_STATUS");
        Map<String, String> statusMap = dictValues.stream().collect(Collectors.toMap(DictValue::getValue, DictValue::getLabel));
        int i = 1;
        for (InstrumentListDTO listDTO : list) {
            listDTO.setIndex(i);
            listDTO.setStatusStr(statusMap.get(listDTO.getStatus()));
            i++;
        }
        return list;
    }

    public void delete(String id) {
        Instrument instrument = instrumentMapper.selectById(id);
        if (instrument != null) {
            instrument.setDeleted(true);
            instrument.update(SecurityUtils.getUserId());

            instrumentMapper.updateById(instrument);
        }
    }

    public void forbiddenTime(InstrumentForbiddenDTO dto) {
        Instrument instrument = instrumentMapper.selectById(dto.getId());
        if (instrument == null) {
            throw new ValidationException("error", "仪器不存在");
        }

        forbiddenTimeMapper.delete(new LambdaQueryWrapper<InstrumentForbiddenTime>()
                .eq(InstrumentForbiddenTime::getInstrumentId, dto.getId()));

        if (CollectionUtils.isNotEmpty(dto.getList())) {
            for (InstrumentForbiddenTimeDTO forbiddenTimeDTO : dto.getList()) {
                InstrumentForbiddenTime forbiddenTime = new InstrumentForbiddenTime();
                BeanUtils.copyProperties(forbiddenTimeDTO, forbiddenTime);
                forbiddenTime.setInstrumentId(dto.getId());
                forbiddenTime.setCreatedId(SecurityUtils.getUserId());
                forbiddenTime.setCreatedDate(LocalDateTime.now());
                forbiddenTimeMapper.insert(forbiddenTime);
            }
        }
    }

    public List<InstrumentForbiddenTimeDTO> forbiddenTimeList(String id) {
        List<InstrumentForbiddenTimeDTO> result = new ArrayList<>();
        List<InstrumentForbiddenTime> list = forbiddenTimeMapper.selectList(new LambdaQueryWrapper<InstrumentForbiddenTime>()
                .eq(InstrumentForbiddenTime::getInstrumentId, id));

        for (InstrumentForbiddenTime instrumentForbiddenTime : list) {
            InstrumentForbiddenTimeDTO forbiddenTimeDTO = new InstrumentForbiddenTimeDTO();
            BeanUtils.copyProperties(instrumentForbiddenTime, forbiddenTimeDTO);
            result.add(forbiddenTimeDTO);
        }
        return result;
    }

    public Page<InstrumentShowListDTO> pageShow(Page page, InstrumentQueryDTO dto) {
        List<InstrumentShowListDTO> list = instrumentMapper.selectShowPage(page, dto);
        if (CollectionUtils.isNotEmpty(list)) {
            boolean canOrder = true;

            LocalDate now = LocalDate.now();
//            DayOfWeek dayOfWeek = now.getDayOfWeek();
            //判断预约时间是否为节假日
            if (holidayService.isHoliday(now.toString())) {
                canOrder = false;
            }
            //判断预约时间是否为调休
//            if (dayOfWeek.getValue() > 5 && !holidayService.isMakeUp(now.toString())) {
//                canOrder = false;
//            }

            List<InstrumentOrderTimeDTO> times = new ArrayList<>();
            List<DictValue> dictValues = dictService.getByCode("INSTRUMENT_ORDER_TIME");
            int i = 0;
            for (DictValue dictValue : dictValues) {
                if (i <= 2) {
                    InstrumentOrderTimeDTO timeDTO = new InstrumentOrderTimeDTO();
                    timeDTO.setOrderDate(now);
                    timeDTO.setOrderTime(dictValue.getValue());
                    timeDTO.setOrderTimeStr(dictValue.getLabel());
                    timeDTO.setCanOrder(canOrder);
                    times.add(timeDTO);
                }
                i++;
            }

            for (InstrumentShowListDTO showListDTO : list) {
                List<InstrumentOrderTimeDTO> showTimes = times.stream().collect(Collectors.toList());

                Set<String> set = new HashSet<>();
                Map<String, Integer> map = new HashMap<>();

                if (canOrder) {
                    //获取禁用时间
                    set = instrumentOrderService.getForbiddenIndex(showListDTO.getId(), now);
                    //获取已约时间
                    map = instrumentOrderService.getAgreedTime(showListDTO.getId(), now);
                }
                for (InstrumentOrderTimeDTO showTime : showTimes) {
                    showTime.setPersonNum(showListDTO.getPersonNum());
                    if (canOrder) {
                        if (set.contains(showTime.getOrderTime())) {
                            showTime.setCanOrder(false);
                        }
                        if (map.containsKey(showTime.getOrderTime())) {
                            int nowPersonNum = map.get(showTime.getOrderTime());
                            if (nowPersonNum >= showTime.getPersonNum()) {
                                showTime.setCanOrder(false);
                            }
                            showTime.setNowPersonNum(nowPersonNum);
                        }
                    }
                }
                showListDTO.setTimes(showTimes);
            }
        }
        page.setRecords(list);
        return page;
    }


}

