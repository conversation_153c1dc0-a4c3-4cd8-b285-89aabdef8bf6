package com.fengyun.udf.uaa.dto.response.declareproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class FileDTO {

    @ApiModelProperty(value = "附件文件名")
    private String name;

    @ApiModelProperty(value = "文件格式")
    private String format;

    @ApiModelProperty(value = "是否必要")
    private String necessary;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板路径")
    private String templateUrl;

}
