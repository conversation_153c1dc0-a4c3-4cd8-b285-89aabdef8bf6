<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.area.AreaWorldMapper" >
    <resultMap id="BaseResultMap" type="com.fengyun.udf.uaa.domain.area.AreaWorld">
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="NUMERIC_CODE" property="numericCode" jdbcType="VARCHAR" />
        <result column="ALPHA3_CODE" property="alpha3Code" jdbcType="VARCHAR" />
        <result column="NAME_PINYIN" property="namePinyin" jdbcType="VARCHAR" />
        <result column="NAME_CN" property="nameCn" jdbcType="VARCHAR" />
        <result column="NAME_EN" property="nameEn" jdbcType="VARCHAR" />
        <result column="LEVEL" property="level" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="VARCHAR" />
        <result column="SEQUENCE" property="sequence" jdbcType="INTEGER" />
        <result column="STATUS" property="status" jdbcType="VARCHAR" />
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Area_Special_Column_List" >
        ID, NUMERIC_CODE, ALPHA3_CODE, NAME_PINYIN, NAME_CN, NAME_EN, LEVEL, TYPE, SEQUENCE, STATUS, PARENT_ID
    </sql>
    <sql id="Base_Column_List" >
        <include refid="Area_Special_Column_List" />
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" >
        <property name="aliasTable" value="t" />
        </include>
        from t_area_world t
        where t.ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.fengyun.udf.uaa.domain.area.AreaWorld" >
        delete from t_area_world
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.fengyun.udf.uaa.domain.area.AreaWorld" >
        insert into t_area_world (ID, NUMERIC_CODE, ALPHA3_CODE,
            NAME_PINYIN, NAME_CN, NAME_EN,
            LEVEL, TYPE, SEQUENCE,
            STATUS, PARENT_ID, VERSION,
            CREATED_ID, CREATED_DATE, UPDATED_ID,
            UPDATED_DATE)
        values (#{id,jdbcType=VARCHAR}, #{numericCode,jdbcType=VARCHAR}, #{alpha3Code,jdbcType=VARCHAR},
            #{namePinyin,jdbcType=VARCHAR}, #{nameCn,jdbcType=VARCHAR}, #{nameEn,jdbcType=VARCHAR},
            #{level,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, #{sequence,jdbcType=INTEGER},
            #{status,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER},
            #{createdId,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{updatedId,jdbcType=VARCHAR},
            #{updatedDate,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.fengyun.udf.uaa.domain.area.AreaWorld" >
        update t_area_world
        set NUMERIC_CODE = #{numericCode,jdbcType=VARCHAR},
            ALPHA3_CODE = #{alpha3Code,jdbcType=VARCHAR},
            NAME_PINYIN = #{namePinyin,jdbcType=VARCHAR},
            NAME_CN = #{nameCn,jdbcType=VARCHAR},
            NAME_EN = #{nameEn,jdbcType=VARCHAR},
            LEVEL = #{level,jdbcType=INTEGER},
            TYPE = #{type,jdbcType=VARCHAR},
            SEQUENCE = #{sequence,jdbcType=INTEGER},
            STATUS = #{status,jdbcType=VARCHAR},
            PARENT_ID = #{parentId,jdbcType=VARCHAR},
            VERSION = #{version,jdbcType=INTEGER},
            CREATED_ID = #{createdId,jdbcType=VARCHAR},
            CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            UPDATED_ID = #{updatedId,jdbcType=VARCHAR},
            UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <select id="findAllByLevelAndParentIdAndStatusNotOrderBySequenceAsc" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" ></include>
        from t_area_world t
        where t.LEVEL = #{level}
            AND t.PARENT_ID = #{parentId}
            AND t.STATUS  <![CDATA[ <> ]]> #{status}
        order by SEQUENCE asc
    </select>

    <select id="findAllByLevelAndStatusNotOrderBySequenceAsc" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from t_area_world t
        where t.LEVEL = #{level}
        AND t.STATUS != #{status}
        order by SEQUENCE asc
    </select>

    <select id="selectByPrimaryKeys" resultType="com.fengyun.udf.uaa.domain.area.AreaWorld">
        select <include refid="Base_Column_List"></include>
        from t_area_world t
        WHERE t.ID in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findAll" resultType="com.fengyun.udf.uaa.domain.area.AreaWorld">
        select <include refid="Base_Column_List" ></include>
        from t_area_world t
    </select>

</mapper>