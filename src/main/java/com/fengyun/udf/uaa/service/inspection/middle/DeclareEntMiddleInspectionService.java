package com.fengyun.udf.uaa.service.inspection.middle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.constant.DeclareCons;
import com.fengyun.udf.uaa.domain.declareproject.DeclareEnt;
import com.fengyun.udf.uaa.domain.flowform.*;
import com.fengyun.udf.uaa.domain.inspection.middle.*;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.inspection.middle.DeclareEntMiddleInspectionAuditDTO;
import com.fengyun.udf.uaa.dto.request.inspection.middle.*;
import com.fengyun.udf.uaa.dto.response.declareproject.RuNodeTaskHistoryShowDTO;
import com.fengyun.udf.uaa.dto.response.inspection.middle.*;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareEntMapper;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareProjectMapper;
import com.fengyun.udf.uaa.mapper.flowform.*;
import com.fengyun.udf.uaa.mapper.inspection.middle.*;
import com.fengyun.udf.uaa.mapper.system.UserMapper;
import com.fengyun.udf.uaa.service.declareproject.DeclareFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.*;

import static com.fengyun.udf.uaa.util.BigDecimalUtil.double2BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@SuppressWarnings("all")
@Transactional(rollbackFor = Exception.class)
public class DeclareEntMiddleInspectionService {

    @Autowired
    private DeclareEntMiddleAchievementPaperMapper achievementPaperMapper;
    @Autowired
    private DeclareEntMiddleAchievementPatentMapper achievementPatentMapper;
    @Autowired
    private DeclareEntMiddleAttachmentMapper attachmentMapper;
    @Autowired
    private DeclareEntMiddleBasicInfoMapper basicInfoMapper;
    @Autowired
    private DeclareEntMiddleCompletionMapper completionMapper;
    @Autowired
    private DeclareEntMiddleFundingMapper fundingMapper;
    @Autowired
    private DeclareEntMiddleFundingUsageMapper fundingUsageMapper;
    @Autowired
    private DeclareEntMiddleParticipantMapper participantMapper;
    @Autowired
    private DeclareEntMiddleResponsibleUnitMapper responsibleUnitMapper;
    @Autowired
    private DeclareEntMiddleSuzhouUnitMapper suzhouUnitMapper;
    @Autowired
    private DeclareEntMiddleChargePrincipalMapper chargePrincipalMapper;
    @Autowired
    private DeclareEntMiddleTalentAwardsMapper talentAwardsMapper;
    @Autowired
    private DeclareEntMiddleFundingRemarkMapper fundingRemarkMapper;

    @Autowired
    private ConditionMapper conditionMapper;
    @Autowired
    private PersonnelSituationMapper situationMapper;
    @Autowired
    private ResponsibleUnitMapper unitMapper;
    @Autowired
    private ResponsibleUnitInfoMapper unitInfoMapper;
    @Autowired
    private ProjectScheduleMapper scheduleMapper;
    @Autowired
    private SourceFundsMapper fundsMapper;
    @Autowired
    private SpendingBudgetMapper budgetMapper;
    @Autowired
    private AttachmentFormMapper attachmentFormMapper;
    @Autowired
    private DeclareEntMapper declareEntMapper;
    @Autowired
    private DeclareProjectMapper declareProjectMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DeclareFlowService declareFlowService;

    /**
     * 提交中期检查
     * @param projectId 项目ID
     */
    public void submitMiddleInspection(String projectId) {
        DeclareEntMiddleBasicInfo basicInfo = getBasicInfoFromDataBase(projectId);
        if(StrUtil.isBlank(basicInfo.getId())) {
            throw new ValidationException("中期检查", "单位基本信息还未保存, 请先保存单位基本信息");
        }
        boolean firstTimeSubmit = DeclareCons.MIDDLE_INSPECTION_STATUS.SAVE.getCode().equals(basicInfo.getStatus());
        basicInfo.setStatus(DeclareCons.MIDDLE_INSPECTION_STATUS.SUBMIT.getCode());
        if(firstTimeSubmit) {
            basicInfo.create(SecurityUtils.getUserId());
        }else {
            basicInfo.update(SecurityUtils.getUserId());
        }

        basicInfoMapper.updateById(basicInfo);

        // 更新项目状态为中期检查审核中
        pushDeclareEntProjectStatus2MidInspectionAuditing(projectId);

        // 创建提交历史
        declareFlowService.createHistory("中期检查提交",
                basicInfo.getId(),
                null,
                firstTimeSubmit? "start": "restart");
    }

    private void pushDeclareEntProjectStatus2MidInspectionAuditing(String projectId) {
        // 更新项目状态为中期检查审核中
        DeclareEnt declareEnt = declareEntMapper.selectById(projectId);
        declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.MID_INSPECTION_AUDITING.getCode());
        declareEntMapper.updateById(declareEnt);
    }

    /**
     * 审核中期检查
     * @param dto 审核dto
     */
    public void auditMiddleInspection(DeclareEntMiddleInspectionAuditDTO dto) {
        DeclareEntMiddleBasicInfo basicInfo = getBasicInfoFromDataBase(dto.getProjectId());
        if(!DeclareCons.MIDDLE_INSPECTION_STATUS.SUBMIT.getCode().equals(basicInfo.getStatus())) {
            throw new ValidationException("中期检查", "该状态不允许审核, 请刷新列表后重新操作");
        }
        // 状态
        basicInfo.setStatus(DeclareCons.MIDDLE_INSPECTION_AUDIT_OPERATION.getByCode(dto.getEvent()).getStatusCode());
        // 保存最新审核意见
        basicInfo.setAuditOpinion(dto.getRemark());
        basicInfoMapper.updateById(basicInfo);
        // 创建历史
        declareFlowService.createHistory("中期检查审核",
                basicInfo.getId(),
                dto.getRemark(),
                dto.getEvent());
    }

    /**
     * 分页查找中期检查审核列表
     * @param page 分页参数
     * @param query 查询参数
     * @return 审核分页列表
     */
    public Page<DeclareEntMiddleBasicInfoListDTO> pageAudit(
            Page<DeclareEntMiddleBasicInfoListDTO> page, DeclareEntMiddleInspectionQueryDTO query) {
        List<DeclareEntMiddleBasicInfoListDTO> result = basicInfoMapper.page(page, query);
        page.setRecords(result);
        return page;
    }

    /**
     * 保存单位基本信息
     *
     * @param dto 单位基本信息聚合DTO
     */
    public void saveUnitBasicInfo(UnitBasicInfoSaveDTO dto) {
        DeclareEntMiddleBasicInfoSaveDTO basicInfo = dto.getBasicInfo();
        DeclareEntMiddleResponsibleUnitSaveDTO responsibleUnit = dto.getResponsibleUnit();
        DeclareEntMiddleSuzhouUnitSaveDTO suzhouUnit = dto.getSuzhouUnit();
        // 保存基本信息
        saveBasicInfo(basicInfo);
        // 保存责任单位信息
        saveResponsibleUnit(responsibleUnit);
        // 保存苏州单位信息
        saveSuzhouUnit(suzhouUnit);
    }

    /**
     * 保存项目完成情况
     *
     * @param dto 项目完成情况DTO
     */
    public void saveProjectCompletion(ProjectCompletionSaveDTO dto) {
        DeclareEntMiddleCompletionSaveDTO completion = dto.getCompletion();
        String projectId = dto.getProjectId();

        // 保存项目完成情况
        completion.setProjectId(projectId);
        saveProjectCompletion(completion);

        // 保存项目完成情况
        savePaper(projectId, dto.getPaper());
        // 保存专利申请情况
        savePatent(projectId, dto.getPatent());
    }

    /**
     * 保存:项目人员情况
     *
     * @param dto 项目人员情况DTO
     */
    public void saveProjectParticipant(ProjectParticipantSaveDTO dto) {
        String projectId = dto.getProjectId();
        // 保存项目承担单位及负责人
        saveChargePrincipal(projectId, dto.getChargePrincipal());
        // 保存项目项目人员情况
        saveParticipant(projectId, dto.getParticipants());
        // 保存人才申报及获评情况
        saveTalentAwards(projectId, dto.getTalentAwards());
    }

    /**
     * 保存:附件
     *
     * @param dto 附件DTO
     */
    public void saveProjectAttachment(DeclareEntMiddleAttachmentSaveDTO dto) {
        DeclareEntMiddleAttachment entity = getProjectAttachmentFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        attachmentMapper.insertOrUpdate(entity);
    }

    /**
     * 保存:项目人员情况
     *
     * @param dto 项目人员情况DTO
     */
    public void saveProjectFunding(ProjectFundingSaveDTO dto) {
        // 保存项目经费到位情况
        saveProjectFunding(dto.getFunding());
        // 保存项目经费使用情况
        saveProjectFundingUsage(dto.getFundingUsage());
        // 保存经费使用详细说明
        saveFundingRemarks(dto.getProjectId(), dto.getFundingRemarks());
    }

    private void saveFundingRemarks(String projectId,
                                    List<DeclareEntMiddleFundingRemarkSaveDTO> dtoList) {
        // remove all
        removeFundingRemarksFromDataBase(projectId);
        // insert all
        List<DeclareEntMiddleFundingRemark> entities = BeanUtil.copyToList(
                dtoList, DeclareEntMiddleFundingRemark.class);
        int sort = 0;
        for (DeclareEntMiddleFundingRemark entity : entities) {
            entity.setProjectId(projectId);
            entity.setSortNum(++sort);
        }
        fundingRemarkMapper.insert(entities);
    }

    private void removeFundingRemarksFromDataBase(String projectId) {
        fundingRemarkMapper.delete(Wrappers.<DeclareEntMiddleFundingRemark>lambdaQuery()
                .eq(DeclareEntMiddleFundingRemark::getProjectId, projectId));
    }

    /**
     * 详情:单位基本信息
     *
     * @param projectId 项目ID
     * @return 单位基本信息
     */
    public UnitBasicInfoDetailDTO getUnitBasicInfoDetail(String projectId) {
        DeclareEntMiddleBasicInfoDetailDTO basicInfoDetail = getBasicInfoDetail(projectId);
        List<RuNodeTaskHistoryShowDTO> taskHistory = declareFlowService.getTaskHistory(basicInfoDetail.getId());
        return new UnitBasicInfoDetailDTO(
                basicInfoDetail,
                getResponsibleUnitDetail(projectId),
                getSuzhouUnitDetail(projectId),
                taskHistory);
    }

    /**
     * 详情:单位基本信息
     *
     * @param projectId 项目ID
     * @return 单位基本信息
     */
    public ProjectCompletionDetailDTO getProjectCompletionDetail(String projectId) {
        return new ProjectCompletionDetailDTO(
                projectId,
                getCompletionDetail(projectId),
                getAchievementPaperDetail(projectId),
                getAchievementPatentDetail(projectId)
        );
    }

    /**
     * 详情:阶段性成果
     *
     * @param projectId 项目ID
     * @return 阶段性成果
     */
    public ProjectParticipantDetailDTO getProjectParticipantDetail(String projectId) {
        return new ProjectParticipantDetailDTO(
                projectId,
                getChargePrincipalDetail(projectId),
                getParticipantDetail(projectId),
                getTalentAwardsDetail(projectId)
        );
    }

    private List<DeclareEntMiddleTalentAwardsDetailDTO> getTalentAwardsDetail(String projectId) {
        List<DeclareEntMiddleTalentAwards> entities = getTalentAwardsFromDataBase(projectId);
        return BeanUtil.copyToList(entities, DeclareEntMiddleTalentAwardsDetailDTO.class);
    }

    private List<DeclareEntMiddleFundingRemarkDetailDTO> getFundingRemarksDetail(String projectId) {
        List<DeclareEntMiddleFundingRemark> entities = getFundingRemarksFromDataBase(projectId);
        return BeanUtil.copyToList(entities, DeclareEntMiddleFundingRemarkDetailDTO.class);
    }

    private List<DeclareEntMiddleFundingRemark> getFundingRemarksFromDataBase(String projectId) {
        return fundingRemarkMapper.selectList(Wrappers
                .<DeclareEntMiddleFundingRemark>lambdaQuery()
                .eq(DeclareEntMiddleFundingRemark::getProjectId, projectId)
                .orderByAsc(DeclareEntMiddleFundingRemark::getSortNum)
        );
    }


    private List<DeclareEntMiddleTalentAwards> getTalentAwardsFromDataBase(String projectId) {
        return talentAwardsMapper.selectList(Wrappers
                .<DeclareEntMiddleTalentAwards>lambdaQuery()
                .eq(DeclareEntMiddleTalentAwards::getProjectId, projectId)
                .orderByAsc(DeclareEntMiddleTalentAwards::getSortNum)
        );
    }

    private DeclareEntMiddleChargePrincipalDetailDTO getChargePrincipalDetail(String projectId) {
        DeclareEntMiddleChargePrincipal entity = getChargePrincipaFromDataBase(projectId);
        DeclareEntMiddleChargePrincipalDetailDTO dto = new DeclareEntMiddleChargePrincipalDetailDTO();
        BeanUtil.copyProperties(entity, dto);
        dto.setProjectId(projectId);
        return dto;
    }

    private DeclareEntMiddleChargePrincipal getChargePrincipaFromDataBase(String projectId) {
        return Optional.ofNullable(chargePrincipalMapper.selectOne(Wrappers
                        .<DeclareEntMiddleChargePrincipal>lambdaQuery()
                        .eq(DeclareEntMiddleChargePrincipal::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleChargePrincipal::new);
    }

    /**
     * 详情:项目经费到位与使用情况
     *
     * @param projectId 项目ID
     * @return 项目经费到位与使用情况
     */
    public ProjectFundingDetailDTO getProjectFundingDetail(String projectId) {
        return new ProjectFundingDetailDTO(
                getFundingDetail(projectId),
                getFundingUsageDetail(projectId),
                getFundingRemarksDetail(projectId)
        );
    }

    /**
     * 详情:附件
     *
     * @param projectId 项目ID
     * @return 附件
     */
    public DeclareEntMiddleAttachmentDetailDTO getAttachmentDetail(String projectId) {
        DeclareEntMiddleAttachment entity = getProjectAttachmentFromDataBase(projectId);
        DeclareEntMiddleAttachmentDetailDTO dto = new DeclareEntMiddleAttachmentDetailDTO();
        BeanUtil.copyProperties(entity, dto);
        dto.setProjectId(projectId);
        // 是否企业类项目(前端校验用
        DeclareEntMiddleResponsibleUnit responsibleUnit = getResponsibleUnitFromDataBase(projectId);
        dto.setCompanyType(responsibleUnit.getProjectType());
        dto.setProjectType(responsibleUnit.getProjectType());
        // 是否在苏州工业园区注册公司(前端校验用
        DeclareEntMiddleSuzhouUnit suzhouUnit = getSuzhouUnitFromDataBase(projectId);
        dto.setSipRegister(suzhouUnit.getSipRegister());
        // 是否在苏州工业园区租赁实地场地
        dto.setSipLease(suzhouUnit.getSipLease());

        return dto;
    }

    /**
     * 从数据库获取:附件
     *
     * @param projectId 项目ID
     * @return 附件
     */
    private DeclareEntMiddleAttachment getProjectAttachmentFromDataBase(String projectId) {
        return Optional.ofNullable(attachmentMapper.selectOne(Wrappers.<DeclareEntMiddleAttachment>lambdaQuery()
                        .eq(DeclareEntMiddleAttachment::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleAttachment::new);
    }

    /**
     * 保存或更新:项目经费到位情况
     *
     * @param dto 项目经费到位情况
     */
    private void saveProjectFundingUsage(DeclareEntMiddleFundingUsageSaveDTO dto) {
        DeclareEntMiddleFundingUsage entity = getProjectFundingUsageFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        fundingUsageMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取:项目经费到位情况
     *
     * @param projectId 项目ID
     * @return 项目经费到位情况
     */
    private DeclareEntMiddleFundingUsage getProjectFundingUsageFromDataBase(String projectId) {
        return Optional.ofNullable(fundingUsageMapper.selectOne(Wrappers.<DeclareEntMiddleFundingUsage>lambdaQuery()
                .eq(DeclareEntMiddleFundingUsage::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleFundingUsage::new);
    }

    /**
     * 保存或更新:项目经费到位情况
     *
     * @param dto 项目经费到位情况
     */
    private void saveProjectFunding(DeclareEntMiddleFundingSaveDTO dto) {
        DeclareEntMiddleFunding entity = getProjectFundingFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        fundingMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取:项目经费到位情况
     *
     * @param projectId 项目ID
     * @return 项目经费到位情况
     */
    private DeclareEntMiddleFunding getProjectFundingFromDataBase(String projectId) {
        return Optional.ofNullable(fundingMapper.selectOne(Wrappers.<DeclareEntMiddleFunding>lambdaQuery()
                        .eq(DeclareEntMiddleFunding::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleFunding::new);
    }

    private void saveChargePrincipal(String projectId,
                                     DeclareEntMiddleChargePrincipalSaveDTO dto) {
        DeclareEntMiddleChargePrincipal entity = getChargePrincipalFromDataBase(projectId);
        BeanUtil.copyProperties(dto, entity);
        entity.setProjectId(projectId);
        chargePrincipalMapper.insertOrUpdate(entity);
    }

    private DeclareEntMiddleChargePrincipal getChargePrincipalFromDataBase(String projectId) {
        return Optional.ofNullable(chargePrincipalMapper.selectOne(Wrappers
                        .<DeclareEntMiddleChargePrincipal>lambdaQuery()
                        .eq(DeclareEntMiddleChargePrincipal::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleChargePrincipal::new);
    }

    private void saveTalentAwards(String projectId,
                                  List<DeclareEntMiddleTalentAwardsSaveDTO> talentAwards) {
        // remove all
        removeTalentAwardsFromDataBase(projectId);
        // insert all
        List<DeclareEntMiddleTalentAwards> entities = BeanUtil.copyToList(
                talentAwards, DeclareEntMiddleTalentAwards.class);
        int sort = 0;
        for (DeclareEntMiddleTalentAwards entity : entities) {
            entity.setProjectId(projectId);
            entity.setSortNum(++sort);
        }
        talentAwardsMapper.insert(entities);
    }

    private void removeTalentAwardsFromDataBase(String projectId) {
        talentAwardsMapper.delete(Wrappers.<DeclareEntMiddleTalentAwards>lambdaQuery()
                .eq(DeclareEntMiddleTalentAwards::getProjectId, projectId));
    }

    /**
     * 保存或更新:项目人员情况
     *
     * @param projectId 项目ID
     * @param patent 项目人员情况
     */
    private void saveParticipant(String projectId, List<DeclareEntMiddleParticipantSaveDTO> dtoList) {
        // remove all
        removeParticipantFromDataBase(projectId);
        // insert all
        List<DeclareEntMiddleParticipant> entities = BeanUtil.copyToList(dtoList, DeclareEntMiddleParticipant.class);
        int sort = 0;
        for (DeclareEntMiddleParticipant entity : entities) {
            entity.setProjectId(projectId);
            entity.setSortNum(++sort);
        }
        participantMapper.insert(entities);
    }

    /**
     * 从数据库获得:项目人员情况
     *
     * @param projectId 项目ID
     * @return 项目人员情况
     */
    private List<DeclareEntMiddleParticipant> getParticipantFromDataBase(String projectId) {
        return participantMapper.selectList(Wrappers.<DeclareEntMiddleParticipant>lambdaQuery()
                .eq(DeclareEntMiddleParticipant::getProjectId, projectId)
                .orderByAsc(DeclareEntMiddleParticipant::getSortNum)
        );
    }

    /**
     * 从数据库删除:项目人员情况
     *
     * @param projectId 项目ID
     */
    private void removeParticipantFromDataBase(String projectId) {
        participantMapper.delete(Wrappers.<DeclareEntMiddleParticipant>lambdaQuery()
                .eq(DeclareEntMiddleParticipant::getProjectId, projectId));
    }

    /**
     * 保存或更新项目阶段性成果-专利申请情况
     *
     * @param projectId 项目ID
     * @param patent 项目阶段性成果-专利申请情况
     */
    private void savePatent(String projectId, List<DeclareEntMiddleAchievementPatentSaveDTO> dtoList) {
        // remove all
        removePatentFromDataBase(projectId);
        // insert all
        List<DeclareEntMiddleAchievementPatent> entities = BeanUtil.copyToList(
                dtoList, DeclareEntMiddleAchievementPatent.class);
        int sort = 0;
        for (DeclareEntMiddleAchievementPatent entity : entities) {
            entity.setProjectId(projectId);
            entity.setSortNum(++sort);
        }
        achievementPatentMapper.insert(entities);
    }

    /**
     * 从数据库获得项目阶段性成果-专利申请情况
     *
     * @param projectId 项目ID
     * @return 项目阶段性成果-专利申请情况
     */
    private List<DeclareEntMiddleAchievementPatent> getPatentFromDataBase(String projectId) {
        return achievementPatentMapper.selectList(Wrappers.<DeclareEntMiddleAchievementPatent>lambdaQuery()
                .eq(DeclareEntMiddleAchievementPatent::getProjectId, projectId)
                .orderByAsc(DeclareEntMiddleAchievementPatent::getSortNum)
        );
    }

    /**
     * 从数据库删除项目阶段性成果-专利申请情况
     *
     * @param projectId 项目ID
     */
    private void removePatentFromDataBase(String projectId) {
        achievementPatentMapper.delete(Wrappers.<DeclareEntMiddleAchievementPatent>lambdaQuery()
                .eq(DeclareEntMiddleAchievementPatent::getProjectId, projectId));
    }

    /**
     * 保存或更新项目阶段性成果-论文发表情况
     *
     * @param projectId 项目ID
     * @param paper 项目阶段性成果-论文发表情况
     */
    private void savePaper(String projectId, List<DeclareEntMiddleAchievementPaperSaveDTO> dtoList) {
        // remove all
        removePaperFromDataBase(projectId);
        // insert all
        List<DeclareEntMiddleAchievementPaper> entities = BeanUtil.copyToList(
                dtoList, DeclareEntMiddleAchievementPaper.class);
        int sort = 0;
        for (DeclareEntMiddleAchievementPaper entity : entities) {
            entity.setProjectId(projectId);
            entity.setSortNum(++sort);
        }
        achievementPaperMapper.insert(entities);
    }

    /**
     * 从数据库获得项目阶段性成果-论文发表情况
     *
     * @param projectId 项目ID
     * @return 项目阶段性成果-论文发表情况
     */
    private List<DeclareEntMiddleAchievementPaper> getPaperFromDataBase(String projectId) {
        return achievementPaperMapper.selectList(Wrappers.<DeclareEntMiddleAchievementPaper>lambdaQuery()
                .eq(DeclareEntMiddleAchievementPaper::getProjectId, projectId)
                .orderByAsc(DeclareEntMiddleAchievementPaper::getSortNum)
        );
    }

    /**
     * 从数据库删除项目阶段性成果-论文发表情况
     *
     * @param projectId 项目ID
     */
    private void removePaperFromDataBase(String projectId) {
        achievementPaperMapper.delete(Wrappers.<DeclareEntMiddleAchievementPaper>lambdaQuery()
                .eq(DeclareEntMiddleAchievementPaper::getProjectId, projectId));
    }

    /**
     * 保存或更新项目完成情况
     *
     * @param dto 项目完成情况
     */
    private void saveProjectCompletion(DeclareEntMiddleCompletionSaveDTO dto) {
        DeclareEntMiddleCompletion entity = getProjectCompletionFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        completionMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取项目完成情况
     *
     * @param projectId 项目ID
     * @return 项目完成情况
     */
    private DeclareEntMiddleCompletion getProjectCompletionFromDataBase(String projectId) {
        return Optional.ofNullable(completionMapper.selectOne(Wrappers.<DeclareEntMiddleCompletion>lambdaQuery()
                        .eq(DeclareEntMiddleCompletion::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleCompletion::new);
    }


    /**
     * 保存或更新苏州单位信息
     *
     * @param dto 苏州单位信息
     */
    private void saveSuzhouUnit(DeclareEntMiddleSuzhouUnitSaveDTO dto) {
        DeclareEntMiddleSuzhouUnit entity = getSuzhouUnitFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        suzhouUnitMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取苏州单位信息
     *
     * @param projectId 项目ID
     * @return 苏州单位信息
     */
    private DeclareEntMiddleSuzhouUnit getSuzhouUnitFromDataBase(String projectId) {
        return Optional.ofNullable(suzhouUnitMapper.selectOne(Wrappers.<DeclareEntMiddleSuzhouUnit>lambdaQuery()
                        .eq(DeclareEntMiddleSuzhouUnit::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleSuzhouUnit::new);
    }

    /**
     * 保存或更新承担单位信息
     *
     * @param dto 承担单位信息
     */
    private void saveResponsibleUnit(DeclareEntMiddleResponsibleUnitSaveDTO dto) {
        DeclareEntMiddleResponsibleUnit entity = getResponsibleUnitFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        responsibleUnitMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取承担单位信息
     *
     * @param projectId 项目ID
     * @return 承担单位信息
     */
    private DeclareEntMiddleResponsibleUnit getResponsibleUnitFromDataBase(String projectId) {
        return Optional.ofNullable(responsibleUnitMapper.selectOne(Wrappers.<DeclareEntMiddleResponsibleUnit>lambdaQuery()
                        .eq(DeclareEntMiddleResponsibleUnit::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleResponsibleUnit::new);
    }

    /**
     * 获取详情:基础信息
     *
     * @param projectId 项目ID
     * @return 详情:基础信息
     */
    private DeclareEntMiddleBasicInfoDetailDTO getBasicInfoDetail(String projectId) {
        DeclareEntMiddleBasicInfo entity = getBasicInfoFromDataBase(projectId);
        DeclareEntMiddleBasicInfoDetailDTO dto = new DeclareEntMiddleBasicInfoDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutBasicInfo(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 承担单位基础信息
     *
     * @param projectId 项目ID
     * @return 详情:基础信息
     */
    private DeclareEntMiddleResponsibleUnitDetailDTO getResponsibleUnitDetail(String projectId) {
        DeclareEntMiddleResponsibleUnit entity = getResponsibleUnitFromDataBase(projectId);
        DeclareEntMiddleResponsibleUnitDetailDTO dto = new DeclareEntMiddleResponsibleUnitDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutResponsibleUnit(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 承担单位基础信息
     *
     * @param projectId 项目ID
     * @return 详情:基础信息
     */
    private DeclareEntMiddleSuzhouUnitDetailDTO getSuzhouUnitDetail(String projectId) {
        DeclareEntMiddleSuzhouUnit entity = getSuzhouUnitFromDataBase(projectId);
        DeclareEntMiddleSuzhouUnitDetailDTO dto = new DeclareEntMiddleSuzhouUnitDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutSuzhouUnit(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 项目完成情况
     *
     * @param projectId 项目ID
     * @return 详情:项目完成情况
     */
    private DeclareEntMiddleCompletionDetailDTO getCompletionDetail(String projectId) {
        DeclareEntMiddleCompletion entity = getProjectCompletionFromDataBase(projectId);
        DeclareEntMiddleCompletionDetailDTO dto = new DeclareEntMiddleCompletionDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutCompletion(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 论文发表情况
     *
     * @param projectId 项目ID
     * @return 详情:论文发表情况
     */
    private List<DeclareEntMiddleAchievementPaperDetailDTO> getAchievementPaperDetail(String projectId) {
        return BeanUtil.copyToList(getPaperFromDataBase(projectId), DeclareEntMiddleAchievementPaperDetailDTO.class);
    }

    /**
     * 获取详情: 专利申请情况
     *
     * @param projectId 项目ID
     * @return 详情:专利申请情况
     */
    private List<DeclareEntMiddleAchievementPatentDetailDTO> getAchievementPatentDetail(String projectId) {
        return BeanUtil.copyToList(getPatentFromDataBase(projectId), DeclareEntMiddleAchievementPatentDetailDTO.class);
    }

    /**
     * 获取详情: 其他阶段性成果
     *
     * @param projectId 项目ID
     * @return 详情:其他阶段性成果
     */
    private List<DeclareEntMiddleParticipantDetailDTO> getParticipantDetail(String projectId) {
        List<DeclareEntMiddleParticipant> participants = getParticipantFromDataBase(projectId);
        if(CollUtil.isEmpty(participants)) {
            return autoBringOutParticipant(projectId);
        }else {
            return BeanUtil.copyToList(participants, DeclareEntMiddleParticipantDetailDTO.class);
        }
    }

    /**
     * 获取详情: 项目经费到位情况
     *
     * @param projectId 项目ID
     * @return 详情:项目经费到位情况
     */
    private DeclareEntMiddleFundingDetailDTO getFundingDetail(String projectId) {
        DeclareEntMiddleFunding entity = getProjectFundingFromDataBase(projectId);
        DeclareEntMiddleFundingDetailDTO dto = new DeclareEntMiddleFundingDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutFunding(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 项目经费使用情况
     *
     * @param projectId 项目ID
     * @return 详情:项目经费使用情况
     */
    private DeclareEntMiddleFundingUsageDetailDTO getFundingUsageDetail(String projectId) {
        DeclareEntMiddleFundingUsage entity = getProjectFundingUsageFromDataBase(projectId);
        DeclareEntMiddleFundingUsageDetailDTO dto = new DeclareEntMiddleFundingUsageDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutFundingUsage(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 自动带出经费到位情况
     *
     * @param dto 经费到位情况
     * @param projectId 项目ID
     */
    private void autoBringOutFunding(DeclareEntMiddleFundingDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        SourceFunds funds = getFundsFromDatabase(declareEnt.getDeclareId(), userId);
        // 单位自有经费
        dto.setOwned(double2BigDecimal(funds.getOwned()));
        // 银行贷款经费
        dto.setLoan(double2BigDecimal(funds.getLoan()));
        // 风险投资经费
        dto.setVc(double2BigDecimal(funds.getVc()));
        // 国创中心资助经费
        dto.setFunding(double2BigDecimal(funds.getFunding()));
        // 其他
        dto.setOther(double2BigDecimal(funds.getOther()));
        // 项目id
        dto.setProjectId(projectId);
    }

    /**
     * 自动带出项目资费使用情况
     *
     * @param dto 项目资费使用情况
     * @param projectId 项目ID
     */
    private void autoBringOutFundingUsage(DeclareEntMiddleFundingUsageDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        SpendingBudget budget = getBudgetFromDatabase(declareEnt.getDeclareId(), userId);

        dto.setEquipmentPurchaseBudget(double2BigDecimal(budget.getEquipmentPurchaseBudget()));
        dto.setEquipmentPurchaseSupport(double2BigDecimal(budget.getEquipmentPurchaseSupport()));
        dto.setEquipmentTrialBudget(double2BigDecimal(budget.getEquipmentTrialBudget()));
        dto.setEquipmentTrialSupport(double2BigDecimal(budget.getEquipmentTrialSupport()));
        dto.setEquipmentModificationBudget(double2BigDecimal(budget.getEquipmentModificationBudget()));
        dto.setEquipmentModificationSupport(double2BigDecimal(budget.getEquipmentModificationSupport()));
        dto.setMaterialFeeBudget(double2BigDecimal(budget.getMaterialFeeBudget()));
        dto.setMaterialFeeSupport(double2BigDecimal(budget.getMaterialFeeSupport()));
        dto.setTravelExpensesBudget(double2BigDecimal(budget.getTravelExpensesBudget()));
        dto.setTravelExpensesSupport(double2BigDecimal(budget.getTravelExpensesSupport()));
        dto.setLaborFeeBudget(double2BigDecimal(budget.getLaborFeeBudget()));
        dto.setLaborFeeSupport(double2BigDecimal(budget.getLaborFeeSupport()));
        dto.setOtherBudget(double2BigDecimal(budget.getOtherBudget()));
        dto.setOtherSupport(double2BigDecimal(budget.getOtherSupport()));
        dto.setPerformancePayBudget(double2BigDecimal(budget.getPerformancePayBudget()));
        dto.setPerformancePaySupport(double2BigDecimal(budget.getPerformancePaySupport()));
        dto.setManagePayBudget(double2BigDecimal(budget.getManagePayBudget()));
        dto.setManagePaySupport(double2BigDecimal(budget.getManagePaySupport()));
        // 项目id
        dto.setProjectId(projectId);
    }

    /**
     * 自动带出项目人员情况
     *
     * @param projectId 项目ID
     * @return 项目人员情况
     */
    private List<DeclareEntMiddleParticipantDetailDTO> autoBringOutParticipant(String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        PersonnelSituation situation = getPersonnelSituationFromDatabase(declareEnt.getDeclareId(), userId);
        String projectParticipantsJson = situation.getProjectParticipants();
        if(JSONUtil.isJson(projectParticipantsJson)) {
            List<DeclareEntMiddleParticipantDetailDTO> results
                    = JSON.parseArray(projectParticipantsJson, DeclareEntMiddleParticipantDetailDTO.class);
            if(CollUtil.isNotEmpty(results)) {
                results.forEach(x -> x.setProjectId(projectId));
            }
            return results;
        }
        return new ArrayList<>();
    }

    /**
     * 自动带出项目完成情况
     *
     * @param dto 项目完成情况
     * @param projectId 项目ID
     */
    private void autoBringOutCompletion(DeclareEntMiddleCompletionDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
//        String userId = SecurityUtils.getUserId();
//        ProjectSchedule schedule = getScheduleFromDatabase(declareEnt.getDeclareId(), userId);
//        // 第1阶段起止时间
//        dto.setFirstStageDate(schedule.getFirstStageDate());
//        // 第1阶段考核指标
//        dto.setFirstSchedule(schedule.getFirstSchedule());
//
//        // 第2阶段起止时间
//        dto.setSecondStageDate(schedule.getSecondStageDate());
//        // 第2阶段考核指标
//        dto.setSecondSchedule(schedule.getSecondSchedule());
//
//        // 第3阶段起止时间
//        dto.setThirdStageDate(schedule.getThirdStageDate());
//        // 第3阶段考核指标
//        dto.setThirdSchedule(schedule.getThirdSchedule());

        // 项目id
        dto.setProjectId(projectId);
    }

    private void autoBringOutSuzhouUnit(DeclareEntMiddleSuzhouUnitDetailDTO dto, String projectId) {
        dto.setProjectId(projectId);
    }

    private void autoBringOutResponsibleUnit(DeclareEntMiddleResponsibleUnitDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        ResponsibleUnit unit = getUnitFromDatabase(declareEnt.getDeclareId(), userId);
        ResponsibleUnitInfo unitInfo = getUnitInfoFromDatabase(declareEnt.getDeclareId(), userId);
        // 单位名称
        dto.setResponsibleName(unit.getResponsibleName());
        // 注册时间
        dto.setRegisterDate(Optional.ofNullable(unitInfo.getRegisterDate())
                .map(date -> date.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate())
                .orElse(null));
        // 注册资本
        dto.setRegisteredCapital(unitInfo.getRegisteredCapital());
        // 法人代表
        dto.setCorporateRepresentative(unitInfo.getCorporateRepresentative());
        // 注册地址
        // 第一次提交的是三级选择, 故不带出
        // dto.setRegisterAddress(unitInfo.getRegisterAddress());
        // 公司人数
        dto.setEmployerNum(unitInfo.getEmployees());
        // 项目id
        dto.setProjectId(projectId);
    }

    private void autoBringOutBasicInfo(DeclareEntMiddleBasicInfoDetailDTO dto, String projectId) {
        // 自动带出属性
        String userId = SecurityUtils.getUserId();
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        Condition condition = getFlowFormConditionFromDataBase(declareEnt.getDeclareId(), userId);
        ResponsibleUnit responsibleUnit = getFlowFormResponsibleUnitFromDataBase(declareEnt.getDeclareId(), userId);
        PersonnelSituation personnelSituation = getPersonnelSituationFromDatabase(declareEnt.getDeclareId(), userId);
        User user = userMapper.selectById(userId);
        // 项目名称
        dto.setSubjectName(condition.getSubjectName());
        // 项目类别
        dto.setDeclareType(declareEnt.getDeclareType());
        // 项目编号
        dto.setProjectNo(declareEnt.getProjectNo());
        // 项目承担单位
        dto.setCompanyName(responsibleUnit.getResponsibleName());
        // 项目负责人
        dto.setPrincipalName(personnelSituation.getPrincipalName());
        // 项目负责人电话
        dto.setPrincipalTel(personnelSituation.getTel());
        // 项目联系人
        dto.setContactName(user.getNickName());
        // 项目联系人电话
        dto.setContactTel(user.getTel());
        // 项目id
        dto.setProjectId(projectId);
    }

    /**
     * 保存或更新基础信息
     *
     * @param dto 基础信息
     */
    private void saveBasicInfo(DeclareEntMiddleBasicInfoSaveDTO dto) {
        DeclareEntMiddleBasicInfo entity = getBasicInfoFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        if(StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus(DeclareCons.MIDDLE_INSPECTION_STATUS.SAVE.getCode());
        }else {
            entity.update(SecurityUtils.getUserId());
        }
        basicInfoMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取基础信息
     *
     * @param projectId 项目ID
     * @return 基础信息
     */
    private DeclareEntMiddleBasicInfo getBasicInfoFromDataBase(String projectId) {
        return Optional.ofNullable(basicInfoMapper.selectOne(Wrappers.<DeclareEntMiddleBasicInfo>lambdaQuery()
                        .eq(DeclareEntMiddleBasicInfo::getProjectId, projectId)))
                .orElseGet(DeclareEntMiddleBasicInfo::new);
    }

    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓流程初始表单获取方法区开始↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

    /**
     * 从数据库获取企业申报
     *
     * @param projectId 项目ID
     * @return 企业申报
     */
    private DeclareEnt getDeclareEntFromDataBase(String projectId) {
        return Optional.ofNullable(declareEntMapper.selectById(projectId))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取条件
     *
     * @param projectId 项目ID
     * @return 条件
     */
    private Condition getFlowFormConditionFromDataBase(String declareId, String userId) {
        return Optional.ofNullable(conditionMapper.selectOne(Wrappers.<Condition>lambdaQuery()
                        .eq(Condition::getProjectId, declareId)
                        .eq(Condition::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取责任单位
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 责任单位
     */
    private ResponsibleUnit getFlowFormResponsibleUnitFromDataBase(String declareId, String userId) {
        // 这个流程表单里存的projectid是父项目的id 即declare project的id而不是delcare ent的id
        return Optional.ofNullable(unitMapper.selectOne(Wrappers.<ResponsibleUnit>lambdaQuery()
                        .eq(ResponsibleUnit::getProjectId, declareId)
                        .eq(ResponsibleUnit::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取参与人
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 参与人
     */
    private PersonnelSituation getPersonnelSituationFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(situationMapper.selectOne(Wrappers.<PersonnelSituation>lambdaQuery()
                        .eq(PersonnelSituation::getProjectId, declareId)
                        .eq(PersonnelSituation::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取单位
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 单位
     */
    private ResponsibleUnit getUnitFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(unitMapper.selectOne(Wrappers.<ResponsibleUnit>lambdaQuery()
                        .eq(ResponsibleUnit::getProjectId, declareId)
                        .eq(ResponsibleUnit::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取单位信息
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 单位信息
     */
    private ResponsibleUnitInfo getUnitInfoFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(unitInfoMapper.selectOne(Wrappers.<ResponsibleUnitInfo>lambdaQuery()
                        .eq(ResponsibleUnitInfo::getProjectId, declareId)
                        .eq(ResponsibleUnitInfo::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取计划
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 计划
     */
    private ProjectSchedule getScheduleFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(scheduleMapper.selectOne(Wrappers.<ProjectSchedule>lambdaQuery()
                        .eq(ProjectSchedule::getProjectId, declareId)
                        .eq(ProjectSchedule::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取资金来源
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 资金来源
     */
    private SourceFunds getFundsFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(fundsMapper.selectOne(Wrappers.<SourceFunds>lambdaQuery()
                        .eq(SourceFunds::getProjectId, declareId)
                        .eq(SourceFunds::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取项目投资估算
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 项目投资估算
     */
    private SpendingBudget getBudgetFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(budgetMapper.selectOne(Wrappers.<SpendingBudget>lambdaQuery()
                        .eq(SpendingBudget::getProjectId, declareId)
                        .eq(SpendingBudget::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("中期检查", "该项目在本系统中不存在"));
    }
    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑流程初始表单获取方法区结束↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

}
