package com.fengyun.udf.uaa.dto.response.inspection.middle;


import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntMiddleAttachmentDetailDTO", description = "DeclareEntMiddleAttachmentDetailDTO")
public class DeclareEntMiddleAttachmentDetailDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 是否企业类项目(前端校验用
     */
    @ApiModelProperty("是否企业类项目")
    private String companyType;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    /**
     * 是否在苏州工业园区注册公司
     */
    @ApiModelProperty("是否在苏州工业园区注册公司")
    private Boolean sipRegister;

    @ApiModelProperty("是否在苏州工业园区租赁实地场地")
    private Boolean sipLease;

    /**
     * 企业营业执照
     */
    @ApiModelProperty("企业营业执照")
    private List<AttachmentDTO> businessLicense;

    /**
     * 年度审计报告
     */
    @ApiModelProperty("年度审计报告")
    private List<AttachmentDTO> annualAuditReport;

    /**
     * 最近一期社保缴费汇总表
     */
    @ApiModelProperty("最近一期社保缴费汇总表")
    private List<AttachmentDTO> latestSocialSecurityPaymentSummary;

    /**
     * 苏州工业园区公司
     */
    @ApiModelProperty("苏州工业园区公司")
    private List<AttachmentDTO> suzhouIndustrialParkCompany;

    /**
     * 租赁合同
     */
    @ApiModelProperty("租赁合同")
    private List<AttachmentDTO> leaseContract;

    /**
     * 证明材料
     */
    @ApiModelProperty("考核指标完成情况证明材料")
    private List<AttachmentDTO> certifyingDocuments;

    /**
     * 重大进展、重大奖项
     */
    @ApiModelProperty("重大进展、重大奖项")
    private List<AttachmentDTO> majorProgressAndAwards;

    /**
     * 专利及论文
     */
    @ApiModelProperty("专利及论文")
    private List<AttachmentDTO> patentsAndPapers;

    /**
     * 各级人才称号
     */
    @ApiModelProperty("各级人才称号")
    private List<AttachmentDTO> talentTitlesAtAllLevels;

    /**
     * 经费的支出明细账或流水
     */
    @ApiModelProperty("经费的支出明细账或流水")
    private List<AttachmentDTO> expenseDetailAccountOrTransaction;

    @ApiModelProperty("盖章版申报书")
    private List<AttachmentDTO> stampedApplicationForm;

    @ApiModelProperty("龙头企业关键核心技术攻关合作证明")
    private List<AttachmentDTO> entKeyTecProof;

    @ApiModelProperty("专利交易或技术转移转化证明材料")
    private List<AttachmentDTO> patentTransProof;

    @ApiModelProperty("项目合同")
    private List<AttachmentDTO> projectContract;

}
