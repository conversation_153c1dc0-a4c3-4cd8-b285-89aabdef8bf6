package com.fengyun.udf.uaa.web.rest.login;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.login.ForgetPasswordDTO;
import com.fengyun.udf.uaa.dto.request.system.EnForgetPasswordDTO;
import com.fengyun.udf.uaa.service.register.RegisterService;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/ua/password")
@Api(value = "忘记密码", tags = "忘记密码")
@Validated
@Slf4j
public class PasswordResource extends BaseResource {
    @Autowired
    private UserService userService;

    @Autowired
    private RegisterService registerService;

    @PostMapping("/forgot/update")
    @AuditLog(method = "/api/ua/password/forgot/update", methodDescription = "忘记密码(手机找回密码)-通用", httpMethod = "POST")
    @ApiOperation(value = "忘记密码(手机找回密码)-通用")
    public ResponseEntity<ReturnResultDTO> restPassword(@RequestBody @Validated ForgetPasswordDTO dto) {
        if (!registerService.checkSmsCaptchaCode(dto.getTel(), dto.getSmsCaptchaCode())) {
            throw new ValidationException("smsCaptchaCode", "短信验证码不正确");
        }

        List<String> userTypes = new ArrayList<>();
        if(StringUtils.isEmpty(dto.getLoginType())){
            userTypes.add(UserType.member.name());
        }else{
            userTypes.add(dto.getLoginType());
        }

        User user = userService.findByTel(dto.getTel(), userTypes);
        if (user == null) {
            throw new ValidationException("tel", "error.userId.notExisting");
        }
        userService.updatePassword(user, dto.getPassword());

        registerService.removeSmsCaptchaCodeFromCache(dto.getTel());

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

//    @PostMapping("/en/forgot")
//    @AuditLog(method = "/api/ua/password/en/forgot", methodDescription = "忘记密码(统一信用代码找回密码)-企业账户", httpMethod = "POST")
//    @ApiOperation(value = "忘记密码(统一信用代码找回密码)-企业账户")
//    public ResponseEntity<ReturnResultDTO> restPassword(@RequestBody @Validated EnForgetPasswordDTO dto) {
//        List<String> userTypes = new ArrayList<>();
//        userTypes.add(UserType.member.name());
//
//        User byLoginName = userService.findByLoginName(dto.getCreditCode(), userTypes);
//        if (byLoginName == null) {
//            throw new ValidationException("error", "用户信息不正确!");
//        }
//        EnRegister enRegister = userService.queryUserEnRegInfo(byLoginName.getId());
//
//        if (enRegister == null) {
//            throw new ValidationException("error", "用户信息不正确");
//        }
//        dto.checkMatch(enRegister);
//        userService.updatePassword(byLoginName, dto.getNewPassword());
//
//        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
//    }

    @PostMapping("/en/forgot")
    @AuditLog(method = "/api/ua/password/en/forgot", methodDescription = "忘记密码(统一信用代码找回密码)-企业账户", httpMethod = "POST")
    @ApiOperation(value = "忘记密码(统一信用代码找回密码)-企业账户")
    public ResponseEntity<ReturnResultDTO> restPassword(@RequestBody @Validated EnForgetPasswordDTO dto) {
        if (!registerService.checkSmsCaptchaCode(dto.getTel(), dto.getSmsCaptchaCode())) {
            throw new ValidationException("smsCaptchaCode", "短信验证码不正确");
        }

        User user = userService.findByCodeTel(dto.getCreditCode(), dto.getTel());
        if (user == null) {
            throw new ValidationException("tel", "error.userId.notExisting");
        }
        userService.updatePassword(user, dto.getNewPassword());

        registerService.removeSmsCaptchaCodeFromCache(dto.getTel());

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

}
