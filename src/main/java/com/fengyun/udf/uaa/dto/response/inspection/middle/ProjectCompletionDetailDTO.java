package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.uaa.dto.request.inspection.middle.DeclareEntMiddleAchievementPaperSaveDTO;
import com.fengyun.udf.uaa.dto.request.inspection.middle.DeclareEntMiddleAchievementPatentSaveDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonUnwrapped;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProjectCompletionDetailDTO", description = "ProjectCompletionDetailDTO")
public class ProjectCompletionDetailDTO extends AbstractDTO {

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "项目完成情况")
    private DeclareEntMiddleCompletionDetailDTO completion;

    @ApiModelProperty(value = "论文发表情况")
    private List<DeclareEntMiddleAchievementPaperDetailDTO> paper;

    @ApiModelProperty(value = "专利申请情况")
    private List<DeclareEntMiddleAchievementPatentDetailDTO> patent;

}
