package com.fengyun.udf.uaa.mapper.expertpanel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertPanel;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertPanelSearchDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertPanelDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ExpertPanelMapper extends com.baomidou.mybatisplus.core.mapper.BaseMapper<ExpertPanel> {

    List<ExpertPanelDTO> page(Page page, @Param("params") ExpertPanelSearchDTO dto);

    List<ExpertPanelDTO> getExpertGroupList();
}
