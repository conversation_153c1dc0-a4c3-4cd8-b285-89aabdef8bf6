package com.fengyun.udf.uaa.web.rest.register;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.system.QueryRegisterUserDto;
import com.fengyun.udf.uaa.dto.response.system.EnRegisterListDto;
import com.fengyun.udf.uaa.dto.response.system.PerRegisterListDto;
import com.fengyun.udf.uaa.service.register.RegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/manage/register")
@Api(value = "用户注册管理", tags = "用户注册管理")
@Validated
@Slf4j
@PreAuthorize("@pms.hasPermission('management')")
public class RegisterManageResource extends BaseResource {

    @Autowired
    private RegisterService registerService;

    @GetMapping("/enRegisterPage")
    @CustomBaseException
    @AuditLog(method = "/api/manage/register/enRegisterPage", methodDescription = "企业用户注册审核列表", httpMethod = "GET")
    @ApiOperation(value = "企业用户注册审核列表")
    public ResponseEntity<ReturnResultDTO<Page<EnRegisterListDto>>> getRegisteredList(QueryRegisterUserDto dto, Page page) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), registerService.getEnRegisterPage(page, dto));
    }

    @GetMapping("/perRegisterPage")
    @CustomBaseException
    @AuditLog(method = "/api/manage/register/perRegisterPage", methodDescription = "个人用户注册列表", httpMethod = "GET")
    @ApiOperation(value = "个人用户注册列表")
    public ResponseEntity<ReturnResultDTO<Page<PerRegisterListDto>>> perRegisterPage(QueryRegisterUserDto dto, Page page) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), registerService.getPerRegisterPage(page, dto));
    }

    @PostMapping("/enRegisterAudit")
    @CustomBaseException
    @AuditLog(method = "/api/manage/register/enRegisterAudit", methodDescription = "企业用户注册审核", httpMethod = "POST")
    @ApiOperation(value = "企业用户注册审核")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "审核id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "处理状态 pass:通过, dead:不通过", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditDesc", value = "审核意见", dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<Page<EnRegisterListDto>>> enRegisterAudit(String status, String id, String auditDesc) throws Exception {
        registerService.processEnRegister(id, status, auditDesc);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/orderState")
    @CustomBaseException
    @AuditLog(method = "/api/manage/register/orderState", methodDescription = "修改用户预约状态", httpMethod = "GET")
    @ApiOperation(value = "修改用户预约状态")
    public ResponseEntity<ReturnResultDTO> editOrderState(String id) {
        registerService.editOrderState(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }


}
