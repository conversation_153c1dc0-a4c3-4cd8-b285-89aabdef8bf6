package com.fengyun.udf.uaa.domain.module;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.time.ZonedDateTime;

import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* <p>
* 内容表
* </p>
*
* <AUTHOR>
* @since 2022-01-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("m_content")
@ApiModel(value = "Content对象", description = "内容表")
public class Content extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "内容名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "内容简述")
    @TableField("introduction")
    private String introduction;

    @ApiModelProperty(value = "所属模块(直属模块)")
    @TableField("owning_module")
    private String owningModule;

    @ApiModelProperty(value = "内容")
    @TableField("content")
    private String content;

    @ApiModelProperty(value = "封面")
    @TableField("cover")
    private String cover;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @TableField("attachments")
    private String attachments;

    @ApiModelProperty(value = "发布时间")
    @TableField("sub_date")
    private ZonedDateTime subDate;
}
