package com.fengyun.udf.uaa.dto.response.instrument;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.interceptor.annotation.Nickname;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InstrumentOrderAuditRecordDTO extends AbstractDTO {

    @ApiModelProperty("审核节点")
    @DictData(target = "nodeStr",codeType = "INSTRUMENT_ORDER_AUDIT_NODE")
    private String node;
    private String nodeStr;

    @ApiModelProperty("审核操作")
    @DictData(target = "statusStr",codeType = "INSTRUMENT_ORDER_AUDIT_STATUS")
    private String status;
    private String statusStr;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    @ApiModelProperty("审核人")
    @Nickname(target = "auditUserName")
    private String auditUser;
    private String auditUserName;

    @ApiModelProperty("审核意见")
    private String remark;
}
