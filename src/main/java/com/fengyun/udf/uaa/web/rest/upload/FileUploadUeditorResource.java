package com.fengyun.udf.uaa.web.rest.upload;

import com.fengyun.udf.resource.BaseResource;
import io.swagger.annotations.Api;
import net.viservice.editor.ueditor.UeditorActionEnter;
import net.viservice.editor.ueditor.UeditorService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ueditor")
@Api(value = "UEditor文件上传", description = "UEditor文件上传")
public class FileUploadUeditorResource extends BaseResource {
    @Resource(name = "UeditorServiceFastdfsImpl")
    private UeditorService ueditoreService;

    @RequestMapping("/execute")
    @ResponseBody
    public ResponseEntity<?> uploadFile(HttpServletRequest request, HttpServletResponse response) {
        String rootPath = "/opt/config/";
        String resultMsg = new UeditorActionEnter(request, rootPath, this.ueditoreService).exec();
        return ResponseEntity.ok().body(resultMsg);
    }
}
