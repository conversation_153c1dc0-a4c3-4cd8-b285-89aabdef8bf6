package com.fengyun.udf.uaa.mapper.flowform;

import com.fengyun.udf.uaa.domain.flowform.Condition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 项目情况介绍 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@Repository
public interface ConditionMapper extends BaseMapper<Condition> {

    boolean checkIsCreated(@Param("projectId") String projectId, @Param("userId") String userId, @Param("tableName") String tableName);
}
