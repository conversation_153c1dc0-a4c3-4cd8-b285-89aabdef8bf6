package com.fengyun.udf.uaa.domain.holiday;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 节假日
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Getter
@Setter
@TableName("t_holiday")
public class Holiday implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 年度
     */
    @TableId("year")
    private String year;

    /**
     * 节假日配置
     */
    @TableField("config")
    private String config;


}
