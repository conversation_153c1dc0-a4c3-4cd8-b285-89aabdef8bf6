package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 仪器预约明细确认
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@TableName("in_instrument_order_detail_confirm")
public class InstrumentOrderDetailConfirm extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 明细确认标题
     */
    @TableField("title")
    private String title;


    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户类型
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 总核准金额
     */
    @TableField("amount")
    private Double amount;

    /**
     * 联系方式
     */
    @TableField("contact_tel")
    private String contactTel;

    /**
     * 联系邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 文件
     */
    @TableField("file")
    private String file;

    /**
     * 是否发送客户
     */
    @TableField("is_send")
    private Boolean isSend;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;

    /**
     * 是否确认
     */
    @TableField("is_confirm")
    private Boolean isConfirm;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private LocalDateTime confirmTime;


}
