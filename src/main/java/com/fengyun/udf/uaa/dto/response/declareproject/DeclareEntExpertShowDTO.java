package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Created by Administrator on 2021/3/11.
 */
@Data
public class DeclareEntExpertShowDTO extends AbstractDTO {

    private String id;

    @ApiModelProperty("申报项目ID")
    private String declareId;

    @ApiModelProperty("申报项目角色ID")
    private String declareEntRoleId;

    @ApiModelProperty("申报项目流程ID")
    private String declareEntId;

    @ApiModelProperty("申报项目名称")
    private String declareName;

    @ApiModelProperty("申报开始时间")
    private String declareBeginTime;

    @ApiModelProperty("申报结束时间")
    private String declareEndTime;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty(value = "课题名称")
    private String subjectName;

    @ApiModelProperty("承担单位")
    private String responsibleName;

    @ApiModelProperty("企业名称")
    private String entpriseName;

    @ApiModelProperty("社会信用代码")
    private String creditCode;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime createdDate;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime updatedDate;

    @ApiModelProperty("创建人")
    private String createdId;

    @ApiModelProperty("审批节点")
    @DictData(target = "nodeStr", codeType = "TDECLARE_NODE")
    private String node;
    private String nodeStr;

    @ApiModelProperty(hidden = true)
    private String status;

    private Boolean returnFlag;

    @ApiModelProperty(hidden = true)
    private String preNode;

    @ApiModelProperty(value = "企业申报状态")
    @DictData(target = "enDeclareStatusValue", codeType = "TDECLARE_STATUS")
    private String enDeclareStatus;
    private String enDeclareStatusValue;

    private String entStatus;

    @ApiModelProperty(value = "当前角色")
    @DictData(target = "currentRoleStr", codeType = "TDECLARE_NODE")
    private String currentRole;
    private String currentRoleStr;

    @ApiModelProperty(value = "提交状态，0-待提交，1-已提交")
    private String submitStatus;

    @ApiModelProperty(value = "上传收据按钮标识，0-不展示，1-展示")
    private String receiptButtonFlag;

    @ApiModelProperty(value = "0-未公示，1-已公示，2-项目立项")
    private String publicFlag;

    @ApiModelProperty(value = "申报类别")
    @DictData(target = "declareTypeStr", codeType = "DECLARE_TYPE_BAK")
    private String declareType;
    private String declareTypeStr;

    @ApiModelProperty(value = "审核结果" )
    @DictData(target = "selectEventStr",codeType = "TDECLARE_EVENT")
    private String selectEvent;
    private String selectEventStr;

    @ApiModelProperty(value = "项目编号" )
    private String projectNo;
    @ApiModelProperty(value = "结辩ppt")
    private String closeDebatePPT;

    @ApiModelProperty(value = "结辩视频")
    private String closeDebateVideo;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> closeDebatePPTs;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> closeDebateVideos;
    private String expertPanelStatus;
    
    @ApiModelProperty(value = "项目状态")
    @DictData(target = "projectStatusStr",codeType = "NEW_PROJECT_STATUS")
    private String projectStatus;
    private String projectStatusStr;

    private String expertPanelName;

    private String member;

    private String memberName;

    private String allMember;

    private String allMemberName;

    private String averageScore;

    private String eval;

    private String totalScore;

}