package com.fengyun.udf.uaa.mapper.declare;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.declare.DeclareEntYearlySummary;
import com.fengyun.udf.uaa.dto.request.declare.QueryDeclareEntYearlySummaryDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryDetailDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Repository
public interface DeclareEntYearlySummaryMapper extends BaseMapper<DeclareEntYearlySummary> {

    /**
     * 分页列表
     *
     * @param page 分页参数
     * @param dto  筛选条件
     * @return 分页列表
     */
    List<DeclareEntYearlySummaryListDTO> page(
            Page<DeclareEntYearlySummaryListDTO> page,
            @Param("condition") QueryDeclareEntYearlySummaryDTO dto);

    /**
     * 详情
     * @param projectId 项目ID
     * @return 详情
     */
    DeclareEntYearlySummaryDetailDTO detail(@Param("projectId") String projectId);

}
