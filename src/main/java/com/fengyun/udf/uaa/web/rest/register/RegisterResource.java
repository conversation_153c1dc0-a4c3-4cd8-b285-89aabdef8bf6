package com.fengyun.udf.uaa.web.rest.register;


import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.cache.redis.service.RedisClient;
import com.fengyun.udf.captcha.CaptchaGenerator;
import com.fengyun.udf.constant.Regex;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.RedisObjectDTO;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.register.EnRegisterDto;
import com.fengyun.udf.uaa.dto.request.register.RestPasswordDTO;
import com.fengyun.udf.uaa.dto.request.register.SignInDTO;
import com.fengyun.udf.uaa.service.register.RegisterService;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version version 1.0
 */
@RestController
@RequestMapping("/api/register")
@Api(value = "用户注册", tags = "用户注册")
@Validated
@Slf4j
public class RegisterResource extends BaseResource {
    @Autowired
    private UserService userService;

    @Autowired
    private RegisterService registerService;

    @Autowired
    private RedisClient redisClient;

    @GetMapping("/sms-captcha")
    @AuditLog(method = "/api/register/sms-captcha", methodDescription = "发送短信验证码", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tel", value = "手机号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "短信类型（register：注册，login：登录，restPassword：重置密码）", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "发送短信验证码")
    public ResponseEntity<ReturnResultDTO> sendSmsCaptchaCode(@Pattern(regexp = Regex.DEFAULT_PHONE_NUM_REGEX, message = "{error.tel.format}")
                                                                  @NotBlank(message = "{error.tel.blank}") String tel,
                                                              @NotBlank(message = "短信类型不能为空") String type,
                                                              @NotBlank(message = "图形验证码不能为空") String captchaCode,
                                                              @NotBlank(message = "图形验证码不id") String id) throws Exception {
        //验证 图形验证码
        registerService.graphCaptchaCodeVerification(id, captchaCode);

        registerService.sendSmsCaptchaCode(tel, type);

        //删除 图形验证码
        registerService.graphCaptchaCodeDelete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/captcha/graph/{id}")
    @AuditLog(method = "/api/register/captcha/graph/{id}", methodDescription = "图形验证码生成", httpMethod = "GET")
    @ApiOperation(value = "图形验证码生成", httpMethod = "GET", response = ReturnResultDTO.class, notes = "图形验证码生成, 需要uuid")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO> generateCaptcha(@ApiParam(value = "验证码编号", required = true) @PathVariable("id") String id) throws Exception{
        //生成图形验证码
        String captchaCode = CaptchaGenerator.generateCaptchaCode(4);
        log.info("图形验证码为："+captchaCode);
        //Base64编码
        String captchaCode_base64 = CaptchaGenerator.outputImageBase64(300, 80, captchaCode);
        //保存图形码
        RedisObjectDTO redisObjectDTO = new RedisObjectDTO("", "captcha", id);
        redisObjectDTO.setValue(captchaCode);
        redisObjectDTO.setTimeout(86400L);
        redisClient.setObject(redisObjectDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), captchaCode_base64);
    }

    @GetMapping("/captcha/graph/{id}/verification")
    @AuditLog(method = "/api/register/captcha/graph/{id}/verification", methodDescription = "图形验证码核实", httpMethod = "GET")
    @ApiOperation(value = "图形验证码核实", httpMethod = "GET", response = ReturnResultDTO.class, notes = "检查输入的验证码是否正确, 需要验证码和uuid")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "验证成功")})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "验证码编号", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "captchaCode", value = "图形验证码", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<?> verificateCaptcha(@PathVariable("id") String id,
                                               @NotBlank(message = "图形验证码不能为空") String captchaCode) throws Exception{
        registerService.graphCaptchaCodeVerification(id, captchaCode);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("")
    @AuditLog(method = "/api/register", methodDescription = "注册", httpMethod = "POST")
    @ApiOperation(value = "注册")
    public ResponseEntity<ReturnResultDTO> signInUser(HttpServletRequest request
            , @RequestBody @Validated SignInDTO signInDTO) {
        signInDTO.setSignupChannel(registerService.getUserSignUpChannel(request));
        signInDTO.setUserType(2);
        String[] ids = registerService.signInUser(signInDTO);

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/enterprise")
    @AuditLog(method = "/api/register/enterprise", methodDescription = "企业注册", httpMethod = "POST")
    @ApiOperation(value = "注册")
    public ResponseEntity<ReturnResultDTO> signInUserByEnterprise(HttpServletRequest request
            , @RequestBody @Validated EnRegisterDto signInDTO) {
        signInDTO.setSignupChannel(registerService.getUserSignUpChannel(request));
        signInDTO.setUserType(1);
        String[] ids = registerService.signInUserByEnterprise(signInDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/password/update")
    @AuditLog(method = "/api/register/password/update", methodDescription = "忘记密码(手机找回密码)", httpMethod = "POST")
    @ApiOperation(value = "忘记密码(手机找回密码)")
    public ResponseEntity<ReturnResultDTO> restPassword(@RequestBody @Validated RestPasswordDTO restPasswordDTO) {
        if (!registerService.checkSmsCaptchaCode(restPasswordDTO.getTel(), restPasswordDTO.getSmsCaptchaCode())) {
            throw new ValidationException("smsCaptchaCode", "短信验证码不正确");
        }

        User user = userService.findByTel(restPasswordDTO.getTel(), prepareUserType());
        if (user == null) {
            throw new ValidationException("tel", "error.userId.notExisting");
        }
        userService.updatePassword(user, restPasswordDTO.getPassword());

        registerService.removeSmsCaptchaCodeFromCache(restPasswordDTO.getTel());
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    private List<String> prepareUserType(){
        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());
        return userTypes;
    }
}
