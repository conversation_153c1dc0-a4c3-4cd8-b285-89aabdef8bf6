package com.fengyun.udf.uaa.config;

import com.fengyun.udf.constant.CommonConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * Swagger2的接口配置
 * Created by wangg on 2018/7/2.
 */
@Configuration
@EnableSwagger2
@Profile(CommonConstants.SPRING_PROFILE_SWAGGER)
public class SwaggerConfiguration {

    /**
     * 创建API
     */
    @Bean
    public Docket createRestApi() {
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.fengyun.udf"))
                // 扫描所有 .apis(RequestHandlerSelectors.any())
                .paths(PathSelectors.any())
                .build();
        setHeader(docket);
        return docket;
    }

    /**
     * 添加摘要信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("接口文档")
                .description("管理系统")
                .contact(new Contact("mofang", null, null))
                .version("版本：1.0")
                .build();
    }

    private void setHeader(Docket docket) {
        //header全局参数设置
        List<Parameter> parameterBuilders = new ArrayList<>();
        final Parameter authorizationParameter = new ParameterBuilder()
                .name("Authorization")
                .description("token，登录后获取")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .defaultValue("Bearer ")
                .build();
        parameterBuilders.add(authorizationParameter);

        docket.globalOperationParameters(parameterBuilders);
    }
}
