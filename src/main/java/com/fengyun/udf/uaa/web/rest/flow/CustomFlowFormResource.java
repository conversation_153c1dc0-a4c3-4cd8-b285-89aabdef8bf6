package com.fengyun.udf.uaa.web.rest.flow;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.request.flowform.*;
import com.fengyun.udf.uaa.dto.response.flowform.*;
import com.fengyun.udf.uaa.service.flowform.FlowFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(value = "项目申报-企业", tags = "项目申报-企业")
@RestController
@RequestMapping({"/api/flowForm"})
@Validated
public class CustomFlowFormResource extends BaseResource {

    @Autowired
    private FlowFormService formService;


    @PostMapping("/condition/save")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/condition/save", methodDescription = "保存项目详情介绍", httpMethod = "POST")
    @ApiOperation(value = "保存项目详情介绍")
    public ResponseEntity<ReturnResultDTO> condition(@RequestBody CreateConditionDto dto) {
        formService.saveCondition(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/condition/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/condition/detail", methodDescription = "项目详情介绍详情", httpMethod = "GET")
    @ApiOperation(value = "项目详情介绍详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ConditionDetailDto>> conditionDetail(String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.conditionDetail(projectId, SecurityUtils.getUserId()));
    }

    @PostMapping("/responsible/save")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/responsible/save", methodDescription = "保存承担单位情况", httpMethod = "POST")
    @ApiOperation(value = "保存承担单位情况")
    public ResponseEntity<ReturnResultDTO> responsible(@RequestBody CreateResponsibleDto dto) {
        formService.saveResponsible(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/responsible/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/responsible/detail", methodDescription = "承担单位情况详情", httpMethod = "GET")
    @ApiOperation(value = "承担单位情况详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ResponsibleDetailDto>> responsibleDetail(String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.responsibleDetail(projectId, SecurityUtils.getUserId()));
    }

    @PostMapping("/person/save")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/person/save", methodDescription = "保存人员情况", httpMethod = "POST")
    @ApiOperation(value = "保存人员情况")
    public ResponseEntity<ReturnResultDTO> person(@RequestBody CreatedSituationDto dto) {
        formService.saveSituation(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/person/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/person/detail", methodDescription = "人员情况详情", httpMethod = "GET")
    @ApiOperation(value = "人员情况详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<SituationDetailDto>> personDetail(String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.situationDetail(projectId, SecurityUtils.getUserId()));
    }

    @PostMapping("/schedule/save")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/schedule/save", methodDescription = "保存项目执行计划", httpMethod = "POST")
    @ApiOperation(value = "保存项目执行计划")
    public ResponseEntity<ReturnResultDTO> schedule(@RequestBody CreateScheduleDto dto) {
        formService.saveSchedule(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/schedule/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/schedule/detail", methodDescription = "项目执行计划详情", httpMethod = "GET")
    @ApiOperation(value = "项目执行计划详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ScheduleDetailDto>> scheduleDetail(String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.scheduleDetail(projectId, SecurityUtils.getUserId()));
    }

    @PostMapping("/funds/save")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/funds/save", methodDescription = "保存资金预算支出", httpMethod = "POST")
    @ApiOperation(value = "保存资金预算支出")
    public ResponseEntity<ReturnResultDTO> funds(@RequestBody CreatedFundsDto dto) {
        formService.saveFund(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/funds/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/funds/detail", methodDescription = "资金预算支出详情", httpMethod = "GET")
    @ApiOperation(value = "资金预算支出详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<FundDetailDto>> fundsDetail(String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.fundDetail(projectId, SecurityUtils.getUserId()));
    }

    @PostMapping("/attachment/save")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/attachment/save", methodDescription = "保存附件", httpMethod = "POST")
    @ApiOperation(value = "保存附件")
    public ResponseEntity<ReturnResultDTO> attachment(@RequestBody CreatedAttachmentDto dto) {
        formService.saveAttachment(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/attachment/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/attachment/detail", methodDescription = "附件详情", httpMethod = "GET")
    @ApiOperation(value = "附件详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<AttachmentDetailDto>> attachmentDetail(String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.attachmentDetail(projectId, SecurityUtils.getUserId()));
    }

    @PostMapping("/upload/debates")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/upload/debates", methodDescription = "上传答辩ppt和视频", httpMethod = "POST")
    @ApiOperation(value = "附件详情")
    public ResponseEntity<ReturnResultDTO<AttachmentDetailDto>> attachmentDetail(@RequestBody CreatedDebateDto dto) {
        formService.saveDebatesPPtAndVideo(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

}
