package com.fengyun.udf.uaa.dto.response.instrument;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderConsumableDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class InstrumentOrderWorkDetailDTO extends AbstractDTO {

    @ApiModelProperty("预约id")
    private Integer id;

    @ApiModelProperty("租赁类型")
    @DictData(target = "leaseTypeStr", codeType = "INSTRUMENT_ORDER_LEASE_TYPE")
    private String leaseType;
    private String leaseTypeStr;

    @ApiModelProperty("仪器租赁单价")
    private Double leasePrice;

    @ApiModelProperty("仪器租赁价格单位")
    private String leasePriceUnit;

    @ApiModelProperty("仪器租赁最低收费")
    private Double leasePriceMin;

    @ApiModelProperty("委托测试单价")
    private Double testPrice;

    @ApiModelProperty("委托测试价格单位")
    private String testPriceUnit;

    @ApiModelProperty("计算方式，INSTRUMENT_CALCULATE_METHOD")
    @DictData(target = "calculateMethodStr",codeType = "INSTRUMENT_CALCULATE_METHOD")
    private String calculateMethod;
    private String calculateMethodStr;

    @ApiModelProperty("耗材金额")
    private Double consumableAmount;

    @ApiModelProperty("核准金额")
    private Double amount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态,暂存:draft,提交:commit,最终暂存:final_draft,最终提交:final_commit")
    private String status;

    @ApiModelProperty("传已盖章工单pdf")
    private String workStampPdf;

    @ApiModelProperty("生成工单pdf")
    private String workPdf;

    @ApiModelProperty("工单发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    @ApiModelProperty("预计使用样品数（个）")
    private String useSampleNum;

    @ApiModelProperty("预约使用耗材")
    private List<InstrumentOrderConsumableDTO> orderConsumableList;

    @ApiModelProperty("设备耗材")
    private List<ConsumableListDTO> consumableList;

    @ApiModelProperty("预约时间")
    private List<InstrumentOrderDateDTO> orderDateDTOList;

    @ApiModelProperty("预约时间")
    private String orderDateStr;
}
