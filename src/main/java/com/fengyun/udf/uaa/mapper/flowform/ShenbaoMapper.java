package com.fengyun.udf.uaa.mapper.flowform;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.flowform.Shenbao;
import com.fengyun.udf.uaa.dto.request.flowform.QueryShenbaoDto;
import com.fengyun.udf.uaa.dto.response.flowform.ShenbaoDetailDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Repository
public interface ShenbaoMapper extends BaseMapper<Shenbao> {

    ShenbaoDetailDto selectByIdDetail(@Param("id") String id);

    List<ShenbaoDetailDto> selectPageList(@Param("page") Page page, @Param("dto") QueryShenbaoDto dto);
}
