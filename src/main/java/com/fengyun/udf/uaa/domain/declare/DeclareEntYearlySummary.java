package com.fengyun.udf.uaa.domain.declare;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "declare_ent_yearly_summary", autoResultMap = true)
@ApiModel(value = "DeclareEntYearlySummary对象", description = "")
public class DeclareEntYearlySummary extends BaseDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty("总结提交时间")
    private LocalDateTime submitAt;

    @ApiModelProperty("附件")
    @TableField(value = "attachment", typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> attachment;
}
