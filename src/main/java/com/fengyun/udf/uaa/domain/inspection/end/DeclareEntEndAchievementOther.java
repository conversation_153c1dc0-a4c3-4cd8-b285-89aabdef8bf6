package com.fengyun.udf.uaa.domain.inspection.end;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "declare_ent_end_achievement_other")
@TableName(value = "declare_ent_end_achievement_other", autoResultMap = true)
public class DeclareEntEndAchievementOther extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @TableField("project_id")
    private String projectId;

    /**
     * 获得时间
     */
    @ApiModelProperty("获得时间")
    @TableField("acquire_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", locale = "zh")
    private LocalDate acquireDate;

    /**
     * 成果概述
     */
    @ApiModelProperty("成果概述")
    @TableField("description")
    private String description;
}
