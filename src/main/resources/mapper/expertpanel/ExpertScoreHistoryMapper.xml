<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.expertpanel.ExpertScoreHistoryMapper">

    <select id="selectScoreDetailList" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreDTO">
        select a.*,b.NICK_NAME as userName from t_expert_score_history a
        left join t_user b on a.created_id=b.ID
        where declare_ent_id=#{declareEntId} and a.status='1' and a.score_index ='1' order by created_date desc
    </select>
    <select id="selectSecondScoreDetailList" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreDTO">
        select a.*,b.NICK_NAME as userName from t_expert_score_history a
        left join t_user b on a.created_id=b.ID
        where declare_ent_id=#{declareEntId} and a.status='1' and a.score_index ='2' order by created_date desc
    </select>
</mapper>