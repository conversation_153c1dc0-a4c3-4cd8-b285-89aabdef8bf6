package com.fengyun.udf.uaa.dto.response;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;


@Data
public class AuditDTO extends AbstractDTO {

    @DictData(target = "statusValue", codeType = "CONTENT_AUDIT_STATUS")
    private String status;
    private String statusValue;

    private String auditOpinion;

    private List<AttachmentDTO> attachments;

    private ZonedDateTime createdDate;
}
