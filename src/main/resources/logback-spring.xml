<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <property name="APP_NAME" value="uaa"></property>
    <property name="LOG_HOME" value="/var/log"></property>

    <appender name="FILE_AUDIT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}/api-log/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!--<maxHistory>60</maxHistory>-->
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="BEHAVIOR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}/behavior-log/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!--<maxHistory>60</maxHistory>-->
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="FILE_SYS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}/${APP_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger - %msg%n</Pattern>
        </encoder>
    </appender>

    <logger name="com.fengyun.udf.aspect.AuditLogAspect" level="#logback.loglevel#">
        <appender-ref ref="FILE_AUDIT"/>
    </logger>

    <logger name="ws-log" level="#logback.loglevel#">
        <appender-ref ref="BEHAVIOR_FILE"/>
    </logger>

    <logger name="com.fengyun.udf" level="#logback.loglevel#"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="springfox" level="WARN"/>
    <logger name="javax.xml.bind" level="WARN"/>
    <logger name="javax.xml.bind" level="WARN"/>
    <logger name="c.n.d.shared" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="com.netflix.discovery" level="WARN"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <root level="#logback.loglevel#">
        <appender-ref ref="FILE_SYS"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
