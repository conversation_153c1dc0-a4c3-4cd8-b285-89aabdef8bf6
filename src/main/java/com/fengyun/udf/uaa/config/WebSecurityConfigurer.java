package com.fengyun.udf.uaa.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fengyun.udf.uaa.config.security.handler.WebAuthenticationFailureHandler;
import com.fengyun.udf.uaa.config.security.handler.WebAuthenticationSuccessHandler;
import com.fengyun.udf.uaa.config.security.provider.PhoneAuthenticationProvider;
import com.fengyun.udf.uaa.config.security.provider.ThirdAuthenticationProvider;
import com.fengyun.udf.uaa.config.security.provider.WebAuthenticationProvider;
import com.fengyun.udf.uaa.config.security.service.PhoneUserDetailsService;
import com.fengyun.udf.uaa.config.security.service.ThirdUserDetailsService;
import com.fengyun.udf.uaa.config.security.service.WebUserDetailsService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

/**
 * Created by wangg on 2019/7/4.
 */
@Configuration
@Order(100)
public class WebSecurityConfigurer extends WebSecurityConfigurerAdapter {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ClientDetailsService clientDetailsService;
    @Lazy
    @Autowired
    private AuthorizationServerTokenServices defaultAuthorizationServerTokenServices;

    @Autowired
    private PhoneUserDetailsService phoneUserDetailService;

    @Autowired
    private WebUserDetailsService webUserDetailsService;

    @Autowired
    private ThirdUserDetailsService thirdUserDetailsService;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .authorizeRequests()
                .antMatchers("/actuator/**").permitAll()
                .anyRequest().authenticated()
                .and().csrf().disable();
    }

    @Override
    public void configure(AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(phoneAuthenticationProvider());
        auth.authenticationProvider(webAuthenticationProvider());
        auth.authenticationProvider(thirdAuthenticationProvider());
    }

    @Bean
    @Override
    @SneakyThrows
    public AuthenticationManager authenticationManagerBean() {
        return super.authenticationManagerBean();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 登录成功后处理逻辑
     *
     * @return
     */
    @Bean
    public WebAuthenticationSuccessHandler udfAuthenticationSuccessHandler() {
        return WebAuthenticationSuccessHandler.builder()
                .objectMapper(objectMapper)
                .clientDetailsService(clientDetailsService)
                .passwordEncoder(passwordEncoder())
                .authorizationServerTokenServices(defaultAuthorizationServerTokenServices).build();
    }

    /**
     * 登录失败处理逻辑
     *
     * @return
     */
    @Bean
    public WebAuthenticationFailureHandler udfAuthenticationFailureHandler() {
        return WebAuthenticationFailureHandler.builder().objectMapper(objectMapper).build();
    }

    /**
     * 普通登录认证
     *
     * @return
     */
    @Bean
    public WebAuthenticationProvider webAuthenticationProvider() {
        WebAuthenticationProvider provider = new WebAuthenticationProvider();
        provider.setUserDetailsService(webUserDetailsService);
        provider.setHideUserNotFoundExceptions(false);
        provider.setPasswordEncoder(passwordEncoder());
        return provider;
    }

    /**
     * 手机验证码登录认证
     *
     * @return
     */
    @Bean
    public PhoneAuthenticationProvider phoneAuthenticationProvider() {
        PhoneAuthenticationProvider provider = new PhoneAuthenticationProvider();
        // 设置userDetailsService
        provider.setUserDetailsService(phoneUserDetailService);
        // 禁止隐藏用户未找到异常
        provider.setHideUserNotFoundExceptions(false);
        return provider;
    }

    /**
     * 第三方登录认证
     *
     * @return
     */
    @Bean
    public ThirdAuthenticationProvider thirdAuthenticationProvider() {
        ThirdAuthenticationProvider provider = new ThirdAuthenticationProvider();
        // 设置userDetailsService
        provider.setUserDetailsService(thirdUserDetailsService);
        // 禁止隐藏用户未找到异常
        provider.setHideUserNotFoundExceptions(false);
        return provider;
    }

}
