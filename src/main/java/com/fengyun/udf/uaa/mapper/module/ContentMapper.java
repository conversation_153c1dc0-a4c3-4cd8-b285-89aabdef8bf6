package com.fengyun.udf.uaa.mapper.module;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.module.Content;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.response.module.ContentListDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 内容表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Repository
public interface ContentMapper extends BaseMapper<Content> {

    List<ContentListDto> selectContentByOwningIds(Page page, @Param("keyWord") String keyWord, @Param("ids") List<String> ids);

}
