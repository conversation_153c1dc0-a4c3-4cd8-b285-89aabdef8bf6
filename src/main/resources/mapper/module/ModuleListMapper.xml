<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.module.ModuleListMapper">

    <select id="selectListTree" resultType="com.fengyun.udf.uaa.dto.response.module.ModuleListTreeDto">
        SELECT
            l.id,
            l.parent_id parentId,
            l.module_name moduleName,
            l.module_type moduleType,
            l.module_url moduleUrl,
            l.sort,
            l.is_show_in_nav isShowInNav
        FROM
            m_module_list l
        WHERE 1 = 1
        <if test="isShowInNav != null and isShowInNav!=''">
            AND l.is_show_in_nav = #{isShowInNav}
        </if>
        ORDER BY
            l.sort
    </select>
    <select id="selectListShow" resultType="com.fengyun.udf.uaa.dto.response.module.ModuleListShowDto">
        select
            l.module_name moduleName,
            l.id
        from
            m_module_list l
        WHERE
            l.parent_id = #{condition}
        ORDER BY
            l.sort
    </select>
</mapper>
