package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 资金来源
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_source_funds")
@Api(hidden = true)
public class SourceFunds extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "支出id")
    @TableField("budget_id")
    private String budgetId;

    @ApiModelProperty(value = "项目总投资")
    @TableField("total")
    private Double total;

    @ApiModelProperty(value = "来源说明")
    @TableField("total_description")
    private String totalDescription;

    @ApiModelProperty(value = "已投入资金")
    @TableField("invested")
    private Double invested;

    @ApiModelProperty(value = "来源说明")
    @TableField("invested_description")
    private String investedDescription;

    @ApiModelProperty(value = "单位自有投资")
    @TableField("owned")
    private Double owned;

    @ApiModelProperty(value = "来源说明")
    @TableField("owned_description")
    private String ownedDescription;

    @ApiModelProperty(value = "银行贷款")
    @TableField("loan")
    private Double loan;

    @ApiModelProperty(value = "来源说明")
    @TableField("loan_description")
    private String loanDescription;

    @ApiModelProperty(value = "风险投资")
    @TableField("vc")
    private Double vc;

    @ApiModelProperty(value = "来源说明")
    @TableField("vc_description")
    private String vcDescription;

    @ApiModelProperty(value = "自筹比例")
    @TableField("self_raised_proportion")
    private String selfRaisedProportion;

    @ApiModelProperty(value = "资助资金")
    @TableField("funding")
    private Double funding;

    @ApiModelProperty(value = "来源描述")
    @TableField("funding_description")
    private String fundingDescription;

    @ApiModelProperty(value = "资助比例")
    @TableField("funding_proportion")
    private String fundingProportion;

    @ApiModelProperty(value = "其他资金")
    @TableField("other")
    private Double other;

    @ApiModelProperty(value = "来源描述")
    @TableField("other_description")
    private String otherDescription;

    @ApiModelProperty(value = "其他比例")
    @TableField("other_proportion")
    private String otherProportion;

    @ApiModelProperty(value = "合计")
    @TableField("all_funds")
    private Double allFunds;

    @ApiModelProperty(value = "来源说明")
    @TableField("all_description")
    private String allDescription;

}
