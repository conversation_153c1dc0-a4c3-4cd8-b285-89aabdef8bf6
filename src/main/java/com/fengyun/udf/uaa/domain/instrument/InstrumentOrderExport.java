package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Getter
@Setter
@TableName("in_instrument_order_export")
public class InstrumentOrderExport extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户类型
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 预约时间开始
     */
    @TableField("order_start_date")
    private LocalDate orderStartDate;

    /**
     * 预约时间结束
     */
    @TableField("order_end_date")
    private LocalDate orderEndDate;

    /**
     * 导出字段
     */
    @TableField("export_field")
    private String exportField;

    /**
     * 核准金额
     */
    @TableField("amount")
    private Double amount;

    /**
     * 是否发送客户
     */
    @TableField("is_send")
    private Boolean isSend;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;

    /**
     * 明细文件
     */
    @TableField("excel_file")
    private String excelFile;

    /**
     * 联系方式
     */
    @TableField("contact_tel")
    private String contactTel;

    /**
     * 联系邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 是否确认
     */
    @TableField("is_confirm")
    private Boolean isConfirm;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private LocalDateTime confirmTime;


}
