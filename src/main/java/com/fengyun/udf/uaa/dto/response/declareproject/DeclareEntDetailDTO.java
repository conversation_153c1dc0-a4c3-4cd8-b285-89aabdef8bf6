package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.DeclareEntInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Created by Administrator on 2021/3/11.
 */
@Data
public class DeclareEntDetailDTO extends AbstractDTO {

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("申报项目ID")
    private String declareId;

    @ApiModelProperty("申报项目ID")
    private String declareEntId;

    @ApiModelProperty("申报项目名称")
    private String declareName;

    @ApiModelProperty("企业信息")
    private DeclareEntInfoDTO declareEntInfoDTO;

    @ApiModelProperty("序号")
    private String num;

    @ApiModelProperty("申报材料的名称")
    private String declareChildrenName;

    @ApiModelProperty("审批节点")
    @DictData(target = "nodeStr",codeType = "TDECLARE_NODE")
    private String node;
    private String nodeStr;

    @ApiModelProperty(hidden = true)
    private ZonedDateTime createdDate;
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd")
    private String declareCreateTime;

    @ApiModelProperty("是否可申报")
    private Boolean isDeclare;

    @ApiModelProperty(value = "结辩ppt")
    private String closeDebatePPT;

    @ApiModelProperty(value = "结辩视频")
    private String closeDebateVideo;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> closeDebatePPTs;

    @ApiModelProperty(value = "评审模板")
    private List<AttachmentDTO> closeDebateVideos;

    @ApiModelProperty(value = "项目进度")
    @DictData(target = "projectProgressStr", codeType = "PROJECT_PROGRESS")
    private String projectProgress;
    private String projectProgressStr;
}