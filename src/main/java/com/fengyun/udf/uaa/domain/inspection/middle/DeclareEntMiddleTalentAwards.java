package com.fengyun.udf.uaa.domain.inspection.middle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "人才奖项")
@TableName(value = "declare_ent_middle_talent_awards", autoResultMap = true)
public class DeclareEntMiddleTalentAwards extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @TableField("project_id")
    private String projectId;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    @TableField("applicant_name")
    private String applicantName;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @TableField("`year`")
    private String year;

    /**
     * 申报单位
     */
    @ApiModelProperty("申报单位")
    @TableField("declare_unit")
    private String declareUnit;

    /**
     * 人才称号
     */
    @ApiModelProperty("人才称号")
    @TableField("talentTitle")
    private String talentTitle;

    /**
     * 是否获评
     */
    @ApiModelProperty("是否获评")
    @TableField("evaluated")
    private Boolean evaluated;

    @ApiModelProperty("排序")
    @TableField("sort_num")
    private Integer sortNum;

}
