package com.fengyun.udf.uaa.web.rest.instrument;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmListDTO;
import com.fengyun.udf.uaa.service.instrument.InstrumentOrderDetailConfirmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */

@RestController
@RequestMapping("/api/customer/instrument-order-detail-confirm")
@Api(value = "企业端-仪器预约明细确认", tags = "企业端-仪器预约明细确认")
@Validated
@Slf4j
@AllArgsConstructor
public class CustomerInstrumentOrderDetailConfirmResource extends BaseResource {
    private final InstrumentOrderDetailConfirmService instrumentOrderDetailConfirmService;
    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order-detail-confirm/view", methodDescription = "仪器预约明细确认详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约明细确认ID", required = true, dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "仪器预约明细确认详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderDetailConfirmDetailDTO>> detail(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderDetailConfirmService.detail(id));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order-detail-confirm", methodDescription = "仪器预约明细确认列表", httpMethod = "GET")
    @ApiOperation(value = "仪器预约明细确认列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderDetailConfirmListDTO>>> page(Page page, InstrumentOrderDetailConfirmQueryDTO dto) {
        dto.setUserId(SecurityUtils.getUserId());
        dto.setIsSend(true);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderDetailConfirmService.page(page, dto));
    }

    @GetMapping("/confirm")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm/confirm", methodDescription = "仪器预约明细确认", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约明细确认ID", required = true, dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "仪器预约明细确认")
    public ResponseEntity<ReturnResultDTO> confirm(@NotNull(message = "id不能为空") Integer id) {
        instrumentOrderDetailConfirmService.confirm(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }


}
