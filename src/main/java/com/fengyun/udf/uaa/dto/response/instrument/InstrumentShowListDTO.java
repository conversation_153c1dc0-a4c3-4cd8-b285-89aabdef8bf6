package com.fengyun.udf.uaa.dto.response.instrument;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2023-08-29
 */
@Getter
@Setter
public class InstrumentShowListDTO extends AbstractDTO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备编号")
    private String num;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("厂家")
    private String factory;

    @ApiModelProperty("放置地点")
    private String address;

    @ApiModelProperty("状态")
    @DictData(target = "statusStr", codeType = "INSTRUMENT_STATUS")
    private String status;
    private String statusStr;

    @ApiModelProperty("每时间段人数")
    private Integer personNum;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("区域，INSTRUMENT_AREA")
    @DictData(target = "instrumentAreaStr",codeType = "INSTRUMENT_AREA")
    private String instrumentArea;
    private String instrumentAreaStr;

    @ApiModelProperty("预约时间")
    private List<InstrumentOrderTimeDTO> times;

}