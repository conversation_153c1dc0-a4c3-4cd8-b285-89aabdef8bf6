package com.fengyun.udf.uaa.dto.response.instrument;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
public class InstrumentOrderExportDTO extends AbstractDTO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("仪器id")
    private String instrumentId;

    @ApiModelProperty("设备名称")
    private String instrumentName;

    @ApiModelProperty("用户类型")
    @DictData(target = "userTypeStr", codeType = "USER_TYPE")
    private Integer userType;
    private String userTypeStr;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("租赁类型")
    @DictData(target = "leaseTypeStr", codeType = "INSTRUMENT_ORDER_LEASE_TYPE")
    private String leaseType;
    private String leaseTypeStr;

    @ApiModelProperty("预约日期")
    private LocalDate orderDate;

    @ApiModelProperty("预约时间")
    @DictData(target = "orderTimeStr", codeType = "INSTRUMENT_ORDER_TIME")
    private String orderTime;
    private String orderTimeStr;

    private String exportOrderTimeStr;

    @ApiModelProperty("企业名称")
    private String enName;

    @ApiModelProperty("预约联系人")
    private String contact;

    @ApiModelProperty("预约联系方式")
    private String contactTel;

    @ApiModelProperty("预约联系邮箱")
    private String contactEmail;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("是否熟练使用仪器")
    private Boolean skilledUse;
    private String skilledUseStr;

    @ApiModelProperty("是否已签框架协议")
    private Boolean signAgreement;
    private String signAgreementStr;

    @ApiModelProperty("预计使用样品数（个）")
    private Integer useSampleNum;

    @ApiModelProperty("样品名称")
    private String sampleName;

    @ApiModelProperty("样品包装")
    private String samplePackage;

    @ApiModelProperty("样品规格")
    private String sampleSpec;

    @ApiModelProperty("样品存放要求,INSTRUMENT_ORDER_SAMPLE_DEPOSIT")
    @DictData(target = "sampleDepositStr", codeType = "INSTRUMENT_ORDER_SAMPLE_DEPOSIT")
    private String sampleDeposit;
    private String sampleDepositStr;

    @ApiModelProperty("样品特性,INSTRUMENT_ORDER_SAMPLE_FEATURE")
    @DictData(target = "sampleFeatureStr", codeType = "INSTRUMENT_ORDER_SAMPLE_FEATURE")
    private String sampleFeature;
    private String sampleFeatureStr;

    @ApiModelProperty("检测项目")
    private String testProject;

    @ApiModelProperty("检测条件")
    private String testCondition;

    @ApiModelProperty("检测方法")
    private String testMethod;

    @ApiModelProperty("实验内容")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    @DictData(target = "statusStr", codeType = "INSTRUMENT_ORDER_STATUS")
    private String status;
    private String statusStr;


    @ApiModelProperty("结算状态")
    @DictData(target = "settlementStatusStr", codeType = "INSTRUMENT_ORDER_SETTLEMENT_STATUS")
    private String settlementStatus;
    private String settlementStatusStr;

    @ApiModelProperty("创建时间")
    private String createdDate;

    private Boolean leaseFace;
    private Double leasePrice;
    private String leasePriceStr;
    private Boolean testFace;
    private Double testPrice;
    private String testPriceStr;

    @ApiModelProperty("核准金额")
    private Double amount;

    private String workStatus;

    @ApiModelProperty("耗材名称")
    private String consumableName;
    @ApiModelProperty("所需数量（个）")
    private String consumableNum;
    @ApiModelProperty("耗材单价（元）")
    private String consumableUnitPrice;

}