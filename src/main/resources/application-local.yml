spring:
  profiles:
    active: local,swagger
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **************************************************************************************************************************************
    username: root
    password: YSZ@yfb@2018
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  redis:
    database: 2
    host: *************
    password: fengyun123456
    port: 9701

cube:
  connect-timeout: 2
  network-timeout: 30
  charset: UTF-8
  http:
    tracker-http-port: 80
    anti-steal-token: no
    secret-key: tetrisDFSPass.!23
  tracker-server: *************:22122
  storage-server: *************
  storage-port: 23000
  file-server-path: http://*************:8081/
  file-group: group1


logging:
  level:
    ROOT: INFO
    com.fengyun.udf.uaa: DEBUG

cache:
  isInit: false