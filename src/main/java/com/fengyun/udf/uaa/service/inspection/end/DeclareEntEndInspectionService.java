package com.fengyun.udf.uaa.service.inspection.end;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.constant.DeclareCons;
import com.fengyun.udf.uaa.domain.declareproject.DeclareEnt;
import com.fengyun.udf.uaa.domain.flowform.*;
import com.fengyun.udf.uaa.domain.inspection.end.*;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.inspection.end.*;
import com.fengyun.udf.uaa.dto.response.inspection.end.*;
import com.fengyun.udf.uaa.dto.response.declareproject.RuNodeTaskHistoryShowDTO;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareEntMapper;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareProjectMapper;
import com.fengyun.udf.uaa.mapper.flowform.*;
import com.fengyun.udf.uaa.mapper.inspection.end.*;
import com.fengyun.udf.uaa.mapper.system.UserMapper;
import com.fengyun.udf.uaa.service.declareproject.DeclareFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.fengyun.udf.uaa.util.BigDecimalUtil.double2BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@SuppressWarnings("all")
@Transactional(rollbackFor = Exception.class)
public class DeclareEntEndInspectionService {

    @Autowired
    private DeclareEntEndAchievementOtherMapper achievementOtherMapper;
    @Autowired
    private DeclareEntEndAchievementPaperMapper achievementPaperMapper;
    @Autowired
    private DeclareEntEndAchievementPatentMapper achievementPatentMapper;
    @Autowired
    private DeclareEntEndAttachmentMapper attachmentMapper;
    @Autowired
    private DeclareEntEndBasicInfoMapper basicInfoMapper;
    @Autowired
    private DeclareEntEndCompletionMapper completionMapper;
    @Autowired
    private DeclareEntEndFundingMapper fundingMapper;
    @Autowired
    private DeclareEntEndFundingUsageMapper fundingUsageMapper;
    @Autowired
    private DeclareEntEndParticipantMapper participantMapper;
    @Autowired
    private DeclareEntEndResponsibleUnitMapper responsibleUnitMapper;
    @Autowired
    private DeclareEntEndSuzhouUnitMapper suzhouUnitMapper;

    @Autowired
    private ConditionMapper conditionMapper;
    @Autowired
    private PersonnelSituationMapper situationMapper;
    @Autowired
    private ResponsibleUnitMapper unitMapper;
    @Autowired
    private ResponsibleUnitInfoMapper unitInfoMapper;
    @Autowired
    private ProjectScheduleMapper scheduleMapper;
    @Autowired
    private SourceFundsMapper fundsMapper;
    @Autowired
    private SpendingBudgetMapper budgetMapper;
    @Autowired
    private AttachmentFormMapper attachmentFormMapper;
    @Autowired
    private DeclareEntMapper declareEntMapper;
    @Autowired
    private DeclareProjectMapper declareProjectMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DeclareFlowService declareFlowService;

    /**
     * 提交结题验收
     * @param projectId 项目ID
     */
    public void submitEndInspection(String projectId) {
        DeclareEntEndBasicInfo basicInfo = getBasicInfoFromDataBase(projectId);
        if(StrUtil.isBlank(basicInfo.getId())) {
            throw new ValidationException("结题验收", "单位基本信息还未保存, 请先保存单位基本信息");
        }
        boolean firstTimeSubmit = DeclareCons.MIDDLE_INSPECTION_STATUS.SAVE.getCode().equals(basicInfo.getStatus());
        basicInfo.setStatus(DeclareCons.MIDDLE_INSPECTION_STATUS.SUBMIT.getCode());
        basicInfoMapper.updateById(basicInfo);

        // 更新项目状态为结题验收审核中
        pushDeclareEntProjectStatus2EndtInspectionAuditing(projectId);

        // 创建提交历史
        declareFlowService.createHistory("结题验收提交",
                basicInfo.getId(),
                null,
                firstTimeSubmit? "start": "restart");
    }

    private void pushDeclareEntProjectStatus2EndtInspectionAuditing(String projectId) {
        // 更新项目状态为结题验收审核中
        DeclareEnt declareEnt = declareEntMapper.selectById(projectId);
        declareEnt.setProjectStatus(Constants.NEW_PROJECT_STATUS.FINAL_ACCEPTANCE_AUDITING.getCode());
        declareEntMapper.updateById(declareEnt);
    }

    /**
     * 审核结题验收
     * @param dto 审核dto
     */
    public void auditEndInspection(DeclareEntEndInspectionAuditDTO dto) {
        DeclareEntEndBasicInfo basicInfo = getBasicInfoFromDataBase(dto.getProjectId());
        if(!DeclareCons.MIDDLE_INSPECTION_STATUS.SUBMIT.getCode().equals(basicInfo.getStatus())) {
            throw new ValidationException("结题验收", "该状态不允许审核, 请刷新列表后重新操作");
        }
        // 状态
        basicInfo.setStatus(DeclareCons.MIDDLE_INSPECTION_AUDIT_OPERATION.getByCode(dto.getEvent()).getStatusCode());
        // 保存最新审核意见
        basicInfo.setAuditOpinion(dto.getRemark());
        basicInfoMapper.updateById(basicInfo);
        // 创建历史
        declareFlowService.createHistory("结题验收审核",
                basicInfo.getId(),
                dto.getRemark(),
                dto.getEvent());
    }

    /**
     * 分页查找结题验收审核列表
     * @param page 分页参数
     * @param query 查询参数
     * @return 审核分页列表
     */
    public Page<DeclareEntEndBasicInfoListDTO> pageAudit(
            Page<DeclareEntEndBasicInfoListDTO> page, DeclareEntEndInspectionQueryDTO query) {
        List<DeclareEntEndBasicInfoListDTO> result = basicInfoMapper.page(page, query);
        page.setRecords(result);
        return page;
    }

    /**
     * 保存单位基本信息
     *
     * @param dto 单位基本信息聚合DTO
     */
    public void saveUnitBasicInfo(EndUnitBasicInfoSaveDTO dto) {
        DeclareEntEndBasicInfoSaveDTO basicInfo = dto.getBasicInfo();
        DeclareEntEndResponsibleUnitSaveDTO responsibleUnit = dto.getResponsibleUnit();
        DeclareEntEndSuzhouUnitSaveDTO suzhouUnit = dto.getSuzhouUnit();
        // 保存基本信息
        saveBasicInfo(basicInfo);
        // 保存责任单位信息
        saveResponsibleUnit(responsibleUnit);
        // 保存苏州单位信息
        saveSuzhouUnit(suzhouUnit);
    }

    /**
     * 保存项目完成情况
     *
     * @param dto 项目完成情况DTO
     */
    public void saveProjectCompletion(ProjectEndCompletionSaveDTO dto) {
        DeclareEntEndCompletionSaveDTO completion = dto.getCompletion();
        // 保存项目完成情况
        saveProjectCompletion(completion);
    }

    /**
     * 保存阶段性成果
     *
     * @param dto 阶段性成果DTO
     */
    public void saveProjectAchievement(EndStageAchievementSaveDTO dto) {
        String projectId = dto.getProjectId();
        List<DeclareEntEndAchievementPaperSaveDTO> paper = dto.getPaper();
        List<DeclareEntEndAchievementPatentSaveDTO> patent = dto.getPatent();
        List<DeclareEntEndAchievementOtherSaveDTO> other = dto.getOther();
        // 保存项目完成情况
        savePaper(projectId, paper);
        // 保存专利申请情况
        savePatent(projectId, patent);
        // 保存其他阶段性成果
        saveOther(projectId, other);
    }

    /**
     * 保存:项目人员情况
     *
     * @param dto 项目人员情况DTO
     */
    public void saveProjectParticipant(ProjectEndParticipantSaveDTO dto) {
        String projectId = dto.getProjectId();
        List<DeclareEntEndParticipantSaveDTO> participants = dto.getParticipants();
        // 保存项目项目人员情况
        saveParticipant(projectId, participants);
    }

    /**
     * 保存:项目人员情况
     *
     * @param dto 项目人员情况DTO
     */
    public void saveProjectFunding(ProjectEndFundingSaveDTO dto) {
        DeclareEntEndFundingSaveDTO funding = dto.getFunding();
        DeclareEntEndFundingUsageSaveDTO fundingUsage = dto.getFundingUsage();
        // 保存项目经费到位情况
        saveProjectFunding(funding);
        // 保存项目经费使用情况
        saveProjectFundingUsage(fundingUsage);
    }

    /**
     * 保存:附件
     *
     * @param dto 附件DTO
     */
    public void saveProjectAttachment(DeclareEntEndAttachmentSaveDTO dto) {
        DeclareEntEndAttachment entity = getProjectAttachmentFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        attachmentMapper.insertOrUpdate(entity);
    }

    /**
     * 详情:单位基本信息
     *
     * @param projectId 项目ID
     * @return 单位基本信息
     */
    public EndUnitBasicInfoDetailDTO getUnitBasicInfoDetail(String projectId) {
        DeclareEntEndBasicInfoDetailDTO basicInfoDetail = getBasicInfoDetail(projectId);
        List<RuNodeTaskHistoryShowDTO> taskHistory = declareFlowService.getTaskHistory(basicInfoDetail.getId());
        return new EndUnitBasicInfoDetailDTO(
                basicInfoDetail,
                getResponsibleUnitDetail(projectId),
                getSuzhouUnitDetail(projectId),
                taskHistory);
    }

    /**
     * 详情:单位基本信息
     *
     * @param projectId 项目ID
     * @return 单位基本信息
     */
    public ProjectEndCompletionDetailDTO getProjectCompletionDetail(String projectId) {
        return new ProjectEndCompletionDetailDTO(getCompletionDetail(projectId));
    }

    /**
     * 详情:阶段性成果
     *
     * @param projectId 项目ID
     * @return 阶段性成果
     */
    public EndStageAchievementDetailDTO getStageAchievementDetail(String projectId) {
        return new EndStageAchievementDetailDTO(
                projectId,
                getAchievementPaperDetail(projectId),
                getAchievementPatentDetail(projectId),
                getAchievementOtherDetail(projectId)
        );
    }

    /**
     * 详情:阶段性成果
     *
     * @param projectId 项目ID
     * @return 阶段性成果
     */
    public ProjectEndParticipantDetailDTO getProjectParticipantDetail(String projectId) {
        return new ProjectEndParticipantDetailDTO(
                projectId,
                getParticipantDetail(projectId));
    }

    /**
     * 详情:项目经费到位与使用情况
     *
     * @param projectId 项目ID
     * @return 项目经费到位与使用情况
     */
    public ProjectEndFundingDetailDTO getProjectFundingDetail(String projectId) {
        return new ProjectEndFundingDetailDTO(
                getFundingDetail(projectId),
                getFundingUsageDetail(projectId));
    }

    /**
     * 详情:附件
     *
     * @param projectId 项目ID
     * @return 附件
     */
    public DeclareEntEndAttachmentDetailDTO getAttachmentDetail(String projectId) {
        DeclareEntEndAttachment entity = getProjectAttachmentFromDataBase(projectId);
        DeclareEntEndAttachmentDetailDTO dto = new DeclareEntEndAttachmentDetailDTO();
        BeanUtil.copyProperties(entity, dto);
        dto.setProjectId(projectId);
        return dto;
    }

    /**
     * 从数据库获取:附件
     *
     * @param projectId 项目ID
     * @return 附件
     */
    private DeclareEntEndAttachment getProjectAttachmentFromDataBase(String projectId) {
        return Optional.ofNullable(attachmentMapper.selectOne(Wrappers.<DeclareEntEndAttachment>lambdaQuery()
                        .eq(DeclareEntEndAttachment::getProjectId, projectId)))
                .orElseGet(DeclareEntEndAttachment::new);
    }


    /**
     * 保存或更新:项目经费到位情况
     *
     * @param dto 项目经费到位情况
     */
    private void saveProjectFundingUsage(DeclareEntEndFundingUsageSaveDTO dto) {
        DeclareEntEndFundingUsage entity = getProjectFundingUsageFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        fundingUsageMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取:项目经费到位情况
     *
     * @param projectId 项目ID
     * @return 项目经费到位情况
     */
    private DeclareEntEndFundingUsage getProjectFundingUsageFromDataBase(String projectId) {
        return Optional.ofNullable(fundingUsageMapper.selectOne(Wrappers.<DeclareEntEndFundingUsage>lambdaQuery()
                .eq(DeclareEntEndFundingUsage::getProjectId, projectId)))
                .orElseGet(DeclareEntEndFundingUsage::new);
    }

    /**
     * 保存或更新:项目经费到位情况
     *
     * @param dto 项目经费到位情况
     */
    private void saveProjectFunding(DeclareEntEndFundingSaveDTO dto) {
        DeclareEntEndFunding entity = getProjectFundingFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        fundingMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取:项目经费到位情况
     *
     * @param projectId 项目ID
     * @return 项目经费到位情况
     */
    private DeclareEntEndFunding getProjectFundingFromDataBase(String projectId) {
        return Optional.ofNullable(fundingMapper.selectOne(Wrappers.<DeclareEntEndFunding>lambdaQuery()
                        .eq(DeclareEntEndFunding::getProjectId, projectId)))
                .orElseGet(DeclareEntEndFunding::new);
    }

    /**
     * 保存或更新:项目人员情况
     *
     * @param projectId 项目ID
     * @param patent 项目人员情况
     */
    private void saveParticipant(String projectId, List<DeclareEntEndParticipantSaveDTO> patent) {
        // remove all
        removeParticipantFromDataBase(projectId);
        // insert all
        for (DeclareEntEndParticipantSaveDTO dto : patent) {
            DeclareEntEndParticipant entity = new DeclareEntEndParticipant();
            BeanUtil.copyProperties(dto, entity);
            participantMapper.insert(entity);
        }
    }

    /**
     * 从数据库获得:项目人员情况
     *
     * @param projectId 项目ID
     * @return 项目人员情况
     */
    private List<DeclareEntEndParticipant> getParticipantFromDataBase(String projectId) {
        return participantMapper.selectList(Wrappers.<DeclareEntEndParticipant>lambdaQuery()
                .eq(DeclareEntEndParticipant::getProjectId, projectId));
    }

    /**
     * 从数据库删除:项目人员情况
     *
     * @param projectId 项目ID
     */
    private void removeParticipantFromDataBase(String projectId) {
        participantMapper.delete(Wrappers.<DeclareEntEndParticipant>lambdaQuery()
                .eq(DeclareEntEndParticipant::getProjectId, projectId));
    }

    /**
     * 保存或更新项目阶段性成果-其他阶段性成果
     *
     * @param projectId 项目ID
     * @param patent 项目阶段性成果-其他阶段性成果
     */
    private void saveOther(String projectId, List<DeclareEntEndAchievementOtherSaveDTO> patent) {
        // remove all
        removeOtherFromDataBase(projectId);
        // insert all
        for (DeclareEntEndAchievementOtherSaveDTO dto : patent) {
            DeclareEntEndAchievementOther entity = new DeclareEntEndAchievementOther();
            BeanUtil.copyProperties(dto, entity);
            achievementOtherMapper.insert(entity);
        }
    }

    /**
     * 从数据库获得项目阶段性成果-其他阶段性成果
     *
     * @param projectId 项目ID
     * @return 项目阶段性成果-其他阶段性成果
     */
    private List<DeclareEntEndAchievementOther> getOtherFromDataBase(String projectId) {
        return achievementOtherMapper.selectList(Wrappers.<DeclareEntEndAchievementOther>lambdaQuery()
                .eq(DeclareEntEndAchievementOther::getProjectId, projectId));
    }

    /**
     * 从数据库删除项目阶段性成果-其他阶段性成果
     *
     * @param projectId 项目ID
     */
    private void removeOtherFromDataBase(String projectId) {
        achievementOtherMapper.delete(Wrappers.<DeclareEntEndAchievementOther>lambdaQuery()
                .eq(DeclareEntEndAchievementOther::getProjectId, projectId));
    }

    /**
     * 保存或更新项目阶段性成果-专利申请情况
     *
     * @param projectId 项目ID
     * @param patent 项目阶段性成果-专利申请情况
     */
    private void savePatent(String projectId, List<DeclareEntEndAchievementPatentSaveDTO> patent) {
        // remove all
        removePatentFromDataBase(projectId);
        // insert all
        for (DeclareEntEndAchievementPatentSaveDTO dto : patent) {
            DeclareEntEndAchievementPatent entity = new DeclareEntEndAchievementPatent();
            BeanUtil.copyProperties(dto, entity);
            achievementPatentMapper.insert(entity);
        }
    }

    /**
     * 从数据库获得项目阶段性成果-专利申请情况
     *
     * @param projectId 项目ID
     * @return 项目阶段性成果-专利申请情况
     */
    private List<DeclareEntEndAchievementPatent> getPatentFromDataBase(String projectId) {
        return achievementPatentMapper.selectList(Wrappers.<DeclareEntEndAchievementPatent>lambdaQuery()
                .eq(DeclareEntEndAchievementPatent::getProjectId, projectId));
    }

    /**
     * 从数据库删除项目阶段性成果-专利申请情况
     *
     * @param projectId 项目ID
     */
    private void removePatentFromDataBase(String projectId) {
        achievementPatentMapper.delete(Wrappers.<DeclareEntEndAchievementPatent>lambdaQuery()
                .eq(DeclareEntEndAchievementPatent::getProjectId, projectId));
    }

    /**
     * 保存或更新项目阶段性成果-论文发表情况
     *
     * @param projectId 项目ID
     * @param paper 项目阶段性成果-论文发表情况
     */
    private void savePaper(String projectId, List<DeclareEntEndAchievementPaperSaveDTO> paper) {
        // remove all
        removePaperFromDataBase(projectId);
        // insert all
        for (DeclareEntEndAchievementPaperSaveDTO dto : paper) {
            DeclareEntEndAchievementPaper entity = new DeclareEntEndAchievementPaper();
            BeanUtil.copyProperties(dto, entity);
            achievementPaperMapper.insert(entity);
        }
    }

    /**
     * 从数据库获得项目阶段性成果-论文发表情况
     *
     * @param projectId 项目ID
     * @return 项目阶段性成果-论文发表情况
     */
    private List<DeclareEntEndAchievementPaper> getPaperFromDataBase(String projectId) {
        return achievementPaperMapper.selectList(Wrappers.<DeclareEntEndAchievementPaper>lambdaQuery()
                .eq(DeclareEntEndAchievementPaper::getProjectId, projectId));
    }

    /**
     * 从数据库删除项目阶段性成果-论文发表情况
     *
     * @param projectId 项目ID
     */
    private void removePaperFromDataBase(String projectId) {
        achievementPaperMapper.delete(Wrappers.<DeclareEntEndAchievementPaper>lambdaQuery()
                .eq(DeclareEntEndAchievementPaper::getProjectId, projectId));
    }

    /**
     * 保存或更新项目完成情况
     *
     * @param dto 项目完成情况
     */
    private void saveProjectCompletion(DeclareEntEndCompletionSaveDTO dto) {
        DeclareEntEndCompletion entity = getProjectCompletionFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        completionMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取项目完成情况
     *
     * @param projectId 项目ID
     * @return 项目完成情况
     */
    private DeclareEntEndCompletion getProjectCompletionFromDataBase(String projectId) {
        return Optional.ofNullable(completionMapper.selectOne(Wrappers.<DeclareEntEndCompletion>lambdaQuery()
                        .eq(DeclareEntEndCompletion::getProjectId, projectId)))
                .orElseGet(DeclareEntEndCompletion::new);
    }


    /**
     * 保存或更新苏州单位信息
     *
     * @param dto 苏州单位信息
     */
    private void saveSuzhouUnit(DeclareEntEndSuzhouUnitSaveDTO dto) {
        DeclareEntEndSuzhouUnit entity = getSuzhouUnitFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        suzhouUnitMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取苏州单位信息
     *
     * @param projectId 项目ID
     * @return 苏州单位信息
     */
    private DeclareEntEndSuzhouUnit getSuzhouUnitFromDataBase(String projectId) {
        return Optional.ofNullable(suzhouUnitMapper.selectOne(Wrappers.<DeclareEntEndSuzhouUnit>lambdaQuery()
                        .eq(DeclareEntEndSuzhouUnit::getProjectId, projectId)))
                .orElseGet(DeclareEntEndSuzhouUnit::new);
    }

    /**
     * 保存或更新承担单位信息
     *
     * @param dto 承担单位信息
     */
    private void saveResponsibleUnit(DeclareEntEndResponsibleUnitSaveDTO dto) {
        DeclareEntEndResponsibleUnit entity = getResponsibleUnitFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        responsibleUnitMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取承担单位信息
     *
     * @param projectId 项目ID
     * @return 承担单位信息
     */
    private DeclareEntEndResponsibleUnit getResponsibleUnitFromDataBase(String projectId) {
        return Optional.ofNullable(responsibleUnitMapper.selectOne(Wrappers.<DeclareEntEndResponsibleUnit>lambdaQuery()
                        .eq(DeclareEntEndResponsibleUnit::getProjectId, projectId)))
                .orElseGet(DeclareEntEndResponsibleUnit::new);
    }

    /**
     * 获取详情:基础信息
     *
     * @param projectId 项目ID
     * @return 详情:基础信息
     */
    private DeclareEntEndBasicInfoDetailDTO getBasicInfoDetail(String projectId) {
        DeclareEntEndBasicInfo entity = getBasicInfoFromDataBase(projectId);
        DeclareEntEndBasicInfoDetailDTO dto = new DeclareEntEndBasicInfoDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutBasicInfo(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 承担单位基础信息
     *
     * @param projectId 项目ID
     * @return 详情:基础信息
     */
    private DeclareEntEndResponsibleUnitDetailDTO getResponsibleUnitDetail(String projectId) {
        DeclareEntEndResponsibleUnit entity = getResponsibleUnitFromDataBase(projectId);
        DeclareEntEndResponsibleUnitDetailDTO dto = new DeclareEntEndResponsibleUnitDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutResponsibleUnit(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 承担单位基础信息
     *
     * @param projectId 项目ID
     * @return 详情:基础信息
     */
    private DeclareEntEndSuzhouUnitDetailDTO getSuzhouUnitDetail(String projectId) {
        DeclareEntEndSuzhouUnit entity = getSuzhouUnitFromDataBase(projectId);
        DeclareEntEndSuzhouUnitDetailDTO dto = new DeclareEntEndSuzhouUnitDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutSuzhouUnit(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 项目完成情况
     *
     * @param projectId 项目ID
     * @return 详情:项目完成情况
     */
    private DeclareEntEndCompletionDetailDTO getCompletionDetail(String projectId) {
        DeclareEntEndCompletion entity = getProjectCompletionFromDataBase(projectId);
        DeclareEntEndCompletionDetailDTO dto = new DeclareEntEndCompletionDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutCompletion(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 论文发表情况
     *
     * @param projectId 项目ID
     * @return 详情:论文发表情况
     */
    private List<DeclareEntEndAchievementPaperDetailDTO> getAchievementPaperDetail(String projectId) {
        return getPaperFromDataBase(projectId).stream()
                .map(entity -> {
                    DeclareEntEndAchievementPaperDetailDTO dto = new DeclareEntEndAchievementPaperDetailDTO();
                    BeanUtil.copyProperties(entity, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取详情: 专利申请情况
     *
     * @param projectId 项目ID
     * @return 详情:专利申请情况
     */
    private List<DeclareEntEndAchievementPatentDetailDTO> getAchievementPatentDetail(String projectId) {
        return getPatentFromDataBase(projectId).stream()
                .map(entity -> {
                    DeclareEntEndAchievementPatentDetailDTO dto = new DeclareEntEndAchievementPatentDetailDTO();
                    BeanUtil.copyProperties(entity, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取详情: 其他阶段性成果
     *
     * @param projectId 项目ID
     * @return 详情:其他阶段性成果
     */
    private List<DeclareEntEndAchievementOtherDetailDTO> getAchievementOtherDetail(String projectId) {
        return getPatentFromDataBase(projectId).stream()
                .map(entity -> {
                    DeclareEntEndAchievementOtherDetailDTO dto = new DeclareEntEndAchievementOtherDetailDTO();
                    BeanUtil.copyProperties(entity, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取详情: 其他阶段性成果
     *
     * @param projectId 项目ID
     * @return 详情:其他阶段性成果
     */
    private List<DeclareEntEndParticipantDetailDTO> getParticipantDetail(String projectId) {
        List<DeclareEntEndParticipant> participants = getParticipantFromDataBase(projectId);
        if(CollUtil.isEmpty(participants)) {
            return autoBringOutParticipant(projectId);
        }else {
            return participants.stream()
                    .map(entity -> {
                        DeclareEntEndParticipantDetailDTO dto = new DeclareEntEndParticipantDetailDTO();
                        BeanUtil.copyProperties(entity, dto);
                        return dto;
                    })
                    .collect(Collectors.toList());
        }
    }

    /**
     * 获取详情: 项目经费到位情况
     *
     * @param projectId 项目ID
     * @return 详情:项目经费到位情况
     */
    private DeclareEntEndFundingDetailDTO getFundingDetail(String projectId) {
        DeclareEntEndFunding entity = getProjectFundingFromDataBase(projectId);
        DeclareEntEndFundingDetailDTO dto = new DeclareEntEndFundingDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutFunding(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 获取详情: 项目经费使用情况
     *
     * @param projectId 项目ID
     * @return 详情:项目经费使用情况
     */
    private DeclareEntEndFundingUsageDetailDTO getFundingUsageDetail(String projectId) {
        DeclareEntEndFundingUsage entity = getProjectFundingUsageFromDataBase(projectId);
        DeclareEntEndFundingUsageDetailDTO dto = new DeclareEntEndFundingUsageDetailDTO();
        if(Objects.isNull(entity.getId())) {
            autoBringOutFundingUsage(dto, projectId);
        }else {
            BeanUtil.copyProperties(entity, dto);
        }
        return dto;
    }

    /**
     * 自动带出经费到位情况
     *
     * @param dto 经费到位情况
     * @param projectId 项目ID
     */
    private void autoBringOutFunding(DeclareEntEndFundingDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        SourceFunds funds = getFundsFromDatabase(declareEnt.getDeclareId(), userId);
        // 单位自有经费
        dto.setOwned(double2BigDecimal(funds.getOwned()));
        // 银行贷款经费
        dto.setLoan(double2BigDecimal(funds.getLoan()));
        // 风险投资经费
        dto.setVc(double2BigDecimal(funds.getVc()));
        // 国创中心资助经费
        dto.setFunding(double2BigDecimal(funds.getFunding()));
        // 其他
        dto.setOther(double2BigDecimal(funds.getOther()));
        // 项目id
        dto.setProjectId(projectId);
    }

    /**
     * 自动带出项目资费使用情况
     *
     * @param dto 项目资费使用情况
     * @param projectId 项目ID
     */
    private void autoBringOutFundingUsage(DeclareEntEndFundingUsageDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        SpendingBudget budget = getBudgetFromDatabase(declareEnt.getDeclareId(), userId);

        dto.setEquipmentPurchaseBudget(double2BigDecimal(budget.getEquipmentPurchaseBudget()));
        dto.setEquipmentPurchaseSupport(double2BigDecimal(budget.getEquipmentPurchaseSupport()));
        dto.setEquipmentTrialBudget(double2BigDecimal(budget.getEquipmentTrialBudget()));
        dto.setEquipmentTrialSupport(double2BigDecimal(budget.getEquipmentTrialSupport()));
        dto.setEquipmentModificationBudget(double2BigDecimal(budget.getEquipmentModificationBudget()));
        dto.setEquipmentModificationSupport(double2BigDecimal(budget.getEquipmentModificationSupport()));
        dto.setMaterialFeeBudget(double2BigDecimal(budget.getMaterialFeeBudget()));
        dto.setMaterialFeeSupport(double2BigDecimal(budget.getMaterialFeeSupport()));
        dto.setTravelExpensesBudget(double2BigDecimal(budget.getTravelExpensesBudget()));
        dto.setTravelExpensesSupport(double2BigDecimal(budget.getTravelExpensesSupport()));
        dto.setLaborFeeBudget(double2BigDecimal(budget.getLaborFeeBudget()));
        dto.setLaborFeeSupport(double2BigDecimal(budget.getLaborFeeSupport()));
        dto.setOtherBudget(double2BigDecimal(budget.getOtherBudget()));
        dto.setOtherSupport(double2BigDecimal(budget.getOtherSupport()));
        dto.setPerformancePayBudget(double2BigDecimal(budget.getPerformancePayBudget()));
        dto.setPerformancePaySupport(double2BigDecimal(budget.getPerformancePaySupport()));
        dto.setManagePayBudget(double2BigDecimal(budget.getManagePayBudget()));
        dto.setManagePaySupport(double2BigDecimal(budget.getManagePaySupport()));
        // 项目id
        dto.setProjectId(projectId);
    }

    /**
     * 自动带出项目人员情况
     *
     * @param projectId 项目ID
     * @return 项目人员情况
     */
    private List<DeclareEntEndParticipantDetailDTO> autoBringOutParticipant(String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        PersonnelSituation situation = getPersonnelSituationFromDatabase(declareEnt.getDeclareId(), userId);
        String projectParticipantsJson = situation.getProjectParticipants();
        if(JSONUtil.isJson(projectParticipantsJson)) {
            List<DeclareEntEndParticipantDetailDTO> results
                    = JSON.parseArray(projectParticipantsJson, DeclareEntEndParticipantDetailDTO.class);
            if(CollUtil.isNotEmpty(results)) {
                results.forEach(x -> x.setProjectId(projectId));
            }
            return results;
        }
        return new ArrayList<>();
    }

    /**
     * 自动带出项目完成情况
     *
     * @param dto 项目完成情况
     * @param projectId 项目ID
     */
    private void autoBringOutCompletion(DeclareEntEndCompletionDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        ProjectSchedule schedule = getScheduleFromDatabase(declareEnt.getDeclareId(), userId);
        // 第1阶段起止时间
        dto.setFirstStageDate(schedule.getFirstStageDate());
        // 第1阶段考核指标
        dto.setFirstSchedule(schedule.getFirstSchedule());

        // 第2阶段起止时间
        dto.setSecondStageDate(schedule.getSecondStageDate());
        // 第2阶段考核指标
        dto.setSecondSchedule(schedule.getSecondSchedule());

        // 第3阶段起止时间
        dto.setThirdStageDate(schedule.getThirdStageDate());
        // 第3阶段考核指标
        dto.setThirdSchedule(schedule.getThirdSchedule());

        // 项目id
        dto.setProjectId(projectId);
    }

    private void autoBringOutSuzhouUnit(DeclareEntEndSuzhouUnitDetailDTO dto, String projectId) {
        dto.setProjectId(projectId);
    }

    private void autoBringOutResponsibleUnit(DeclareEntEndResponsibleUnitDetailDTO dto, String projectId) {
        // 自动带出属性
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        String userId = SecurityUtils.getUserId();
        ResponsibleUnit unit = getUnitFromDatabase(declareEnt.getDeclareId(), userId);
        ResponsibleUnitInfo unitInfo = getUnitInfoFromDatabase(declareEnt.getDeclareId(), userId);
        // 单位名称
        dto.setResponsibleName(unit.getResponsibleName());
        // 注册时间
        dto.setRegisterDate(Optional.ofNullable(unitInfo.getRegisterDate())
                .map(date -> date.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate())
                .orElse(null));
        // 注册资本
        dto.setRegisteredCapital(unitInfo.getRegisteredCapital());
        // 法人代表
        dto.setCorporateRepresentative(unitInfo.getCorporateRepresentative());
        // 注册地址
        dto.setRegisterAddress(unitInfo.getRegisterAddress());
        // 公司人数
        dto.setEmployerNum(unitInfo.getEmployees());
        // 项目id
        dto.setProjectId(projectId);
    }

    private void autoBringOutBasicInfo(DeclareEntEndBasicInfoDetailDTO dto, String projectId) {
        // 自动带出属性
        String userId = SecurityUtils.getUserId();
        DeclareEnt declareEnt = getDeclareEntFromDataBase(projectId);
        Condition condition = getFlowFormConditionFromDataBase(declareEnt.getDeclareId(), userId);
        ResponsibleUnit responsibleUnit = getFlowFormResponsibleUnitFromDataBase(declareEnt.getDeclareId(), userId);
        PersonnelSituation personnelSituation = getPersonnelSituationFromDatabase(declareEnt.getDeclareId(), userId);
        User user = userMapper.selectById(userId);
        // 项目名称
        dto.setSubjectName(condition.getSubjectName());
        // 项目类别
        dto.setDeclareType(declareEnt.getDeclareType());
        // 项目编号
        dto.setProjectNo(declareEnt.getProjectNo());
        // 项目承担单位
        dto.setCompanyName(responsibleUnit.getResponsibleName());
        // 项目负责人
        dto.setPrincipalName(personnelSituation.getPrincipalName());
        // 项目负责人电话
        dto.setPrincipalTel(personnelSituation.getTel());
        // 项目联系人
        dto.setContactName(user.getNickName());
        // 项目联系人电话
        dto.setContactTel(user.getTel());
        // 项目id
        dto.setProjectId(projectId);
    }

    /**
     * 保存或更新基础信息
     *
     * @param dto 基础信息
     */
    private void saveBasicInfo(DeclareEntEndBasicInfoSaveDTO dto) {
        DeclareEntEndBasicInfo entity = getBasicInfoFromDataBase(dto.getProjectId());
        BeanUtil.copyProperties(dto, entity);
        if(StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus(DeclareCons.MIDDLE_INSPECTION_STATUS.SAVE.getCode());
        }
        basicInfoMapper.insertOrUpdate(entity);
    }

    /**
     * 从数据库获取基础信息
     *
     * @param projectId 项目ID
     * @return 基础信息
     */
    private DeclareEntEndBasicInfo getBasicInfoFromDataBase(String projectId) {
        return Optional.ofNullable(basicInfoMapper.selectOne(Wrappers.<DeclareEntEndBasicInfo>lambdaQuery()
                        .eq(DeclareEntEndBasicInfo::getProjectId, projectId)))
                .orElseGet(DeclareEntEndBasicInfo::new);
    }

    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓流程初始表单获取方法区开始↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

    /**
     * 从数据库获取企业申报
     *
     * @param projectId 项目ID
     * @return 企业申报
     */
    private DeclareEnt getDeclareEntFromDataBase(String projectId) {
        return Optional.ofNullable(declareEntMapper.selectById(projectId))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取条件
     *
     * @param projectId 项目ID
     * @return 条件
     */
    private Condition getFlowFormConditionFromDataBase(String declareId, String userId) {
        return Optional.ofNullable(conditionMapper.selectOne(Wrappers.<Condition>lambdaQuery()
                        .eq(Condition::getProjectId, declareId)
                        .eq(Condition::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取责任单位
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 责任单位
     */
    private ResponsibleUnit getFlowFormResponsibleUnitFromDataBase(String declareId, String userId) {
        // 这个流程表单里存的projectid是父项目的id 即declare project的id而不是delcare ent的id
        return Optional.ofNullable(unitMapper.selectOne(Wrappers.<ResponsibleUnit>lambdaQuery()
                        .eq(ResponsibleUnit::getProjectId, declareId)
                        .eq(ResponsibleUnit::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取参与人
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 参与人
     */
    private PersonnelSituation getPersonnelSituationFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(situationMapper.selectOne(Wrappers.<PersonnelSituation>lambdaQuery()
                        .eq(PersonnelSituation::getProjectId, declareId)
                        .eq(PersonnelSituation::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取单位
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 单位
     */
    private ResponsibleUnit getUnitFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(unitMapper.selectOne(Wrappers.<ResponsibleUnit>lambdaQuery()
                        .eq(ResponsibleUnit::getProjectId, declareId)
                        .eq(ResponsibleUnit::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取单位信息
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 单位信息
     */
    private ResponsibleUnitInfo getUnitInfoFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(unitInfoMapper.selectOne(Wrappers.<ResponsibleUnitInfo>lambdaQuery()
                        .eq(ResponsibleUnitInfo::getProjectId, declareId)
                        .eq(ResponsibleUnitInfo::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取计划
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 计划
     */
    private ProjectSchedule getScheduleFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(scheduleMapper.selectOne(Wrappers.<ProjectSchedule>lambdaQuery()
                        .eq(ProjectSchedule::getProjectId, declareId)
                        .eq(ProjectSchedule::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取资金来源
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 资金来源
     */
    private SourceFunds getFundsFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(fundsMapper.selectOne(Wrappers.<SourceFunds>lambdaQuery()
                        .eq(SourceFunds::getProjectId, declareId)
                        .eq(SourceFunds::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }

    /**
     * 从数据库获取项目投资估算
     *
     * @param declareId 大项目ID
     * @param userId 用户ID
     * @return 项目投资估算
     */
    private SpendingBudget getBudgetFromDatabase(String declareId, String userId) {
        return Optional.ofNullable(budgetMapper.selectOne(Wrappers.<SpendingBudget>lambdaQuery()
                        .eq(SpendingBudget::getProjectId, declareId)
                        .eq(SpendingBudget::getCreatedId, userId)))
                .orElseThrow(() -> new ValidationException("结题验收", "该项目在本系统中不存在"));
    }
    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑流程初始表单获取方法区结束↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

}
