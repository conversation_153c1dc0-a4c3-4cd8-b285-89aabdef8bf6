package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仪器预约工单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
@TableName("in_instrument_order_work")
public class InstrumentOrderWork extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Integer id;

    /**
     * 仪器租赁单价
     */
    @TableField("lease_price")
    private Double leasePrice;

    /**
     * 仪器租赁价格单位
     */
    @TableField("lease_price_unit")
    private String leasePriceUnit;

    /**
     * 仪器租赁最低收费
     */
    @TableField("lease_price_min")
    private Double leasePriceMin;

    /**
     * 委托测试单价
     */
    @TableField("test_price")
    private Double testPrice;

    /**
     * 委托测试价格单位
     */
    @TableField("test_price_unit")
    private String testPriceUnit;

    /**
     * 计算方式
     */
    @TableField("calculate_method")
    private String calculateMethod;

    /**
     * 耗材金额
     */
    @TableField("consumable_amount")
    private Double consumableAmount;

    /**
     * 核准金额
     */
    @TableField("amount")
    private Double amount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 生成工单pdf
     */
    @TableField("work_pdf")
    private String workPdf;

    /**
     * 上传已盖章工单pdf
     */
    @TableField("work_stamp_pdf")
    private String workStampPdf;

    /**
     * 工单发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;


}
