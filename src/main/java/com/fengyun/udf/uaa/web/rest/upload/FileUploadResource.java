package com.fengyun.udf.uaa.web.rest.upload;

import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.response.UploadResultDTO;
import com.fengyun.udf.uaa.fastdfs.ClientGlobal;
import com.fengyun.udf.uaa.fastdfs.client.UploadFile;
import com.fengyun.udf.uaa.fastdfs.client.UploadManager;
import com.fengyun.udf.uaa.util.ContentTypeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.IntStream;

@RestController
@RequestMapping("/api/upload")
@Api(value = "文件上传")
@Slf4j
public class FileUploadResource extends BaseResource {

    @PostMapping("/file")
    @ApiOperation(value = "上传文件", httpMethod = "POST", response = UploadResultDTO.class, notes = "上传文件")
    public ResponseEntity<ReturnResultDTO<UploadResultDTO>> uploadFile(
            final String _fileName, HttpServletRequest request) throws Exception {
        List<UploadResultDTO> result = new ArrayList<>(1);
        MultipartHttpServletRequest mRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iter = mRequest.getFileNames();
        while (iter.hasNext()) {
            MultipartFile file = mRequest.getFile(iter.next());
            String fileName = StringUtils.isEmpty(_fileName)? file.getOriginalFilename() : _fileName;
            //获取文件类型
            String extName = fileName.substring(fileName.lastIndexOf("."))
                        .toLowerCase()
                        .replace(".", "");
            // 检查文件类型
            checkContentTypeAndExtName(extName, file);
            // 上传文件
            UploadFile uploadfile = new UploadFile(fileName, file.getBytes(), extName);
            String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
            // 返回绝对路径
            UploadResultDTO uploadResult = new UploadResultDTO();
            uploadResult.setUrl(ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
            uploadResult.setType(extName);
            uploadResult.setSize(file.getSize());
            uploadResult.setFilename(fileName);
            result.add(uploadResult);
        }
        if (result.isEmpty()) {
            throw new ValidationException("file", "文件为空");
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }

    @PostMapping("/file/ua")
    @ApiOperation(value = "上传文件", httpMethod = "POST", response = UploadResultDTO.class, notes = "上传文件")
    public ResponseEntity<?> uploadFileUa(final String _fileName, HttpServletRequest request, HttpServletResponse
            response) throws Exception {
        List<UploadResultDTO> result = new ArrayList<>();
        MultipartHttpServletRequest mRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iter = mRequest.getFileNames();
        while (iter.hasNext()) {
            MultipartFile file = mRequest.getFile(iter.next());
            if (file != null && file.getSize() > 5242880) {
                throw new ValidationException("error", "文件大小不能超过5MB");
            }
            String fileName = StringUtils.isEmpty(_fileName)? file.getOriginalFilename() : _fileName;
            //获取文件类型
            String extName = fileName.substring(fileName.lastIndexOf("."))
                    .toLowerCase()
                    .replace(".", "");
            // 检查文件类型
            checkContentTypeAndExtName(extName, file);

            if ("pdf".equals(extName)) {
                log.info("正在处理pdf wenjian");
                byte [] byteArr = file.getBytes();
                InputStream inputStream = new ByteArrayInputStream(byteArr);
                if(containsJavaScript(inputStream)) {
                    throw new ValidationException("file", "文件包含XSS，不能上传");
                }
            }

            // 上传文件
            UploadFile uploadfile = new UploadFile(fileName, file.getBytes(), extName);
            String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
            // 返回绝对路径
            UploadResultDTO uploadResult = new UploadResultDTO();
            uploadResult.setUrl(ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
            uploadResult.setType(extName);
            uploadResult.setSize(file.getSize());
            uploadResult.setFilename(fileName);
            result.add(uploadResult);
        }
        if (result.isEmpty()) {
            throw new ValidationException("file", "文件为空");
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }


    private void checkContentTypeAndExtName(String extName, MultipartFile file) {
        String contentType = file.getContentType();
        if (StringUtils.isBlank(contentType)) {
            throw new ValidationException("error", "contentType 为空，不允许上传");
        }
        String fileContentType = ContentTypeUtils.getContentType("." + extName);
        if (StringUtils.isBlank(fileContentType)) {
            throw new ValidationException("error", "文件不在允许范围内，请上传正确文件");
        }
        if (!contentType.contains(fileContentType)) {
            throw new ValidationException("error", "文件数据错误，请重新上传");
        }
//        if (extName == null || !extName.toLowerCase().equals("mp4")) {
//            if(file.getSize()>10*1024*1024){
//                throw new ValidationException("error", "文件太大，请重新上传");
//            }
//        }

    }

    public static boolean containsJavaScript(InputStream input) throws IOException {
        PDDocument document = PDDocument.load(input);
        return containsJavaScript(document);
    }

    public static boolean containsJavaScript(PDDocument document) throws IOException {
        PDPageTree pages = document.getPages();
        String cosName = document.getDocument().getTrailer().toString();
        if(cosName.contains("COSName{JavaSCript}")||cosName.contains("COSName{JS}")){
            return true;
        }
        return IntStream.range(0, pages.getCount()).anyMatch(i -> {
            return pages.get(i).getCOSObject().toString().contains("COSName{JS}");
        });

        /*Optional<COSObject> pdfJs = document.getDocument().getObjects().stream().filter(cosObject -> {
            String str = Optional.ofNullable(cosObject.getObject()).map(cosBase -> cosBase.toString().toLowerCase()).orElse("");
            return str.contains("javascript") || str.contains("cosname{js}");
        }).findAny();
        return pdfJs.isPresent();*/
    }

}
