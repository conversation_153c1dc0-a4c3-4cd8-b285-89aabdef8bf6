package com.fengyun.udf.uaa.domain.oa.custom;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 立项变更流程表单
 * <br/>
 * 2023/8/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@TableName(value = "zzzz_flow_data_71")
public class FlowData71 {

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @TableField(value = "run_id")
    private String runId;

    @TableField(value = "created_at")
    private String createdAt;

    @TableField(value = "updated_at")
    private String updatedAt;

    /**
     * 变更原因
     */
    @TableField(value = "DATA_62")
    private String data62;

    @TableField(value = "DATA_64")
    private String data64;
}
