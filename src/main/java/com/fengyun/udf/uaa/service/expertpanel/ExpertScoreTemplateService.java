package com.fengyun.udf.uaa.service.expertpanel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertScoreTemplate;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertScoreTemplateCreateDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertScoreTemplateSearchDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO;
import com.fengyun.udf.uaa.mapper.expertpanel.ExpertScoreTemplateMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;

@Service
@Transactional
public class ExpertScoreTemplateService extends BaseService {

    @Autowired
    private ExpertScoreTemplateMapper expertScoreTemplateMapper;

    /**
     * 列表查询
     * @param page
     * @return
     */
    public Page<ExpertScoreTemplateDTO> page(Page<ExpertScoreTemplateDTO> page, ExpertScoreTemplateSearchDTO dto) {
        List<ExpertScoreTemplateDTO> list = expertScoreTemplateMapper.page(page ,dto);
        page.setRecords(list);
        return page;
    }

    public List<ExpertScoreTemplateDTO> getList() {
        return expertScoreTemplateMapper.getList();
    }

    public void save(ExpertScoreTemplateCreateDTO dto) {
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertScoreTemplate entity = new ExpertScoreTemplate();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(UUIDGenerator.getUUID());
        entity.setStatus(Constants.EXPERT_SCORE_TEMPLATE_STATUS.EXPERT_SCORE_TEMPLATE_STATUS_0.getCode());
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        entity.setCreatedId(userId);
        entity.setUpdatedId(userId);
        entity.setCreatedDate(nowTime);
        entity.setUpdatedDate(nowTime);
        expertScoreTemplateMapper.insert(entity);
    }

    public void update(ExpertScoreTemplateCreateDTO dto) {
        ExpertScoreTemplate entity = expertScoreTemplateMapper.selectById(dto.getId());
        if (entity == null) {
            throw new ValidationException("error", "模板不存在");
        }
        ZonedDateTime nowTime = ZonedDateTime.now();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdatedId(SecurityUtils.getUserId());
        entity.setUpdatedDate(nowTime);
        expertScoreTemplateMapper.updateById(entity);
    }

    public ExpertScoreTemplateDTO getById(String id) {
        ExpertScoreTemplate entity = expertScoreTemplateMapper.selectById(id);
        if (entity == null) {
            throw new ValidationException("error", "模板不存在");
        }
        ExpertScoreTemplateDTO dto = new ExpertScoreTemplateDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 删除
     * @param id
     */
    public void delete(String id) {
        expertScoreTemplateMapper.deleteById(id);
    }

    public ExpertScoreTemplateDTO getRelationTemplate(String declareEntId ,String numbersReview){
        ExpertScoreTemplateDTO dto = null;
        if(StringUtils.isEmpty(numbersReview) || "1".equals(numbersReview)){
            dto = expertScoreTemplateMapper.getRelationTemplate(declareEntId);
        } else if("2".equals(numbersReview)){
            dto = expertScoreTemplateMapper.getSecondRelationTemplate(declareEntId);
        }
        if (dto == null) {
            throw new ValidationException("error", "模板不存在");
        }
        return dto;
    }

}
