package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.uaa.constant.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntMiddleParticipantDetailDTO", description = "DeclareEntMiddleParticipantDetailDTO")
public class DeclareEntMiddleParticipantDetailDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String belongingCompany;

    /**
     * 职务
     */
    @ApiModelProperty("职务")
    private String position;

    /**
     * 在本项目中承担主要工作
     */
    @ApiModelProperty("在本项目中承担主要工作")
    private String work;

    /**
     * 备注(如有人员增减，请说明原因)
     */
    @ApiModelProperty("备注(如有人员增减，请说明原因)")
    private String remarks;

    /**
     * 是否为国创中心双聘人才
     */
    @ApiModelProperty("是否为国创中心双聘人才")
    private Boolean doubleHire;

    public String getDoubleHireStr() {
        return Boolean.TRUE.equals(doubleHire)
                ? Constants.TRUE_TEXT
                : Constants.FALSE_TEXT;
    }
}
