package com.fengyun.udf.uaa.web.rest.declareproject;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declareproject.CreateDeclareEntDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.QueryDeclareProjectDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.QueryMyDeclareProjectDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.UploadContractDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareProjectShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractDetailDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import com.fengyun.udf.uaa.service.declareproject.DeclareProjectService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Api(value = "企业端-申报项目", tags = "企业端-申报项目")
@RestController
@RequestMapping({"/api/declare"})
@Validated
@RequiredArgsConstructor
public class DeclareProjectResource extends BaseResource {

    private final DeclareProjectService declareProjectService;

    private final DeclareEntService declareEntService;


    @GetMapping("/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/list", methodDescription = "企业端-申报项目列表", httpMethod = "GET")
    @ApiOperation(value = "企业端-申报项目列表")
    public ResponseEntity<ReturnResultDTO<DeclareProjectShowDTO>> getDeclareProjectList(Page page,
                                                                                        QueryDeclareProjectDTO
                                                                                                declareStatus) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.list(page, declareStatus));
    }

    @GetMapping("/mylist")
    @CustomBaseException
    @AuditLog(method = "/api/declare/mylist", methodDescription = "企业端-查询我的申报列表", httpMethod = "GET")
    @ApiOperation(value = "企业端-查询我的申报列表")
    public ResponseEntity<ReturnResultDTO<DeclareEntShowDTO>> getMyDeclareProjectList(Page page,
                                                                                      QueryMyDeclareProjectDTO
                                                                                                declareStatus) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.myList(page, declareStatus));
    }

    @GetMapping({"/get/{id}"})
    @ApiOperation(value = "根据id查询申报政策详情")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> get(@PathVariable("id") String id) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getById(id));
    }

    @GetMapping({"/user/{id}"})
    @CustomBaseException
    @AuditLog(method = "/api/declare/user/{id}", methodDescription = "企业 - 申报项目用户详情", httpMethod = "GET")
    @ApiOperation(value = "企业端-申报项目用户详情")
    public ResponseEntity<ReturnResultDTO<DeclareEntDetailDTO>> getDeclareUserId(@PathVariable("id") String id,
                                                                                 String type)
            throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getById(id, type));
    }

    @GetMapping({"/user/childrenitem"})
    @CustomBaseException
    @AuditLog(method = "/api/declare/user/childrenitem", methodDescription = "企业端-申报项目用户子项列表", httpMethod = "GET")
    @ApiOperation(value = "企业端-申报项目用户子项列表")
    public ResponseEntity<ReturnResultDTO<List<String>>> getChildrenitem(String id)
            throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getChildrenItemById(id));
    }

    @GetMapping({"/user/childreninfo"})
    @CustomBaseException
    @AuditLog(method = "/api/declare/user/childreninfo", methodDescription = "企业端-申报项目用户详情子项", httpMethod = "GET")
    @ApiOperation(value = "企业端-申报项目用户详情子项")
    public ResponseEntity<ReturnResultDTO<DeclareEntDetailDTO>> getchildreninfo(String id, String childrenName)
            throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getByIdAndChildren(id,
                childrenName, null));
    }

    @PostMapping({"/user/create"})
    @CustomBaseException
    @AuditLog(method = "/api/declare/user/create", methodDescription = "企业端-提交申报项目详情", httpMethod = "POST")
    @ApiOperation(value = "企业端-提交申报项目详情")
    public ResponseEntity<ReturnResultDTO> create(@RequestBody @Validated CreateDeclareEntDTO
                                                          createDeclareEntDTO)
            throws Exception {
        declareProjectService.createEntDeclare(createDeclareEntDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/user/update"})
    @CustomBaseException
    @AuditLog(method = "/api/declare/user/update", methodDescription = "企业端-提交申报项目详情", httpMethod = "POST")
    @ApiOperation(value = "企业端-更新申报项目详情")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody @Validated CreateDeclareEntDTO
                                                          createDeclareEntDTO)
            throws Exception {
        declareProjectService.updateEntDeclare(createDeclareEntDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }


    @GetMapping({"/enable"})
    @CustomBaseException
    @AuditLog(method = "/api/declare/enable", methodDescription = "企业端-查询申报是否可用", httpMethod = "GET")
    @ApiOperation(value = "企业端-查询申报是否可用")
    public ResponseEntity<ReturnResultDTO<Boolean>> enable()
            throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.enAble());
    }

    @PostMapping("/contract/upload")
    @CustomBaseException
    @AuditLog(method = "/api/declare/contract/upload", methodDescription = "上传合同", httpMethod = "POST")
    @ApiOperation(value = "上传合同")
    public ResponseEntity<ReturnResultDTO<?>> uploadContract(@Validated @RequestBody UploadContractDTO dto) {
        declareEntService.uploadContract(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/contract/detail")
    @CustomBaseException
    @AuditLog(method = "/api/declare/contract/detail", methodDescription = "合同详情", httpMethod = "POST")
    @ApiOperation(value = "合同详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectContractDetailDTO>> contractDetail (
            @Validated @NotBlank(message = "项目id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.contractDetail(id));
    }

}
