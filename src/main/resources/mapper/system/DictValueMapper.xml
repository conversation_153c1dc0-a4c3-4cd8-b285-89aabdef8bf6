<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.system.DictValueMapper">
    <select id="selectSeqByCode" resultType="Integer">
        SELECT SEQ
        FROM t_code_value
        WHERE TYPE_CODE = #{param}
        ORDER BY SEQ DESC
    </select>

    <select id="selectByTypeCode" resultType="com.fengyun.udf.uaa.dto.response.system.DictValueDTO">
        SELECT ID AS id, TYPE_CODE AS typeCode, VALUE AS value, LABEL AS label,
        DESCRIPTION AS description, SEQ AS dictSort, UPDATED_DATE AS updatedDate
        FROM t_code_value
        WHERE TYPE_CODE = #{typeCode}
        AND IS_VIS = 1
        ORDER BY SEQ ASC
    </select>

    <select id="selectByMultipleTypeCode" resultType="com.fengyun.udf.uaa.dto.response.system.DictValueDTO">
        SELECT ID AS id, TYPE_CODE AS typeCode, VALUE AS value, LABEL AS label,
        DESCRIPTION AS description, SEQ AS dictSort, UPDATED_DATE AS updatedDate
        FROM t_code_value
        WHERE IS_VIS = 1
        <if test="types != null and types.size > 0">
            and TYPE_CODE in
            <foreach collection="types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY TYPE_CODE, SEQ ASC
    </select>

</mapper>