package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeclareEntMiddleBasicInfoDetailDTO", description = "DeclareEntMiddleBasicInfoDetailDTO")
public class DeclareEntMiddleBasicInfoDetailDTO extends AbstractDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 课题名称
     */
    @ApiModelProperty("课题名称")
    private String subjectName;

    /**
     * 申报类别
     */
    @ApiModelProperty("申报类别")
    @DictData(target = "declareTypeName", codeType = "DECLARE_TYPE_BAK")
    private String declareType;

    /**
     * 申报类别
     */
    @ApiModelProperty("申报类别")
    private String declareTypeName;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 承担单位名称
     */
    @ApiModelProperty("承担单位名称")
    private String companyName;

    /**
     * 负责人姓名
     */
    @ApiModelProperty("负责人姓名")
    private String principalName;

    /**
     * 负责人电话
     */
    @ApiModelProperty("负责人电话")
    private String principalTel;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    private String contactTel;

    @ApiModelProperty(value = "id")
    private String id;
}
