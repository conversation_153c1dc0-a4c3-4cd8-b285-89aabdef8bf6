package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
*
* </p>
*
* <AUTHOR>
* @since 2022-03-18
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_shenbao")
@Api(hidden = true)
public class Shenbao extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String ProjectId;

    @ApiModelProperty(value = "公司名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "附件内容")
    @TableField("files")
    private String files;

}
