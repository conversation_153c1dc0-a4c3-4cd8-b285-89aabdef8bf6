package com.fengyun.udf.uaa.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.fengyun.udf.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
public class MetaBaseDomainHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        String userId = SecurityUtils.getUserId();
        this.strictInsertFill(metaObject, "createdId", String.class, userId);
        this.strictInsertFill(metaObject, "createdDate", ZonedDateTime.class, ZonedDateTime.now());
        this.strictUpdateFill(metaObject, "updatedId", String.class, userId);
        this.strictUpdateFill(metaObject, "updatedDate", ZonedDateTime.class, ZonedDateTime.now());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        String userId = SecurityUtils.getUserId();
        setFieldValByName("updatedId", userId, metaObject);
        setFieldValByName("updatedDate", ZonedDateTime.now(), metaObject);
    }

}
