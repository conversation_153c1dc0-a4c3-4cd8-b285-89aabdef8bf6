package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
* <p>
* 企业用户注册信息表
* </p>
*
* <AUTHOR>
* @since 2022-03-21
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_en_register")
@Api(hidden = true)
public class EnRegister extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("code")
    private String creditCode;

    @ApiModelProperty(value = "单位名称")
    @TableField("en_name")
    private String name;

    @ApiModelProperty(value = "企业法人")
    @TableField("corporate")
    private String legalRep;

    @ApiModelProperty(value = "证件类型")
    @TableField("certificate_type")
    private String certificateType;

    @ApiModelProperty(value = "证件号码")
    @TableField("certificate_number")
    private String certificateNumber;

    @ApiModelProperty(value = "证件开始有效期")
    @TableField("certificate_start")
    private LocalDate certificateStart;

    @ApiModelProperty(value = "证件结束有效期")
    @TableField("certificate_end")
    private LocalDate certificateEnd;
}
