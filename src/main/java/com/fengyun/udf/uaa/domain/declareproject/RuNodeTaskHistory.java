package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <p>
 * 运行时节点任务
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-19
 */
@TableName("wf_ru_node_task_history")
@Data
@EqualsAndHashCode(callSuper = false)
public class RuNodeTaskHistory extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    /**
    * 主键
    */
    private String id;

    /**
    * 分配用户id
    */
    @TableField(value = "assign_user_id")
    private String assignUserId;

    /**
     * 分配用户名称
     */
    @TableField(value = "assign_user_name")
    private String assignUserName;

    /**
    * 分配用户角色
    */
    @TableField(value = "assign_user_role")
    private String assignUserRole;

    /**
    * 运行时流程id
    */
    @TableField(value = "ru_process_id")
    private String ruProcessId;

    /**
     * 表单信息
     */
    @TableField(value = "form_info")
    private String formInfo;

    /**
    * 状态
    */
    private String status;

    /**
    * 处理时间
    */
    @TableField(value = "process_time")
    private ZonedDateTime processTime;

    /**
    * 处理结果
    */
    @TableField(value = "audit_result")
    private String auditResult;

    /**
    * 处理意见
    */
    @TableField(value = "audit_remarks")
    private String auditRemarks;

    /**
    * 附件
    */
    private String attachments;

    /**
     * 选择的处理事件
     */
    @TableField(value = "select_event")
    private String selectEvent;

    private Boolean deleted;
}
