package com.fengyun.udf.uaa.dto.response.inspection.end;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeclareEntEndAchievementPatentDetailDTO", description = "DeclareEntEndAchievementPatentDetailDTO")
public class DeclareEntEndAchievementPatentDetailDTO extends AbstractDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 专利申请时间
     */
    @ApiModelProperty("专利申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", locale = "zh")
    private LocalDate patentApplicationDate;

    /**
     * 专利类型
     */
    @ApiModelProperty("专利类型")
    private String patentType;

    /**
     * 专利名称
     */
    @ApiModelProperty("专利名称")
    private String patentName;

    /**
     * 专利号
     */
    @ApiModelProperty("专利号")
    private String patentNumber;

    /**
     * 专利权人/申请人
     */
    @ApiModelProperty("专利权人/申请人")
    private String patentApplicant;

    /**
     * 专利状态
     */
    @ApiModelProperty("专利状态")
    @DictData(codeType = "PATENT_STATUS", target = "patentStatusName")
    private String patentStatus;

    /**
     * 专利状态中文
     */
    @ApiModelProperty("专利状态中文")
    private String patentStatusName;
}
