package com.fengyun.udf.uaa.mapper.declareproject;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.declareproject.DeclareEnt;
import com.fengyun.udf.uaa.dto.request.declareproject.*;
import com.fengyun.udf.uaa.dto.request.expertpanel.QueryRelationProjectDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntListDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2021/2/2.
 */
@Repository
public interface DeclareEntMapper extends BaseMapper<DeclareEnt> {

    List<DeclareEntShowDTO> list(Page page,
                                 @Param("role") String role,
                                 @Param("keyWord") String keyWord,
                                 @Param("userId") String userId,
                                 @Param("declareId") String declareId,
                                 @Param("declareTypeList") String[] declareTypeList);


    List<DeclareEntShowDTO> listHistroy(Page page,
                                        @Param("keyWord") String keyWord,
                                        @Param("declareTypeList") String[] declareTypeList,
                                        @Param("userId") String userId);

    List<DeclareEntShowDTO> listFirstPass(Page page, @Param("keyWord") String keyWord,
                                        @Param("declareType") String declareType, @Param("userId") String userId,@Param("isUploadedPPTVideo") String isUploadedPPTVideo);

    List<String> listSecondRelation(Page page,@Param("type") String type, @Param("userId") String userId);

    List<String> listFirstRelation(@Param("userId") String userId);

    Integer countList(@Param("role") String role, @Param("townShips") List<String> townShips, @Param("offices") List<String>
            offices, @Param("declareId") String declareId);

    /**
     * 企业申报展示
     * @param page 分页参数
     * @param dto 筛选条件
     * @return 企业申报列表
     */
    List<DeclareEntShowDTO> page(Page<DeclareEntShowDTO> page, @Param("params") QueryMyDeclareProjectDTO dto);

    List<DeclareEntExpertShowDTO> pageRelation(Page page, @Param("params") QueryRelationProjectDTO dto);

    List<DeclareEntExpertShowDTO> pageRelation2(Page page, @Param("params") QueryRelationProjectDTO dto);

    boolean checkDeclareEntIsCreated(@Param("projectId") String projectId, @Param("userId") String userId);

    List<DeclareEntShowDTO> scorePage(Page page,@Param("keyWord") String keyWord, @Param("userId") String userId,
                                 @Param("declareType") String declareType);

    List<DeclareEntShowDTO> historyScorePage(Page page,@Param("keyWord") String keyWord, @Param("userId") String userId,
                                        @Param("declareType") String declareType);

    List<DeclareEntShowDTO> draftScorePage(Page page,@Param("keyWord") String keyWord, @Param("userId") String userId,
                                             @Param("declareType") String declareType);

    List<DeclareEntExpertShowDTO> summaryScorePage(Page page,@Param("keyWord") String keyWord
            , @Param("declareTypeList") List<String> declareTypeList, @Param("responsibleName") String responsibleName);

    List<DeclareEntExpertShowDTO> secondSummaryScorePage(Page page,@Param("keyWord") String keyWord
            , @Param("declareTypeList") List<String> declareTypeList, @Param("responsibleName") String responsibleName);

    int getExpertGroupById(@Param("id") String id, @Param("expertPanelId") String expertPanelId);

    int getSecondExpertGroupById(@Param("id") String id, @Param("expertPanelId") String expertPanelId);

    List<RecordDTO> getReviewPassRecords(@Param("subjectName") String subjectName, @Param("responsibleName") String responsibleName, @Param("id") String id);

    List<DeclareEnt>  getDeclareEntIdByProjectId(@Param("projectID") String projectID);

    String getMaxProjectNo(@Param("prefix") String prefix);

    List<DeclareEntShowDTO> secondScorePage(Page page,@Param("keyWord") String keyWord, @Param("userId") String userId,
                                      @Param("declareType") String declareType);

    List<DeclareEntShowDTO> secondHistoryScorePage(Page page,@Param("keyWord") String keyWord, @Param("userId") String userId,
                                             @Param("declareType") String declareType);

    List<DeclareEntShowDTO> secondDraftScorePage(Page page,@Param("keyWord") String keyWord, @Param("userId") String userId,
                                           @Param("declareType") String declareType);

    Integer getIsRelationFirst(@Param("userId") String userId);

    List<ImportRecordDTO> getUploadedPPtAndVideoProject(@Param("declareType")String declareType,@Param("isUploadedPPTVideo")String isUploadedPPTVideo);

    /**
     * 根据父项目Id分页查子项目
     * @param page 分页参数
     * @param dto 查询条件
     * @return 子项目列表
     */
    List<DeclareEntListDTO> pageByDeclareId(Page page, @Param("condition") QueryDeclareEntDTO dto);

    /**
     * 根据父项目Id查子项目
     * @param declareId 申报信息id
     * @param projectStatus 项目状态
     * @param projectProgress 项目进程
     * @return 子项目列表
     */
    List<DeclareEntListDTO> getByDeclareIdAndStatus(
            @Param("declareId") String declareId,
            @Param("projectStatus") String projectStatus,
            @Param("projectProgress")  String projectProgress);

}
