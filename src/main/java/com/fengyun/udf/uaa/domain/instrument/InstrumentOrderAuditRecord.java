package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仪器预约审核记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
@TableName("in_instrument_order_audit_record")
public class InstrumentOrderAuditRecord {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 预约id
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 审核节点
     */
    @TableField("node")
    private String node;

    /**
     * 审核操作
     */
    @TableField("status")
    private String status;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核人
     */
    @TableField("audit_user")
    private String auditUser;

    /**
     * 审核意见
     */
    @TableField("remark")
    private String remark;


}
