package com.fengyun.udf.uaa.domain.expertpanel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

@TableName("t_expert_score_template")
@Data
public class ExpertScoreTemplate {

    @TableId("id")
    private String id;

    @TableField("template_name")
    private String templateName;

    @TableField("template_config")
    private String templateConfig;

    @TableField("remark")
    private String remark;

    @TableField("status")
    private String status;

    @TableField("created_id")
    private String createdId;

    @TableField("updated_id")
    private String updatedId;

    @TableField("created_date")
    private ZonedDateTime createdDate;

    @TableField("updated_date")
    private ZonedDateTime updatedDate;

    @TableField("number")
    private String number;//第几次审核模板

}