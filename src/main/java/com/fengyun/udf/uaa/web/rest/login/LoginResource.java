package com.fengyun.udf.uaa.web.rest.login;

import cn.hutool.core.codec.Base64Decoder;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.cache.redis.service.RedisClient;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.RedisKeyDTO;
import com.fengyun.udf.dto.RedisObjectDTO;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.token.PhoneAuthenticationToken;
import com.fengyun.udf.security.token.WebAuthenticationToken;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.dto.request.login.PhoneLoginDTO;
import com.fengyun.udf.uaa.dto.request.login.UserNameLoginDTO;
import com.fengyun.udf.uaa.dto.request.register.SignInDTO;
import com.fengyun.udf.uaa.service.login.LoginService;
import com.fengyun.udf.uaa.service.register.RegisterService;
import com.fengyun.udf.util.Validators;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import static com.fengyun.udf.uaa.constant.Constants.SEPARATOR_COLON;

@Slf4j
@RestController
@RequestMapping("/")
@Api(value = "登录系统", tags = "登录系统")
public class LoginResource extends BaseResource {

    private AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new
            WebAuthenticationDetailsSource();

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private LoginService loginService;

    @Autowired
    private RegisterService registerService;

    public static final String SMS_CAPTCHA_CODE = "smsCaptchaCode";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RedisClient redisClient;

    @PostMapping("/login")
    @CustomBaseException
    @ApiOperation(value = "登录系统", notes = "登录系统")
    @AuditLog(method = "/login", methodDescription = "用户名密码登录系统", httpMethod = "POST", storageMode = {"file", "mysql"})
    public ResponseEntity<ReturnResultDTO> login(HttpServletRequest request, UserNameLoginDTO loginDTO
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        String username = loginDTO.getUsername();
        String password = loginDTO.getPassword();
        password = Base64Decoder.decodeStr(password);
        log.info("password" + password);
        if (!loginDTO.getLoginType().equals("management") && username.length() != 11) {
            throw new ValidationException("error", "请输入正确的手机号");
        }

        if (StringUtils.isBlank(username)) {
            throw new ValidationException("username", "用户名不能为空");
        }
        if (StringUtils.isBlank(password)) {
            throw new ValidationException("password", "密码不能为空");
        }
        //图形验证码验证
        if (StringUtils.isNotBlank(loginDTO.getCaptchaCode()) && StringUtils.isNotBlank(loginDTO.getCaptchaCodeId())) {
            RedisObjectDTO redisObjectDTO = new RedisObjectDTO("", "captcha", loginDTO.getCaptchaCodeId());
            String captchaCode_cache = redisClient.getObject(redisObjectDTO);
            if (Validators.fieldBlank(captchaCode_cache)) {
                throw new ValidationException("error", "验证码不存在");
            } else {
                //验证码比对
                if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(loginDTO.getCaptchaCode(),
                        captchaCode_cache)) {
                    throw new ValidationException("error", "验证码不正确");
                }
                //清除缓存
                redisClient.deleteKey(new RedisKeyDTO("", "captcha", loginDTO.getCaptchaCodeId()));
            }
        }
        try {
            WebAuthenticationToken authRequest = new WebAuthenticationToken(username, password, "", loginDTO
                    .getLoginType());
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            OAuth2AccessToken token = loginService.getAccessToken(authentication, clientType);

            if (loginService.checkUserIsEn(SecurityUtils.getUser(authentication))) {
                throw new ValidationException("error", "请使用企业用户通道登录");
            }
            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), token);
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "请求头错误");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }


    @PostMapping("/login-enterprise")
    @CustomBaseException
    @ApiOperation(value = "企业用户登录系统", notes = "企业用户登录系统")
    @AuditLog(method = "/login-enterprise", methodDescription = "企业用户名密码登录系统", httpMethod = "POST", storageMode =
            {"file", "mysql"})
    public ResponseEntity<ReturnResultDTO> loginByEnterprise(HttpServletRequest request, UserNameLoginDTO loginDTO
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        String username = loginDTO.getUsername();
        String password = loginDTO.getPassword();
        password = Base64Decoder.decodeStr(password);
        log.info("password" + password);
        if (username.length() <= 17) {
            throw new ValidationException("error", "请输入正确的统一社会信用代码");
        }

        if (StringUtils.isBlank(username)) {
            throw new ValidationException("username", "用户名不能为空");
        }
        if (StringUtils.isBlank(password)) {
            throw new ValidationException("password", "密码不能为空");
        }
        try {
            WebAuthenticationToken authRequest = new WebAuthenticationToken(username, password, "", loginDTO
                    .getLoginType());
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            OAuth2AccessToken token = loginService.getAccessToken(authentication, clientType);

            loginService.checkEnUserCanLogin(SecurityUtils.getUser(authentication));

            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), token);
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "请求头错误");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }

    @PostMapping("/login-noAuth")
    @CustomBaseException
    @ApiOperation(value = "测试登录系统", notes = "测试登录系统")
    @AuditLog(method = "/login-noAuth", methodDescription = "测试-用户名密码登录系统", httpMethod = "POST", storageMode =
            {"file", "mysql"})
    public ResponseEntity<ReturnResultDTO> loginNoAuth(HttpServletRequest request, UserNameLoginDTO loginDTO
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        String username = loginDTO.getUsername();
        String password = loginDTO.getPassword();

        if (StringUtils.isBlank(username)) {
            throw new ValidationException("username", "用户名不能为空");
        }
        if (StringUtils.isBlank(password)) {
            throw new ValidationException("password", "密码不能为空");
        }
        //图形验证码验证
        /*if (StringUtils.isNotBlank(loginDTO.getCaptchaCode()) && StringUtils.isNotBlank(loginDTO.getCaptchaCodeId()
        )) {
            RedisObjectDTO redisObjectDTO = new RedisObjectDTO("", "captcha", loginDTO.getCaptchaCodeId());
            String captchaCode_cache = redisClient.getObject(redisObjectDTO);
            if (Validators.fieldBlank(captchaCode_cache)) {
                throw new ValidationException("error", "验证码不存在");
            } else {
                //验证码比对
                if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(loginDTO.getCaptchaCode(),
                captchaCode_cache)) {
                    throw new ValidationException("error", "验证码不正确");
                }
                //清除缓存
                redisClient.deleteKey(new RedisKeyDTO("", "captcha", loginDTO.getCaptchaCodeId()));
            }
        }*/
        try {
            WebAuthenticationToken authRequest = new WebAuthenticationToken(username, password, "", loginDTO
                    .getLoginType());
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            OAuth2AccessToken token = loginService.getAccessToken(authentication, clientType);

            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), token);
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "请求头错误");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }

    @PostMapping("/login-phone")
    @CustomBaseException
    @ApiOperation(value = "手机登录系统", notes = "手机登录系统")
    @AuditLog(method = "/login-phone", methodDescription = "手机登录系统", httpMethod = "POST", storageMode = {"file",
            "mysql"})
    public ResponseEntity<ReturnResultDTO> loginPhone(HttpServletRequest request, PhoneLoginDTO loginDTO
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        String tel = loginDTO.getTel();
        String verifyCode = loginDTO.getVerifyCode();
        if (StringUtils.isBlank(tel)) {
            throw new ValidationException("tel", "手机号码不能为空");
        }
        if (StringUtils.isBlank(verifyCode)) {
            throw new ValidationException("verifyCode", "手机验证码不能为空");
        }
        //从缓存中获取验证码
        String redis_smsCaptchaCode = redisTemplate.opsForValue().get(SMS_CAPTCHA_CODE + SEPARATOR_COLON + tel);
        if (!StringUtils.equals(verifyCode, redis_smsCaptchaCode)) {
            throw new ValidationException("verifyCode", "手机验证码不正确");
        }

        if (!loginService.isExistByTel(loginDTO.getTel())) {
            SignInDTO signInDTO = new SignInDTO();
            signInDTO.setTel(loginDTO.getTel());
            signInDTO.setLoginName(loginDTO.getTel());
            signInDTO.setPassword(registerService.getDefaultPassword());
            signInDTO.setNickName(loginDTO.getTel());
            signInDTO.setSignupChannel(registerService.getUserSignUpChannel(request));
            registerService.signInUserForPhone(signInDTO);
        }

        try {
            PhoneAuthenticationToken authRequest = new PhoneAuthenticationToken(tel, verifyCode, "", loginDTO
                    .getLoginType());
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            OAuth2AccessToken token = loginService.getAccessToken(authentication, clientType);

            //删除缓存中的验证码
            redisTemplate.delete(SMS_CAPTCHA_CODE + SEPARATOR_COLON + tel);

            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), token);
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "请求头错误");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }

    @PostMapping("/login-register")
    @AuditLog(method = "/api/login-register", methodDescription = "手机号密码注册登录（企业不用审核）", httpMethod = "POST")
    @ApiOperation(value = "手机号密码注册登录（企业不用审核）")
    public ResponseEntity<ReturnResultDTO> registerUser(@RequestBody @Validated SignInDTO dto, HttpServletRequest
            request
            , @RequestHeader(value = "clientType", required = false) String clientType) {

        dto.setSignupChannel(registerService.getUserSignUpChannel(request));
        String[] ids = registerService.signInUser(dto, true);

        UserNameLoginDTO loginDTO = new UserNameLoginDTO();
        loginDTO.setUsername(dto.getTel());
        loginDTO.setPassword(dto.getPassword());
        loginDTO.setLoginType(UserType.member.name());
        return login(request, loginDTO, clientType);
    }
}
