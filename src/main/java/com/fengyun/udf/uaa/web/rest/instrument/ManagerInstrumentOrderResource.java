package com.fengyun.udf.uaa.web.rest.instrument;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.instrument.*;
import com.fengyun.udf.uaa.dto.response.instrument.*;
import com.fengyun.udf.uaa.service.instrument.InstrumentOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-30
 */

@RestController
@RequestMapping("/api/manager/instrument-order")
@Api(value = "管理端-仪器预约", tags = "管理端-仪器预约")
@Validated
@Slf4j
@AllArgsConstructor
@PreAuthorize("@pms.hasPermission('management')")
public class ManagerInstrumentOrderResource extends BaseResource {
    private final InstrumentOrderService instrumentOrderService;

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/view", methodDescription = "仪器预约详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "仪器预约详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderDetailDTO>> detail(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.detail(id));
    }

    @GetMapping("/audit/list")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/audit/list", methodDescription = "仪器预约待审核列表", httpMethod = "GET")
    @ApiOperation(value = "仪器预约待审核列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderListDTO>>> auditPage(Page page, InstrumentOrderQueryDTO dto) {
        dto.setStatus(Constants.INSTRUMENT_ORDER_STATUS.pre.name());
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.page(page, dto));
    }

    @PostMapping("/audit")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/audit", methodDescription = "仪器预约审核", httpMethod = "POST")
    @ApiOperation(value = "仪器预约审核")
    public ResponseEntity<ReturnResultDTO> audit(@RequestBody @Validated InstrumentOrderAuditDTO dto) {
        instrumentOrderService.audit(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/summary/list")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/summary/list", methodDescription = "仪器预约汇总列表", httpMethod = "GET")
    @ApiOperation(value = "仪器预约汇总列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderListDTO>>> summaryPage(Page page, InstrumentOrderQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.page(page, dto));
    }

    @GetMapping("/summary/export")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/summary/export", methodDescription = "仪器预约汇总导出", httpMethod = "GET")
    @ApiOperation(value = "仪器预约汇总导出")
    public void export(InstrumentOrderQueryDTO dto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        dto.setExport("YES");
        List<InstrumentOrderListDTO> list = instrumentOrderService.export(dto);

        ExcelWriter writer = ExcelUtil.getWriter(true);

        writer.addHeaderAlias("index", "序号");
        writer.addHeaderAlias("enName", "企业名称");
        writer.addHeaderAlias("instrumentName", "设备名称");
        writer.addHeaderAlias("contact", "预约联系人");
        writer.addHeaderAlias("contactTel", "预约联系方式");
        writer.addHeaderAlias("model", "型号");
        writer.addHeaderAlias("skilledUseStr", "是否需要培训");
        writer.addHeaderAlias("orderDateStr", "预约时间");
        writer.addHeaderAlias("amount", "核准总金额");
        writer.addHeaderAlias("statusStr", "审核状态");
        writer.addHeaderAlias("workStatusStr", "工单状态");
        writer.addHeaderAlias("settlementStatusStr", "结算状态");
        writer.addHeaderAlias("leaseTypeStr", "租赁类型");
        writer.addHeaderAlias("createdDate", "提交时间");


        writer.setOnlyAlias(true);
        writer.write(list, true);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");

        String fileName = "预约记录导出表格.xlsx";

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.addHeader("filename", URLEncoder.encode(fileName, "UTF-8"));
        response.addHeader("Access-Control-Expose-Headers", "filename");

        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);

        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    @PostMapping("/evaluate")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/evaluate", methodDescription = "仪器预约评价", httpMethod = "POST")
    @ApiOperation(value = "仪器预约评价")
    public ResponseEntity<ReturnResultDTO> evaluate(@RequestBody @Validated InstrumentOrderEvaluateDTO dto) {
        instrumentOrderService.evaluate(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/receipt-pdf")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/receipt-pdf", methodDescription = "生成收据", httpMethod = "POST")
    @ApiOperation(value = "生成收据")
    public ResponseEntity<ReturnResultDTO<AttachmentDTO>> receiptPdf(@RequestBody @Validated InstrumentOrderReceiptDTO dto) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.generateReceiptPdf(dto));
    }

    @PostMapping("/receipt")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/receipt", methodDescription = "保存收据", httpMethod = "POST")
    @ApiOperation(value = "保存收据")
    public ResponseEntity<ReturnResultDTO> receipt(@RequestBody @Validated InstrumentOrderReceiptDTO dto) throws Exception {
        instrumentOrderService.receipt(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/receipt")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/receipt", methodDescription = "查看开户及收据信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查看开户及收据信息")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderReceiptDetailDTO>> getReceipt(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.getReceipt(id));
    }

    @PostMapping("/edit-work")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/edit-work", methodDescription = "编辑工单", httpMethod = "POST")
    @ApiOperation(value = "编辑工单")
    public ResponseEntity<ReturnResultDTO<AttachmentDTO>> editWork(@RequestBody @Validated InstrumentOrderEditWorkDTO dto) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.editWork(dto));
    }

    @PostMapping("/final-work")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/final-work", methodDescription = "最终工单", httpMethod = "POST")
    @ApiOperation(value = "最终工单")
    public ResponseEntity<ReturnResultDTO> finalWork(@RequestBody @Validated InstrumentOrderFinalWorkDTO dto) {
        instrumentOrderService.finalWork(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/final-work-edit")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/final-work-edit", methodDescription = "更新材料", httpMethod = "POST")
    @ApiOperation(value = "更新材料")
    public ResponseEntity<ReturnResultDTO> finalWorkEdit(@RequestBody @Validated InstrumentOrderFinalWorkEditDTO dto) {
        instrumentOrderService.finalWorkEdit(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/settlement")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/settlement", methodDescription = "结算", httpMethod = "POST")
    @ApiOperation(value = "结算")
    public ResponseEntity<ReturnResultDTO> settlement(@NotBlank(message = "id不能为空") String id) {
        instrumentOrderService.settlement(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/settlement-cancel")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/settlement-cancel", methodDescription = "取消结算", httpMethod = "POST")
    @ApiOperation(value = "取消结算")
    public ResponseEntity<ReturnResultDTO> cancelSettlement(@NotBlank(message = "id不能为空") String id) {
        instrumentOrderService.cancelSettlement(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/work")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/work", methodDescription = "查看工单信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查看工单信息")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderWorkDetailDTO>> getWork(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.getManagerWork(id));
    }

    @PostMapping("/audit-back")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/audit-back", methodDescription = "强制退回", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "强制退回")
    public ResponseEntity<ReturnResultDTO> auditBack(@RequestBody @Validated InstrumentOrderAuditDTO dto) {
        instrumentOrderService.auditBack(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/invoice")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order/invoice", methodDescription = "发票", httpMethod = "POST")
    @ApiOperation(value = "发票")
    public ResponseEntity<ReturnResultDTO> invoice(@RequestBody @Validated InstrumentOrderInvoiceDTO dto) {
        instrumentOrderService.invoice(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

}
