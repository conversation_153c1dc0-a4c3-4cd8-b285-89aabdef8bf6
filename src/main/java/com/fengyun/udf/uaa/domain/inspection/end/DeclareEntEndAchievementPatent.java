package com.fengyun.udf.uaa.domain.inspection.end;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "declare_ent_end_achievement_patent")
@TableName(value = "declare_ent_end_achievement_patent", autoResultMap = true)
public class DeclareEntEndAchievementPatent extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @TableField("project_id")
    private String projectId;

    /**
     * 专利申请时间
     */
    @ApiModelProperty("专利申请时间")
    @TableField("patent_application_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", locale = "zh")
    private LocalDate patentApplicationDate;

    /**
     * 专利类型
     */
    @ApiModelProperty("专利类型")
    @TableField("patent_type")
    private String patentType;

    /**
     * 专利名称
     */
    @ApiModelProperty("专利名称")
    @TableField("patent_name")
    private String patentName;

    /**
     * 专利号
     */
    @ApiModelProperty("专利号")
    @TableField("patent_number")
    private String patentNumber;

    /**
     * 专利权人/申请人
     */
    @ApiModelProperty("专利权人/申请人")
    @TableField("patent_applicant")
    private String patentApplicant;

    /**
     * 专利状态
     */
    @ApiModelProperty("专利状态")
    @TableField("patent_status")
    private String patentStatus;
}
