<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.declareproject.ProjectContractMapper">

    <select id="detail" resultType="com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractDetailDTO">
        SELECT
            pc.id AS id,
            pc.project_id AS projectId,
            pc.contract AS contractStr,
            pc.audit_status AS auditStatus,
            pc.audit_remark AS auditRemark,
            pc.submit_date AS submitDate,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.created_date AS createdDate,
            de.declare_type AS declareType,
            de.created_id AS createdId,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM
            t_project_contract pc
        LEFT JOIN
            t_declare_ent de ON de.id = pc.project_id
        LEFT JOIN
            flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN
            t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN
            flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        WHERE
            pc.project_id = #{id}
    </select>

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.declareproject.ProjectContractListDTO">
        SELECT
            pc.project_id AS id,
            pc.audit_status AS auditStatus,
            pc.submit_date AS submitDate,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.declare_type AS declareType,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM
            t_project_contract pc
        LEFT JOIN
            t_declare_ent de ON de.id = pc.project_id
        LEFT JOIN
            flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN
            t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN
            flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        <where>
            pc.audit_status != '0'
            <if test="condition.declareName != null and condition.declareName != ''">
                AND dp.declare_name = #{condition.declareName}
            </if>
            <if test="condition.declareTypeList != null and condition.declareTypeList.size > 0">
                AND de.declare_type IN
                <foreach collection="condition.declareTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.subjectName != null and condition.subjectName != ''">
                AND con.subject_name LIKE CONCAT('%',#{condition.subjectName},'%')
            </if>
            <if test="condition.responsibleName != null and condition.responsibleName != ''">
                AND res.responsible_name LIKE CONCAT('%',#{condition.responsibleName},'%')
            </if>
            <if test="condition.auditStatus != null and condition.auditStatus != ''">
                AND pc.audit_status = #{condition.auditStatus}
            </if>
        </where>
        ORDER BY pc.audit_status ASC, pc.submit_date DESC
    </select>
</mapper>
