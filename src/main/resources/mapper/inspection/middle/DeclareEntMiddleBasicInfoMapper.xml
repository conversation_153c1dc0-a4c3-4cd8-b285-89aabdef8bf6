<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.inspection.middle.DeclareEntMiddleBasicInfoMapper">

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.inspection.middle.DeclareEntMiddleBasicInfoListDTO">
        SELECT
            dembi.project_id AS projectId,
            dembi.`status` AS `status`,
            dembi.CREATED_DATE AS createdDate,
            dembi.UPDATED_DATE AS updatedDate,
            dembi.declare_type AS declareType,
            dembi.project_no AS projectNo,
            dembi.subject_name AS subjectName,
            dembi.company_name AS companyName,
            dembi.principal_name AS principalName,
            dembi.principal_tel AS principalTel,
            tde.project_status AS projectStatus,
            tde.declare_id AS declareId
        FROM declare_ent_middle_basic_info dembi
        LEFT JOIN t_declare_ent tde ON tde.id = dembi.project_id
         <where>
             <if test="condition.keyword != null and condition.keyword != ''">
                 AND (
                    dembi.subject_name LIKE CONCAT('%', #{condition.keyword}, '%')
                    OR dembi.company_name LIKE CONCAT('%', #{condition.keyword}, '%')
                    OR dembi.principal_name LIKE CONCAT('%', #{condition.keyword}, '%')
                 )
             </if>
             <if test="condition.declareType != null and condition.declareType != ''">
                 AND FIND_IN_SET(tde.declare_type, #{condition.declareType}) > 0
             </if>
            <choose>
                <when test="condition.history != null and condition.history">
                     AND EXISTS (SELECT 1 FROM wf_ru_node_task_history wrnth WHERE wrnth.ru_process_id = dembi.id)
                </when>
                <otherwise>
                    AND dembi.`status` = 1
                </otherwise>
            </choose>
         </where>
    </select>

</mapper>
