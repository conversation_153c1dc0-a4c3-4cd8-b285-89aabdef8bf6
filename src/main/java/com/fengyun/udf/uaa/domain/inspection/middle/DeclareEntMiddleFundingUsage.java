package com.fengyun.udf.uaa.domain.inspection.middle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "支出预算")
@TableName(value = "declare_ent_middle_funding_usage", autoResultMap = true)
public class DeclareEntMiddleFundingUsage extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 设备购置-预算
     */
    @ApiModelProperty("设备购置-预算")
    @TableField("equipment_purchase_budget")
    private BigDecimal equipmentPurchaseBudget;

    /**
     * 设备购置-国创中心支持
     */
    @ApiModelProperty("设备购置-国创中心支持")
    @TableField("equipment_purchase_support")
    private BigDecimal equipmentPurchaseSupport;

    /**
     * 设备购置-实际支出经费
     */
    @ApiModelProperty("设备购置-实际支出经费")
    @TableField("equipment_purchase_actual")
    private BigDecimal equipmentPurchaseActual;

    /**
     * 设备购置-国创中心支持-实际
     */
    @ApiModelProperty("设备购置-国创中心支持-实际")
    @TableField("equipment_purchase_support_actual")
    private BigDecimal equipmentPurchaseSupportActual;

    /**
     * 设备试制-预算
     */
    @ApiModelProperty("设备试制-预算")
    @TableField("equipment_trial_budget")
    private BigDecimal equipmentTrialBudget;

    /**
     * 设备试制-国创中心支持
     */
    @ApiModelProperty("设备试制-国创中心支持")
    @TableField("equipment_trial_support")
    private BigDecimal equipmentTrialSupport;

    /**
     * 设备试制-实际支出经费
     */
    @ApiModelProperty("设备试制-实际支出经费")
    @TableField("equipment_trial_actual")
    private BigDecimal equipmentTrialActual;

    /**
     * 设备试制-国创中心支持-实际
     */
    @ApiModelProperty("设备试制-国创中心支持-实际")
    @TableField("equipment_trial_support_actual")
    private BigDecimal equipmentTrialSupportActual;

    /**
     * 设备改造-预算
     */
    @ApiModelProperty("设备改造-预算")
    @TableField("equipment_modification_budget")
    private BigDecimal equipmentModificationBudget;

    /**
     * 设备改造-国创中心支持
     */
    @ApiModelProperty("设备改造-国创中心支持")
    @TableField("equipment_modification_support")
    private BigDecimal equipmentModificationSupport;

    /**
     * 设备改造-实际支出经费
     */
    @ApiModelProperty("设备改造-实际支出经费")
    @TableField("equipment_modification_actual")
    private BigDecimal equipmentModificationActual;

    /**
     * 设备改造-国创中心支持-实际
     */
    @ApiModelProperty("设备改造-国创中心支持-实际")
    @TableField("equipment_modification_support_actual")
    private BigDecimal equipmentModificationSupportActual;

    /**
     * 材料费-预算
     */
    @ApiModelProperty("材料费-预算")
    @TableField("material_fee_budget")
    private BigDecimal materialFeeBudget;

    /**
     * 材料费-国创中心支持
     */
    @ApiModelProperty("材料费-国创中心支持")
    @TableField("material_fee_support")
    private BigDecimal materialFeeSupport;

    /**
     * 材料费-实际支出经费
     */
    @ApiModelProperty("材料费-实际支出经费")
    @TableField("material_fee_actual")
    private BigDecimal materialFeeActual;

    /**
     * 材料费-国创中心支持-实际
     */
    @ApiModelProperty("材料费-国创中心支持-实际")
    @TableField("material_fee_support_actual")
    private BigDecimal materialFeeSupportActual;

    /**
     * 差旅费-预算
     */
    @ApiModelProperty("差旅费-预算")
    @TableField("travel_expenses_budget")
    private BigDecimal travelExpensesBudget;

    /**
     * 差旅费-国创中心支持
     */
    @ApiModelProperty("差旅费-国创中心支持")
    @TableField("travel_expenses_support")
    private BigDecimal travelExpensesSupport;

    /**
     * 差旅费-实际支出经费
     */
    @ApiModelProperty("差旅费-实际支出经费")
    @TableField("travel_expenses_actual")
    private BigDecimal travelExpensesActual;

    /**
     * 差旅费-国创中心支持-实际
     */
    @ApiModelProperty("差旅费-国创中心支持-实际")
    @TableField("travel_expenses_support_actual")
    private BigDecimal travelExpensesSupportActual;

    /**
     * 劳务费-预算
     */
    @ApiModelProperty("劳务费-预算")
    @TableField("labor_fee_budget")
    private BigDecimal laborFeeBudget;

    /**
     * 劳务费-国创中心支持
     */
    @ApiModelProperty("劳务费-国创中心支持")
    @TableField("labor_fee_support")
    private BigDecimal laborFeeSupport;

    /**
     * 劳务费-实际支出经费
     */
    @ApiModelProperty("劳务费-实际支出经费")
    @TableField("labor_fee_actual")
    private BigDecimal laborFeeActual;

    /**
     * 劳务费-国创中心支持-实际
     */
    @ApiModelProperty("劳务费-国创中心支持-实际")
    @TableField("labor_fee_support_actual")
    private BigDecimal laborFeeSupportActual;

    /**
     * 其他支出-预算
     */
    @ApiModelProperty("其他支出-预算")
    @TableField("other_budget")
    private BigDecimal otherBudget;

    /**
     * 其他支出-国创中心支持
     */
    @ApiModelProperty("其他支出-国创中心支持")
    @TableField("other_support")
    private BigDecimal otherSupport;

    /**
     * 其他支出-实际支出经费
     */
    @ApiModelProperty("其他支出-实际支出经费")
    @TableField("other_actual")
    private BigDecimal otherActual;

    /**
     * 其他支出-国创中心支持-实际
     */
    @ApiModelProperty("其他支出-国创中心支持-实际")
    @TableField("other_support_actual")
    private BigDecimal otherSupportActual;

    /**
     * 绩效支出-预算
     */
    @ApiModelProperty("绩效支出-预算")
    @TableField("performance_pay_budget")
    private BigDecimal performancePayBudget;

    /**
     * 绩效支出-国创中心支持
     */
    @ApiModelProperty("绩效支出-国创中心支持")
    @TableField("performance_pay_support")
    private BigDecimal performancePaySupport;

    /**
     * 绩效支出-实际支出经费
     */
    @ApiModelProperty("绩效支出-实际支出经费")
    @TableField("performance_pay_actual")
    private BigDecimal performancePayActual;

    /**
     * 绩效支出-国创中心支持-实际
     */
    @ApiModelProperty("绩效支出-国创中心支持-实际")
    @TableField("performance_pay_support_actual")
    private BigDecimal performancePaySupportActual;

    /**
     * 管理费-预算
     */
    @ApiModelProperty("管理费-预算")
    @TableField("manage_pay_budget")
    private BigDecimal managePayBudget;

    /**
     * 管理费-国创中心支持
     */
    @ApiModelProperty("管理费-国创中心支持")
    @TableField("manage_pay_support")
    private BigDecimal managePaySupport;

    /**
     * 管理费-支出实际支出经费
     */
    @ApiModelProperty("管理费-支出实际支出经费")
    @TableField("manage_pay_actual")
    private BigDecimal managePayActual;

    /**
     * 管理费-国创中心支持-实际
     */
    @ApiModelProperty("管理费-国创中心支持-实际")
    @TableField("manage_pay_support_actual")
    private BigDecimal managePaySupportActual;

}
