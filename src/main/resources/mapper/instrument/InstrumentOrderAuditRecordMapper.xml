<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderAuditRecordMapper">

    <select id="selectAuditUser" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderAuditRecordDTO">
        select audit_time,audit_user from in_instrument_order_audit_record
        where order_id = #{orderId} and status = 'pass' order by audit_time desc limit 1
    </select>

</mapper>
