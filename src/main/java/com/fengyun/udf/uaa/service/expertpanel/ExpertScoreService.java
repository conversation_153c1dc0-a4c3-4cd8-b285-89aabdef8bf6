package com.fengyun.udf.uaa.service.expertpanel;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.interceptor.CodeDecorator;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertScoreHistory;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.declareproject.ImportRecordDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertScoreCreateDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreDTO;
import com.fengyun.udf.uaa.mapper.declareproject.DeclareEntMapper;
import com.fengyun.udf.uaa.mapper.expertpanel.ExpertScoreHistoryMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.util.BeanUtils;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.ZonedDateTime;
import java.util.*;

@Service
@Transactional
public class ExpertScoreService extends BaseService {

    @Autowired
    private DeclareEntMapper declareEntMapper;

    @Autowired
    private ExpertScoreHistoryMapper expertScoreHistoryMapper;

    /**
     * 列表查询
     * @param page
     * @return
     */
    public Page<DeclareEntShowDTO> scorePage(Page<DeclareEntShowDTO> page, String keyWord, String declareType) {
        String userId = SecurityUtils.getUserId();
        List<DeclareEntShowDTO> list = new ArrayList<>();
       List<String> firstEntids = declareEntMapper.listFirstRelation(userId);
       if(firstEntids != null && firstEntids.size()>0){
           list = declareEntMapper.scorePage(page, keyWord, userId, declareType);
       }else {
           list = declareEntMapper.secondScorePage(page, keyWord, userId, declareType);
       }

        if (CollectionUtils.isEmpty(list)) {
            return page;
        }
        list.forEach(a->{
            if(StringUtils.isEmpty(a.getSecondExpertPanelId())){
                a.setNumbersReview("1");
            }else {
                a.setNumbersReview("2");
            }
            if (StringUtils.isNotEmpty(a.getCloseDebatePPT())) {
                a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            }
            if (StringUtils.isNotEmpty(a.getCloseDebateVideo())) {
                a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            }
        });
        page.setRecords(list);
        return page;
    }

    public Page<DeclareEntShowDTO> historyScorePage(Page<DeclareEntShowDTO> page, String keyWord, String declareType) {
        String userId = SecurityUtils.getUserId();
        List<DeclareEntShowDTO> list = declareEntMapper.historyScorePage(page, keyWord, userId, declareType);
        if (CollectionUtils.isEmpty(list)) {
            return page;
        }
        list.forEach(a->{
            if(StringUtils.isEmpty(a.getSecondExpertPanelId())){
                a.setNumbersReview("1");
            }else {
                a.setNumbersReview("2");
            }
            if (StringUtils.isNotEmpty(a.getCloseDebatePPT())) {
                a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            }
            if (StringUtils.isNotEmpty(a.getCloseDebateVideo())) {
                a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            }
        });
        page.setRecords(list);
        return page;
    }

    public Page<DeclareEntShowDTO> draftScorePage(Page<DeclareEntShowDTO> page, String keyWord, String declareType) {
        String userId = SecurityUtils.getUserId();
        List<DeclareEntShowDTO> list = declareEntMapper.draftScorePage(page, keyWord, userId, declareType);
        if (CollectionUtils.isEmpty(list)) {
            return page;
        }
        list.forEach(a->{
            if(StringUtils.isEmpty(a.getSecondExpertPanelId())){
                a.setNumbersReview("1");
            }else {
                a.setNumbersReview("2");
            }
            if (StringUtils.isNotEmpty(a.getCloseDebatePPT())) {
                a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            }
            if (StringUtils.isNotEmpty(a.getCloseDebateVideo())) {
                a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            }
        });
        page.setRecords(list);
        return page;
    }

    public Page<DeclareEntExpertShowDTO> summaryScorePage(
            Page<DeclareEntExpertShowDTO> page, String keyWord,String index, String declareType, String responsibleName) {
        List<DeclareEntExpertShowDTO> list = new ArrayList<>();

        List<String> declareTypeList = new ArrayList<>(3);
        if(StringUtils.isNotBlank(declareType)) {
            declareTypeList = Arrays.asList(declareType.split(","));
        }
        if(StringUtils.isEmpty(index) || index.equals("1")){
            list = declareEntMapper.summaryScorePage(page, keyWord, declareTypeList, responsibleName);
        }else if(index.equals("2")){
            list = declareEntMapper.secondSummaryScorePage(page, keyWord, declareTypeList, responsibleName);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            for (DeclareEntExpertShowDTO dto : list) {
                List<String> userIds = splitParam(dto.getAllMember());
                String userNames = userMapper.searchUserNames(userIds);
                dto.setAllMemberName(userNames);
            }
        }
        list.forEach(a->{
            if (StringUtils.isNotEmpty(a.getCloseDebatePPT())) {
                a.setCloseDebatePPTs(JSON.parseArray(a.getCloseDebatePPT(), AttachmentDTO.class));
            }
            if (StringUtils.isNotEmpty(a.getCloseDebateVideo())) {
                a.setCloseDebateVideos(JSON.parseArray(a.getCloseDebateVideo(), AttachmentDTO.class));
            }
        });
        page.setRecords(list);
        return page;
    }

    public List<ExpertScoreDTO> scoreDetail(String declareEntId,String index) {
        if(StringUtils.isEmpty(index) || "1".equals(index)){
            return expertScoreHistoryMapper.selectScoreDetailList(declareEntId);
        }else {
           return expertScoreHistoryMapper.selectSecondScoreDetailList(declareEntId);
        }
    }

    public ExpertScoreDTO getMark(String declareEntId){
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        LambdaQueryWrapper<ExpertScoreHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExpertScoreHistory::getDeclareEntId, declareEntId)
                .eq(ExpertScoreHistory::getCreatedId, userId);
        List<ExpertScoreHistory> histories = expertScoreHistoryMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(histories)) {
            throw new ValidationException("error", "评分信息不存在");
        }
        ExpertScoreDTO dto = new ExpertScoreDTO();
        BeanUtils.copyProperties(histories.get(0), dto);
        return dto;
    }

    public void mark(ExpertScoreCreateDTO dto){
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertScoreHistory expertScoreHistory = new ExpertScoreHistory();
        expertScoreHistory.setId(UUIDGenerator.getUUID());
        expertScoreHistory.setScoreConfig(dto.getScoreConfig());
        expertScoreHistory.setTotalScore(dto.getTotalScore());
        expertScoreHistory.setDeclareEntId(dto.getDeclareEntId());
        expertScoreHistory.setStatus(Constants.SCORE_HISTORY_STATUS.SCORE_HISTORY_STATUS_0.getCode());
        if(StringUtils.isEmpty(dto.getIndex()) || "1".equals(dto.getIndex())){
            expertScoreHistory.setScoreIndex("1");
        }else if("2".equals(dto.getIndex())){
            expertScoreHistory.setScoreIndex("2");
        }
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        expertScoreHistory.setCreatedId(userId);
        expertScoreHistory.setUpdatedId(userId);
        expertScoreHistory.setCreatedDate(nowTime);
        expertScoreHistory.setUpdatedDate(nowTime);
        expertScoreHistoryMapper.insert(expertScoreHistory);
    }

    public void submitMark(ExpertScoreCreateDTO dto){
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertScoreHistory expertScoreHistory = new ExpertScoreHistory();
        expertScoreHistory.setId(UUIDGenerator.getUUID());
        expertScoreHistory.setScoreConfig(dto.getScoreConfig());
        expertScoreHistory.setTotalScore(dto.getTotalScore());
        expertScoreHistory.setDeclareEntId(dto.getDeclareEntId());
        expertScoreHistory.setStatus(Constants.SCORE_HISTORY_STATUS.SCORE_HISTORY_STATUS_1.getCode());
        if(StringUtils.isEmpty(dto.getIndex()) || "1".equals(dto.getIndex())){
            expertScoreHistory.setScoreIndex("1");
        }else if("2".equals(dto.getIndex())){
            expertScoreHistory.setScoreIndex("2");
        }
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        expertScoreHistory.setCreatedId(userId);
        expertScoreHistory.setUpdatedId(userId);
        expertScoreHistory.setCreatedDate(nowTime);
        expertScoreHistory.setUpdatedDate(nowTime);
        expertScoreHistoryMapper.insert(expertScoreHistory);
        delSavedScore(dto, userId);
    }

    public void updateMark(ExpertScoreCreateDTO dto){
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertScoreHistory expertScoreHistory = expertScoreHistoryMapper.selectById(dto.getId());
        if (expertScoreHistory == null) {
            throw new ValidationException("error", "评分信息不存在");
        }
        expertScoreHistory.setScoreConfig(dto.getScoreConfig());
        expertScoreHistory.setTotalScore(dto.getTotalScore());
        expertScoreHistory.setStatus(Constants.SCORE_HISTORY_STATUS.SCORE_HISTORY_STATUS_0.getCode());
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        expertScoreHistory.setUpdatedId(userId);
        expertScoreHistory.setUpdatedDate(nowTime);
        expertScoreHistoryMapper.updateById(expertScoreHistory);
    }

    public void updateSubmitMark(ExpertScoreCreateDTO dto){
        ZonedDateTime nowTime = ZonedDateTime.now();
        ExpertScoreHistory expertScoreHistory = expertScoreHistoryMapper.selectById(dto.getId());
        if (expertScoreHistory == null) {
            throw new ValidationException("error", "评分信息不存在");
        }
        expertScoreHistory.setScoreConfig(dto.getScoreConfig());
        expertScoreHistory.setTotalScore(dto.getTotalScore());
        expertScoreHistory.setStatus(Constants.SCORE_HISTORY_STATUS.SCORE_HISTORY_STATUS_1.getCode());
        // 获取用户id
        String userId = SecurityUtils.getUserId();
        expertScoreHistory.setUpdatedId(userId);
        expertScoreHistory.setUpdatedDate(nowTime);
        expertScoreHistoryMapper.updateById(expertScoreHistory);
        delSavedScore(dto, userId);
    }

    public void exportSummaryScore(HttpServletResponse response, HttpServletRequest request
            , String projectId ,String index, String declareType, String responsibleName) throws Exception {
        List<DeclareEntExpertShowDTO> allList = new ArrayList<>();
        List<Map> listIndex = new ArrayList<>();
        List<DeclareEntExpertShowDTO> list = new ArrayList<>();
        List<String> declareTypeList = new ArrayList<>(3);
        if(StringUtils.isNotBlank(declareType)) {
            declareTypeList = Arrays.asList(declareType.split(","));
        }
        if(StringUtils.isEmpty(index) || "1".equals(index)){
            list = declareEntMapper.summaryScorePage(null, "", declareTypeList, responsibleName);
        }else if("2".equals(index)){
            list = declareEntMapper.secondSummaryScorePage(null, "", declareTypeList, responsibleName);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            for (DeclareEntExpertShowDTO dto : list) {
                List<String> userIds = splitParam(dto.getAllMember());
                String userNames = userMapper.searchUserNames(userIds);
                dto.setAllMemberName(userNames);
                List<ExpertScoreDTO> scoreDTOS = new ArrayList<>();
                if(StringUtils.isEmpty(index) || "1".equals(index)){
                    scoreDTOS = expertScoreHistoryMapper.selectScoreDetailList(dto.getDeclareEntId());
                }else if("2".equals(index)){
                    scoreDTOS = expertScoreHistoryMapper.selectSecondScoreDetailList(dto.getDeclareEntId());
                }
                Map map = new HashMap();
                map.put("index", scoreDTOS.size());
                map.put("averageScore", dto.getAverageScore());
                listIndex.add(map);
                if (CollectionUtils.isNotEmpty(scoreDTOS)) {
                    for (ExpertScoreDTO expertScoreDTO : scoreDTOS) {
                        DeclareEntExpertShowDTO exportDTO =  new DeclareEntExpertShowDTO();
                        BeanUtils.copyProperties(dto, exportDTO);
                        exportDTO.setMemberName(expertScoreDTO.getUserName());
                        exportDTO.setTotalScore(expertScoreDTO.getTotalScore());
                        JSONObject jsonObject = JSONObject.parseObject(expertScoreDTO.getScoreConfig());
                        if (jsonObject.containsKey("eval")) {
                            exportDTO.setEval(jsonObject.get("eval").toString());
                        }
                        allList.add(exportDTO);
                    }
                }
            }
        }

        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.writeHeadRow(Arrays.asList("打分汇总表"));
        writer.merge(0, 0, 0, 6, "打分汇总表", false);

        writer.addHeaderAlias("subjectName", "项目名称");
        writer.addHeaderAlias("responsibleName", "单位名称");
        writer.addHeaderAlias("allMemberName", "团成员名单");
        writer.addHeaderAlias("averageScore", "得分");
        writer.addHeaderAlias("memberName", "实际参与人");
        writer.addHeaderAlias("totalScore", "打分");
        writer.addHeaderAlias("eval", "综合评价");

        writer.setOnlyAlias(true);
        writer.write(allList, true);

        if (CollectionUtils.isNotEmpty(listIndex)) {
            Integer firstRow = 1;
            Integer lastRow = 1;
            for (Map map : listIndex) {
                firstRow = lastRow + 1;
                lastRow = firstRow + Integer.parseInt(map.get("index").toString()) - 1;
                if (Integer.parseInt(map.get("index").toString()) != 1) {
                    writer.merge(firstRow, lastRow, 3, 3, map.get("averageScore").toString(), false);
                }
            }
        }

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");

        String fileName = "打分汇总表.xlsx";

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.addHeader("filename", URLEncoder.encode(fileName, "UTF-8"));
        response.addHeader("Access-Control-Expose-Headers", "filename");

        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);

        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }


   @Autowired
   private CodeDecorator codeDecorator;
    public void exportUploadedPPtAndVideoProject(HttpServletResponse response, HttpServletRequest request,String declareType,String isUploadedPPTVideo) throws UnsupportedEncodingException {

        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.setOnlyAlias(true);
        List<ImportRecordDTO> projectList = declareEntMapper.getUploadedPPtAndVideoProject(declareType,isUploadedPPTVideo);
        projectList = codeDecorator.decorate(projectList);
        //自定义标题别名
        writer.addHeaderAlias("declareTypeStr", "申报类别");
        writer.addHeaderAlias("subjectName", "项目名称");
        writer.addHeaderAlias("responsibleName", "承担单位名称");


        writer.write(projectList, true);


        response.setContentType("application/vnd.ms-excel;charset=utf-8");

        String fileName = "已上传答辩ppt和视频的项目.xlsx";
        // 解决中文文件名乱码问题
        if (request.getHeader("User-Agent").toUpperCase()
                .indexOf("MSIE") > 0) {
            fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
        } else {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
        }

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {

        }
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    /**
     * bug简单修复
     *
     * @param dto 打分dto
     * @param userId 用户id
     */
    public void delSavedScore(ExpertScoreCreateDTO dto, String userId) {
        // 删除该项目的该用户的该次的暂存的打分记录
        expertScoreHistoryMapper.delete(new LambdaQueryWrapper<ExpertScoreHistory>()
                .eq(ExpertScoreHistory::getDeclareEntId, dto.getDeclareEntId())
                .eq(ExpertScoreHistory::getCreatedId, userId)
                .eq(ExpertScoreHistory::getScoreIndex, dto.getIndex())
                .eq(ExpertScoreHistory::getStatus,Constants.SCORE_HISTORY_STATUS.SCORE_HISTORY_STATUS_0.getCode()));
    }

}
