package com.fengyun.udf.uaa.fastdfs;

import java.io.IOException;
import java.io.OutputStream;


public class DownloadStream implements DownloadCallback {
    private OutputStream out;
    private long currentBytes = 0;

    public DownloadStream(OutputStream out) {
        super();
        this.out = out;
    }


    public int recv(long fileSize, byte[] data, int bytes) {
        try {
            out.write(data, 0, bytes);
        } catch (IOException ex) {
            ex.printStackTrace();
            return -1;
        }

        currentBytes += bytes;
        if (this.currentBytes == fileSize) {
            this.currentBytes = 0;
        }

        return 0;
    }
}
