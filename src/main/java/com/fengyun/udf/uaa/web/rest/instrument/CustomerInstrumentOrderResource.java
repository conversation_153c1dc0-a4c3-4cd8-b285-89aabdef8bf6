package com.fengyun.udf.uaa.web.rest.instrument;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderReceiptBankDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.*;
import com.fengyun.udf.uaa.service.instrument.InstrumentOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-30
 */

@RestController
@RequestMapping("/api/customer/instrument-order")
@Api(value = "企业端-仪器预约", tags = "企业端-仪器预约")
@Validated
@Slf4j
@AllArgsConstructor
public class CustomerInstrumentOrderResource extends BaseResource {
    private final InstrumentOrderService instrumentOrderService;

    @PostMapping("/new")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/new", methodDescription = "添加仪器预约", httpMethod = "POST")
    @ApiOperation(value = "添加仪器预约")
    public ResponseEntity<ReturnResultDTO> create(@RequestBody @Validated InstrumentOrderCreateDTO dto) {
        instrumentOrderService.create(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/edit")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/edit", methodDescription = "修改仪器预约", httpMethod = "POST")
    @ApiOperation(value = "修改仪器预约")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody @Validated InstrumentOrderUpdateDTO dto) {
        instrumentOrderService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/view", methodDescription = "仪器预约详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "仪器预约详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderDetailDTO>> detail(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.getInstrumentOrderDetailDTO(id,SecurityUtils.getUserId()));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order", methodDescription = "仪器预约列表", httpMethod = "GET")
    @ApiOperation(value = "仪器预约列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderListDTO>>> page(Page page, InstrumentOrderQueryDTO dto) {
        dto.setUserId(SecurityUtils.getUserId());
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.page(page, dto));
    }

    @GetMapping("/summary/export")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/summary/export", methodDescription = "我的仪器预约汇总导出", httpMethod = "GET")
    @ApiOperation(value = "我的仪器预约汇总导出")
    public void export(InstrumentOrderQueryDTO dto, HttpServletResponse response, HttpServletRequest request) throws IOException {
        dto.setExport("YES");
        dto.setUserId(SecurityUtils.getUserId());
        List<InstrumentOrderListDTO> list = instrumentOrderService.export(dto);

        ExcelWriter writer = ExcelUtil.getWriter(true);

        writer.addHeaderAlias("index", "序号");
        writer.addHeaderAlias("enName", "企业名称");
        writer.addHeaderAlias("instrumentName", "设备名称");
        writer.addHeaderAlias("contact", "预约联系人");
        writer.addHeaderAlias("contactTel", "预约联系方式");
        writer.addHeaderAlias("model", "型号");
        writer.addHeaderAlias("skilledUseStr", "是否需要培训");
        writer.addHeaderAlias("orderDateStr", "预约时间");
        writer.addHeaderAlias("amount", "核准总金额");
        writer.addHeaderAlias("statusStr", "审核状态");
        writer.addHeaderAlias("workStatusStr", "工单状态");
        writer.addHeaderAlias("settlementStatusStr", "结算状态");
        writer.addHeaderAlias("leaseTypeStr", "租赁类型");
        writer.addHeaderAlias("createdDate", "提交时间");


        writer.setOnlyAlias(true);
        writer.write(list, true);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");

        String fileName = "预约记录导出表格.xlsx";

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.addHeader("filename", URLEncoder.encode(fileName, "UTF-8"));
        response.addHeader("Access-Control-Expose-Headers", "filename");

        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);

        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    @GetMapping("/order-time")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/order-time", methodDescription = "根据日期获取预约状态", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderDate", value = "预约日期，yyyy-MM-dd", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "instrumentId", value = "仪器id", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "根据日期获取预约状态")
    public ResponseEntity<ReturnResultDTO<List<InstrumentOrderTimeDTO>>> orderTime(@NotBlank(message = "仪器id不能为空") String instrumentId,
                                                                                   @NotBlank(message = "预约日期不能为空") String orderDate) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.getOrderTime(instrumentId, orderDate));
    }

    @GetMapping("/view-pre")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/view-pre", methodDescription = "前一次预约详情", httpMethod = "GET")
    @ApiOperation(value = "前一次预约详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderDetailDTO>> detailPre() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.detailPre());
    }

    @GetMapping("/cancel")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/cancel", methodDescription = "预约撤回", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "预约撤回")
    public ResponseEntity<ReturnResultDTO> cancel(@NotNull(message = "id不能为空") Integer id) {
        instrumentOrderService.cancel(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/receipt")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/receipt", methodDescription = "查看开户及收据信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查看开户及收据信息")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderReceiptDetailDTO>> getReceipt(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.getReceipt(id));
    }

    @GetMapping("/work")
    @CustomBaseException
    @AuditLog(method = "/api/customer/instrument-order/work", methodDescription = "查看工单信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查看工单信息")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderWorkDetailDTO>> getWork(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderService.getShowWork(id));
    }


}
