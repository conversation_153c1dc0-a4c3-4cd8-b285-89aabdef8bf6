package com.fengyun.udf.uaa.web.rest.module;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.module.SaveModuleListDto;
import com.fengyun.udf.uaa.dto.response.module.ModuleListTreeDto;
import com.fengyun.udf.uaa.service.module.ModuleListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/17 14:01
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/module")
@Api(value = "管理端-模块管理", tags = "管理端-模块管理")
public class MangeModuleResource extends BaseResource {

    @Autowired
    private ModuleListService moduleListService;

    @PostMapping("/save")
    @CustomBaseException
    @AuditLog(method = "/api/module/save", methodDescription = "保存/编辑模块信息", httpMethod = "POST")
    @ApiOperation(value = "保存/编辑模块信息")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> save(@RequestBody SaveModuleListDto dto) {
        moduleListService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/delete/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/module/delete/{id}", methodDescription = "删除模块", httpMethod = "POST")
    @ApiOperation(value = "删除模块")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "模块id", required = true, dataType = "String", paramType = "query")
    })
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> delete(@PathVariable @NotBlank(message = "模块id不能为空") String id) {
        moduleListService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/detail/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/module/detail/{id}", methodDescription = "模块详情", httpMethod = "GET")
    @ApiOperation(value = "模块详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "模块id", required = true, dataType = "String", paramType = "path")
    })
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> detail(@PathVariable @NotBlank(message = "模块id不能为空") String id) {

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), moduleListService.detail(id));
    }

    @GetMapping("/listTree")
    @CustomBaseException
    @AuditLog(method = "/api/module/listTree", methodDescription = "模块树", httpMethod = "GET")
    @ApiOperation(value = "模块树")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<List<ModuleListTreeDto>>> listTree() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), moduleListService.listTree(null));
    }

    @GetMapping("/cascade")
    @CustomBaseException
    @AuditLog(method = "/api/module/listTree", methodDescription = "模块级联列表", httpMethod = "GET")
    @ApiOperation(value = "模块级联列表(根模块传空)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "模块id", dataType = "String", paramType = "query")
    })
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<List<ModuleListTreeDto>>> cascade(String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), moduleListService.cascade(id));
    }

}
