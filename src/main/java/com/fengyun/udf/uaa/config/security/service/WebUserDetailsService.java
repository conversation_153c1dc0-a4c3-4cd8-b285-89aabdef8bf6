package com.fengyun.udf.uaa.config.security.service;


import com.fengyun.udf.security.dto.AuthUser;
import com.fengyun.udf.uaa.domain.system.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by wangg on 2019/7/5.
 */
@Component
public class WebUserDetailsService extends BaseUserDetailsService {

    private final static String USER_NOT_FOUND = "error.login.userNotFound";

    @Autowired
    private MessageSource messageSource;

    public UserDetails loadUserByUsernameAndTenantCode(String username,String loginType) throws UsernameNotFoundException {
        String loginName = username;
        List<String> userTypes = prepareUserTypes(loginType);

        User user = userService.findUserByLoginNameOrTel(loginName, userTypes);
        if (user == null) {
            throw new UsernameNotFoundException(messageSource.getMessage(USER_NOT_FOUND, null, null, LocaleContextHolder.getLocale()));
        }

        Set<String> authSet = getAuthSet(user.getId());
        authSet.add(user.getType());
        Collection<? extends GrantedAuthority> authorities
                = AuthorityUtils.createAuthorityList(authSet.toArray(new String[0]));
        return new AuthUser(user.getId(), user.getNickName(),
                user.getId(), user.getPassword(),
                true, true, true, true, authorities);
    }
}
