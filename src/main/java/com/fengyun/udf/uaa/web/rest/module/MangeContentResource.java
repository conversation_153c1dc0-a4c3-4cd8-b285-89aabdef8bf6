package com.fengyun.udf.uaa.web.rest.module;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.module.QueryContentDto;
import com.fengyun.udf.uaa.dto.request.module.SaveModuleListDto;
import com.fengyun.udf.uaa.dto.response.module.SaveContentDto;
import com.fengyun.udf.uaa.service.module.ContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2022/1/17 17:04
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/content")
@Api(value = "管理端-内容管理", tags = "管理端-内容管理")
public class MangeContentResource extends BaseResource {

    @Autowired
    private ContentService contentService;

    @PostMapping("/save")
    @CustomBaseException
    @AuditLog(method = "/api/content/save", methodDescription = "保存/编辑内容", httpMethod = "POST")
    @ApiOperation(value = "保存/编辑内容")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> save(@RequestBody SaveContentDto dto) {
        contentService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/delete/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/content/delete/{id}", methodDescription = "删除内容", httpMethod = "POST")
    @ApiOperation(value = "删除内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "内容id", required = true, dataType = "String", paramType = "path")
    })
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> delete(@PathVariable @NotBlank(message = "内容id不能为空") String id) {
        contentService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/detail/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/module/detail/{id}", methodDescription = "内容详情", httpMethod = "GET")
    @ApiOperation(value = "内容详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "内容id", required = true, dataType = "String", paramType = "path")
    })
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> detail(@PathVariable @NotBlank(message = "模块id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), contentService.detail(id));
    }

    @GetMapping("/page")
    @CustomBaseException
    @AuditLog(method = "/api/module/page", methodDescription = "分页查询", httpMethod = "GET")
    @ApiOperation(value = "分页查询")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> page(Page page, QueryContentDto dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), contentService.page(page, dto));
    }

}
