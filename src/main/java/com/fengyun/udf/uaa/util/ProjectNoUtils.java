package com.fengyun.udf.uaa.util;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class ProjectNoUtils {

    private  String timeFormat = "yyyyMMddHHmmss";

    public static String generateProjectNo(String prefix ,String time){
        String subStr = String.valueOf(new java.util.Random().nextInt(900)+100);
        String midStr = "";
        if(! StringUtils.isEmpty(time)){
            midStr = time.substring(2,8);
        }else {
            midStr= getCurrentTime("yyyyMMddHHmmss").substring(2,8);
        }
        if(StringUtils.isEmpty(prefix)){
            prefix= "";
        }
        return prefix+midStr+subStr;
    }

    public static String getCurrentTime(String fmt) {
        Calendar calendar = Calendar.getInstance();
        Date date = calendar.getTime();
        SimpleDateFormat formatter = new SimpleDateFormat(fmt);
        return formatter.format(date);
    }

    public static void main(String[] args) {
        String str = generateProjectNo("2022HS","");
        System.out.println(str);
    }
}
