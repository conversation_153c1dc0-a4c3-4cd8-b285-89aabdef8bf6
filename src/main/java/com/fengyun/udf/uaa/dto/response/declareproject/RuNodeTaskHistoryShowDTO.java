package com.fengyun.udf.uaa.dto.response.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <p>
 * 运行时节点任务
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-19
 */
@Data
public class RuNodeTaskHistoryShowDTO extends AbstractDTO implements Serializable{

    @ApiModelProperty(value = "assign_user_id")
    private String assignUserId;

    @ApiModelProperty(value = "assign_user_name")
    private String assignUserName;

    @ApiModelProperty(value = "assign_user_role")
    @DictData(target = "assignUserRoleStr",codeType = "TDECLARE_NODE")
    private String assignUserRole;
    private String assignUserRoleStr;

    /**
    * 状态
    */
    private String status;

    /**
    * 处理时间
    */
    @ApiModelProperty(value = "process_time")
    private ZonedDateTime processTime;
    @DateFormat(property = "processTime",dateFormat = "yyyy-MM-dd HH:mm")
    private String processTimeStr;

    /**
    * 处理意见
    */
    @TableField(value = "audit_remarks")
    private String auditRemarks;

    /**
     * 选择的处理事件
     */
    @ApiModelProperty(value = "审批操作 code:" )
    @DictData(target = "selectEventStr",codeType = "TDECLARE_EVENT")
    private String selectEvent;
    private String selectEventStr;
}
