package com.fengyun.udf.uaa.dto.response.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Getter
@Setter
public class InstrumentDetailDTO extends AbstractDTO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备编号")
    private String num;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("厂家")
    private String factory;

    @ApiModelProperty("放置地点")
    private String address;

    @ApiModelProperty("状态")
    @DictData(target = "statusStr",codeType = "INSTRUMENT_STATUS")
    private String status;
    private String statusStr;

    @ApiModelProperty("每时间段人数")
    private Integer personNum;

    @ApiModelProperty("仪器租赁展示价格")
    private String leasePriceStr;

    @ApiModelProperty("仪器租赁单价")
    private Double leasePrice;

    @ApiModelProperty("仪器租赁价格单位")
    private String leasePriceUnit;

    @ApiModelProperty("仪器租赁最低收费")
    private Double leasePriceMin;

    @ApiModelProperty("委托测试展示价格")
    private String testPriceStr;

    @ApiModelProperty("委托测试单价")
    private Double testPrice;

    @ApiModelProperty("委托测试价格单位")
    private String testPriceUnit;

    @ApiModelProperty("计算方式，INSTRUMENT_CALCULATE_METHOD")
    @DictData(target = "calculateMethodStr",codeType = "INSTRUMENT_CALCULATE_METHOD")
    private String calculateMethod;
    private String calculateMethodStr;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("技术参数内容")
    private String tecContent;

    @ApiModelProperty("功能特色内容")
    private String featureContent;

    @ApiModelProperty("注意事项内容")
    private String careContent;

    @ApiModelProperty("图片")
    private String image;

    @ApiModelProperty("区域，INSTRUMENT_AREA")
    @DictData(target = "instrumentAreaStr",codeType = "INSTRUMENT_AREA")
    private String instrumentArea;
    private String instrumentAreaStr;

    @ApiModelProperty("耗材")
    private List<ConsumableListDTO> list;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("是否全天预约")
    private Boolean isAllDay;
    
}