package com.fengyun.udf.uaa.web.rest.upload;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.dto.response.UploadResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;

@RestController
@RequestMapping("/api/download")
@Api(value = "文件下载", description = "文件下载")
@Slf4j
public class FileDownloadResource extends BaseResource {

    @GetMapping("/file")
    @ResponseBody
    @ApiOperation(value = "文件下载", httpMethod = "GET", response = UploadResultDTO.class, notes = "文件下载")
    public void downloadFile(final String fileName, final String filePath, HttpServletResponse response) throws IOException {
        URL url = new URL(filePath);
        URLConnection uc = url.openConnection();
        String contentType = uc.getContentType();
        int contentLength = uc.getContentLength();
        if (contentType.startsWith("text/") || contentLength == -1) {
            throw new ValidationException("file", "文件为空");
        }
        try (InputStream raw = uc.getInputStream()) {
            InputStream in = new BufferedInputStream(raw);
            byte[] data = new byte[contentLength];
            int offset = 0;
            while (offset < contentLength) {
                int bytesRead = in.read(data, offset, data.length - offset);
                if (bytesRead == -1) {
                    break;
                }
                offset += bytesRead;
            }
            if (offset != contentLength) {
                throw new ValidationException("file", "文件为空");
            }
            // 清空response
            response.reset();
            // 设置response的Header
            /*String name = fileName;
            if(name != null){
                name = new String(name.getBytes("gb2312"), "ISO8859-1");
            }*/
            response.addHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
            response.addHeader("Content-Length", "" + contentLength);
            response.setContentType("application/octet-stream");
            try (OutputStream out = new BufferedOutputStream(response.getOutputStream())) {
                out.write(data);
                out.flush();
            }
        }
    }

    @GetMapping("/template")
    @CustomBaseException
    @AuditLog(method = "/api/download/template", methodDescription = "下载模板", httpMethod = "GET")
    @ApiOperation(value = "下载模板")
    public void downloadTemplate(String type, HttpServletResponse response) throws Exception {
        OutputStream outputStream = null;
        InputStream inputStream = null;
        if (Constants.FIRST_REVIEW.equals(type)) {
            type = "第一轮评审名单模版.xlsx";
        }
        if (Constants.SECOND_REVIEW.equals(type)) {
            type = "第二轮评审名单模版.xlsx";
        }
        if (Constants.APPROVE.equals(type)) {
            type = "立项名单模版.xlsx";
        }
        if (Constants.MIDDLE_INSPECTION.equals(type)) {
            type = "中期检查专家评审通过模版.xlsx";
        }
        if (Constants.END_INSPECTION.equals(type)) {
            type = "结题验收专家评审通过模版.xlsx";
        }
        try {
            response.addHeader("filename", URLEncoder.encode(type, "UTF-8"));
            response.addHeader("Access-Control-Expose-Headers", "filename");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(type, "UTF-8"));
            outputStream = response.getOutputStream();
            inputStream = this.getClass().getResourceAsStream("/template/" + type);

            parse(inputStream, outputStream);

            outputStream.close();
            inputStream.close();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (Exception e) {

            }
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {

            }
        }
    }
    private void parse(final InputStream in, OutputStream swapStream) throws Exception {
        int ch;
        while ((ch = in.read()) != -1) {
            swapStream.write(ch);
        }
    }
}
