package com.fengyun.udf.uaa.web.rest.module;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.module.QueryContentDto;
import com.fengyun.udf.uaa.dto.response.module.ContentListDto;
import com.fengyun.udf.uaa.dto.response.module.ModuleListTreeDto;
import com.fengyun.udf.uaa.service.module.ContentService;
import com.fengyun.udf.uaa.service.module.ModuleListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/17 17:12
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/ua/show")
@Api(value = "展示-内容管理", tags = "展示-内容管理")
public class ShowModuleContentResource extends BaseResource {
    @Autowired
    private ModuleListService moduleListService;

    @Autowired
    private ContentService contentService;

    @GetMapping("/listTree")
    @CustomBaseException
    @AuditLog(method = "/ua/show/listTree", methodDescription = "模块树", httpMethod = "GET")
    @ApiOperation(value = "模块树")
    public ResponseEntity<ReturnResultDTO<List<ModuleListTreeDto>>> listTree() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), moduleListService.listTree("1"));
    }

    @GetMapping("/detail/{id}")
    @CustomBaseException
    @AuditLog(method = "/ua/show/detail/{id}", methodDescription = "内容详情", httpMethod = "GET")
    @ApiOperation(value = "内容详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "内容id", required = true, dataType = "String", paramType = "path")
    })
    public ResponseEntity<ReturnResultDTO> detail(@PathVariable @NotBlank(message = "模块id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), contentService.detail(id));
    }

    @GetMapping("/page")
    @CustomBaseException
    @AuditLog(method = "/ua/show/page", methodDescription = "分页查询", httpMethod = "GET")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<ReturnResultDTO> page(Page page, QueryContentDto dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), contentService.page(page, dto));
    }

    @GetMapping("/content/page")
    @CustomBaseException
    @ApiOperation(value = "首页检索")
    public ResponseEntity<ReturnResultDTO<ContentListDto>> contentPage(Page page, String keyWord) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), contentService.contentPage(page, keyWord));
    }
}
