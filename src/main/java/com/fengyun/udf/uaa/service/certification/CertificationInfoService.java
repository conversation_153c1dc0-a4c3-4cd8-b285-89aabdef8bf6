package com.fengyun.udf.uaa.service.certification;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.certification.CertificationInfo;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.certification.CreateCertificationDto;
import com.fengyun.udf.uaa.dto.request.certification.EnUserAddInfoDto;
import com.fengyun.udf.uaa.mapper.certification.CertificationInfoMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/1/25 14:43
 * @Version 1.0
 */

@Service
@Transactional
public class CertificationInfoService extends BaseService {

    @Autowired
    private CertificationInfoMapper certificationInfoMapper;

    public void save(CreateCertificationDto dto) {
        boolean create = false;

        CertificationInfo certificationInfo = certificationInfoMapper.selectById(dto.getId());
        if (certificationInfo == null) {
            create = true;
        }
        if (create) {
            certificationInfo = new CertificationInfo();
            certificationInfo.setOrgName(dto.getOrgName());
            certificationInfo.setAttachment(toJSONString(dto.getAttachment()));
            certificationInfo.setId(UUIDGenerator.getUUID());
            certificationInfo.create(SecurityUtils.getUserId());

            certificationInfoMapper.insert(certificationInfo);
        } else {
            certificationInfo.setOrgName(dto.getOrgName());
            certificationInfo.setAttachment(toJSONString(dto.getAttachment()));
            certificationInfo.update(SecurityUtils.getUserId());

            certificationInfoMapper.updateById(certificationInfo);
        }
    }

    public void addInfo(EnUserAddInfoDto dto) {
        long integer = userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getTel, dto.getTel()));
        if (integer >= 1) {
            throw new ValidationException("error", "该手机号已注册!");
        }

        User user = userMapper.selectById(dto.getUserId());
        user.setTel(dto.getTel());
        user.setAttachmentLicense(toJSONString(dto.getAttachmentLicense()));

        user.setAuditStatus(Constants.EN_REG_STATUS_PEN);
        user.create(SecurityUtils.getUserId());
        userMapper.updateById(user);
    }
}
