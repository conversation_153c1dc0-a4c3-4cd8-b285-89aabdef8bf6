<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.expertpanel.ExpertScoreTemplateMapper">

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO">
        select * from t_expert_score_template
        <where>
            <if test="params.keyWord != null and params.keyWord != ''">
                and template_name like CONCAT('%', #{params.keyWord}, '%')
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status}
            </if>
        </where>
        order by created_date desc
    </select>

    <select id="getList" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO">
        select * from t_expert_score_template order by created_date desc
    </select>

    <select id="getRelationTemplate" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO">
        select c.* from t_declare_ent a
        left join t_declare_project b on a.declare_id=b.id
        left join t_expert_score_template c on b.expert_score_template_id=c.id
        where a.id=#{declareEntId}
    </select>

    <select id="getSecondRelationTemplate" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO">
        select c.* from t_declare_ent a
        left join t_declare_project b on a.declare_id=b.id
        left join t_expert_score_template c on b.second_expert_score_template_id=c.id
        where a.id=#{declareEntId}
    </select>


</mapper>