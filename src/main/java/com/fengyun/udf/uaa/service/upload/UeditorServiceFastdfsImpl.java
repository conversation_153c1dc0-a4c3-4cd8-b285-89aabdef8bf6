package com.fengyun.udf.uaa.service.upload;

import com.baidu.ueditor.define.AppInfo;
import com.baidu.ueditor.define.BaseState;
import com.baidu.ueditor.define.MultiState;
import com.baidu.ueditor.define.State;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.fastdfs.ClientGlobal;
import com.fengyun.udf.uaa.fastdfs.client.UploadFile;
import com.fengyun.udf.uaa.fastdfs.client.UploadManager;
import lombok.extern.slf4j.Slf4j;
import net.viservice.editor.ueditor.UeditorService;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.stream.IntStream;

/**
 * UeditorService实现 - Fastdfs
 */
@Component("UeditorServiceFastdfsImpl")
@Slf4j
public class UeditorServiceFastdfsImpl implements UeditorService {

    @Override
    public net.viservice.editor.MultipartFile getMultipartFile(String filedName, HttpServletRequest request) {
        net.viservice.editor.MultipartFile resultFile = null;
        try {
            MultipartHttpServletRequest multipartHttpservletRequest = (MultipartHttpServletRequest) request;
            MultipartFile multipartFile = multipartHttpservletRequest.getFile(filedName);
            if (!multipartFile.isEmpty()) {
                resultFile = new net.viservice.editor.StandardMultipartFile(filedName, multipartFile.getInputStream(), multipartFile.getOriginalFilename(), multipartFile.getSize());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultFile;
    }

    @Override
    public State saveFileByInputStream(net.viservice.editor.MultipartFile multipartFile, long maxSize) {
        State state = null;
        try {
            if (multipartFile.getSize() > maxSize) {
                return new BaseState(false, AppInfo.MAX_SIZE);
            }

            Map<String, Object> uploadResult = null;
            String fileName = multipartFile.getOriginalFilename();
            String extName = getExtName(fileName);

            if ("pdf".equals(extName)) {
                log.info("正在处理pdf wenjian");
                byte [] byteArr = multipartFile.getBytes();
                InputStream inputStream = new ByteArrayInputStream(byteArr);
                if(containsJavaScript(inputStream)) {
                    throw new ValidationException("file", "文件包含XSS，不能上传");
                }
            }

            UploadFile uploadfile = new UploadFile(fileName, multipartFile.getBytes(), extName);
            String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);

            state = new BaseState(true);
            state.putInfo("size", multipartFile.getSize());
            state.putInfo("title", fileName);
//            state.putInfo("groupName", ClientGlobal.default_group);
//            state.putInfo("storageFileName", uploadResult.get("storageFileName").toString());
            state.putInfo("url", ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
            return state;
        } catch (Exception e) {
            return new BaseState(false, AppInfo.IO_ERROR);
        }
    }
    private static boolean containsJavaScript(InputStream input) throws IOException {
        PDDocument document = PDDocument.load(input);
        return containsJavaScript(document);
    }

    private static boolean containsJavaScript(PDDocument document) throws IOException {
        PDPageTree pages = document.getPages();
        String cosName = document.getDocument().getTrailer().toString();
        if(cosName.contains("COSName{JavaSCript}")||cosName.contains("COSName{JS}")){
            return true;
        }
        return IntStream.range(0, pages.getCount()).anyMatch(i -> {
            return pages.get(i).getCOSObject().toString().contains("COSName{JS}");
        });

        /*Optional<COSObject> pdfJs = document.getDocument().getObjects().stream().filter(cosObject -> {
            String str = Optional.ofNullable(cosObject.getObject()).map(cosBase -> cosBase.toString().toLowerCase()).orElse("");
            return str.contains("javascript") || str.contains("cosname{js}");
        }).findAny();
        return pdfJs.isPresent();*/
    }
    @Override
    public State saveBinaryFile(byte[] data, String fileName) {
        State state = null;
        try {
            String extName = getExtName(fileName);
            UploadFile uploadfile = new UploadFile(fileName, data, extName);
            String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
            state = new BaseState(true);
            state.putInfo("size", data.length);
            state.putInfo("title", fileName);
            state.putInfo("url", ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
            return state;
        } catch (Exception e) {
            return new BaseState(false, AppInfo.IO_ERROR);
        }
    }

    @Override
    public State listFile(String[] allowFiles, int start, int pageSize) {
        State state = new MultiState(true);
        state.putInfo("start", start);
        state.putInfo("total", 0);
        return state;
    }

    private String getExtName(final String fileName) {
        //获取文件类型
        String extName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        extName = extName.replace(".", "");
        return extName;
    }
}
