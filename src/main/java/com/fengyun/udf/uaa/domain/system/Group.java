package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
@TableName("t_group")
public class Group extends BaseDomain {
    @TableId("ID")
    private String id;

    @TableField("PARENT_ID")
    private String parentId;

    @TableField("NAME")
    private String groupName;

    @TableField("DESCRIPTION")
    private String groupDesc;

    @TableField("SEQ")
    private Integer groupSort;
}
