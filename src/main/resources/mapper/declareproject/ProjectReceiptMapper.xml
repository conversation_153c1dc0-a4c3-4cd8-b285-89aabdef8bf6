<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.declareproject.ProjectReceiptMapper">

    <select id="detail" resultType="com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptDetailDTO">
        SELECT
            pr.id AS id,
            pr.project_id AS projectId,
            pr.audit_status AS auditStatus,
            pr.audit_remark AS auditRemark,
            pr.submit_date AS submitDate,
            pr.category AS category,
            pr.company AS company,
            pr.reason AS reason,
            pr.`date` AS `date`,
            pr.amount AS amount,
            pr.contacts AS contacts,
            pr.contact_num AS contactNum,
            pr.email AS email,
            pr.name AS `name`,
            pr.bank AS bank,
            pr.account AS `account`,
            pr.type AS type,
            pr.receipt AS receiptStr,
            pr.template AS templateStr,
            pr.receipt_no AS receiptNo,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.created_date AS createdDate,
            de.declare_type AS declareType,
            de.created_id AS createdId,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM
            t_project_receipt pr
        LEFT JOIN
            t_declare_ent de ON de.id = pr.project_id
        LEFT JOIN
            flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN
            t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN
            flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        WHERE
            pr.project_id = #{id}
          AND pr.category = #{category}
    </select>

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.declareproject.ProjectReceiptListDTO">
        SELECT
            pr.project_id AS id,
            pr.audit_status AS auditStatus,
            pr.submit_date AS submitDate,
            pr.category AS category,
            de.project_status AS projectStatus,
            de.project_no AS projectNo,
            de.declare_type AS declareType,
            dp.declare_name AS declareName,
            de.project_progress AS projectProgress,
            res.responsible_name as responsibleName,
            con.subject_name AS subjectName
        FROM
            t_project_receipt pr
        LEFT JOIN
            t_declare_ent de ON de.id = pr.project_id
        LEFT JOIN
            flow_form_responsible_unit res ON de.declare_id = res.project_id and de.created_id = res.CREATED_ID
        LEFT JOIN
            t_declare_project dp ON de.declare_id = dp.id
        LEFT JOIN
            flow_form_condition con ON de.declare_id = con.project_id and de.created_id = con.CREATED_ID
        <where>
            pr.audit_status != '0'
            <if test="condition.declareName != null and condition.declareName != ''">
                AND dp.declare_name = #{condition.declareName}
            </if>
            <if test="condition.declareTypeList != null and condition.declareTypeList.size > 0">
                AND de.declare_type IN
                <foreach collection="condition.declareTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.subjectName != null and condition.subjectName != ''">
                AND con.subject_name LIKE CONCAT('%',#{condition.subjectName},'%')
            </if>
            <if test="condition.responsibleName != null and condition.responsibleName != ''">
                AND res.responsible_name LIKE CONCAT('%',#{condition.responsibleName},'%')
            </if>
            <if test="condition.auditStatus != null and condition.auditStatus != ''">
                AND pr.audit_status = #{condition.auditStatus}
            </if>
            <if test="condition.category != null and condition.category != ''">
                AND pr.category = #{condition.category}
            </if>
        </where>
        ORDER BY pr.audit_status ASC, pr.submit_date DESC
    </select>

    <select id="maxNo" resultType="java.lang.Long">
        SELECT MAX(receipt_no)
        FROM
            t_project_receipt pr
        LEFT JOIN t_declare_ent de ON de.id = pr.project_id
        WHERE pr.category = #{category}
          AND de.declare_id = (
                SELECT
                    declare_id
                FROM
                    t_declare_ent
                WHERE
                    id = #{id}
            )
    </select>

</mapper>
