package com.fengyun.udf.uaa.service.declare;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.domain.declare.DeclareEntYearlySummary;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.declare.AddDeclareEntYearlySummaryDTO;
import com.fengyun.udf.uaa.dto.request.declare.QueryDeclareEntYearlySummaryDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryListDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryDetailDTO;
import com.fengyun.udf.uaa.mapper.declare.DeclareEntYearlySummaryMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Service
@Transactional(rollbackFor = RuntimeException.class)
public class DeclareEntYearlySummaryService {

    @Autowired
    private DeclareEntYearlySummaryMapper mapper;

    public void save(AddDeclareEntYearlySummaryDTO dto) {
        DeclareEntYearlySummary entity = getEntityFromDatabase(dto.getProjectId());
        BeanUtils.copyProperties(dto, entity);
        entity.setSubmitAt(LocalDateTime.now());
        mapper.insertOrUpdate(entity);
    }

    public DeclareEntYearlySummaryDetailDTO detailByProjectIdPlatform(String projectId) {
        return Optional.ofNullable(mapper.detail(projectId))
                .orElseThrow(() -> new ValidationException("年度总结", "该条记录在本系统中不存在"));
    }

    public DeclareEntYearlySummaryDetailDTO detailByProjectIdCustomer(String projectId) {
        return Optional.ofNullable(mapper.selectOne(Wrappers.<DeclareEntYearlySummary>lambdaQuery()
                        .eq(DeclareEntYearlySummary::getProjectId, projectId)
                        .last("LIMIT 1")))
                .map(entity -> {
                    DeclareEntYearlySummaryDetailDTO dto = new DeclareEntYearlySummaryDetailDTO();
                    BeanUtil.copyProperties(entity, dto);
                    return dto;
                })
                .orElse(null);
    }

    public Page<DeclareEntYearlySummaryListDTO> page(Page<DeclareEntYearlySummaryListDTO> page,
                                                     QueryDeclareEntYearlySummaryDTO dto) {
        List<DeclareEntYearlySummaryListDTO> list = mapper.page(page, dto);
        page.setRecords(list);
        return page;
    }

    private DeclareEntYearlySummary getEntityFromDatabase(String projectId) {
        return Optional.ofNullable(mapper.selectOne(Wrappers.<DeclareEntYearlySummary>lambdaQuery()
                .eq(DeclareEntYearlySummary::getProjectId, projectId)
                .last("LIMIT 1")))
                .orElseGet(DeclareEntYearlySummary::new);
    }
}
