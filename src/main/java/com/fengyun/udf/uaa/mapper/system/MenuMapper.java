package com.fengyun.udf.uaa.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.domain.system.Menu;
import com.fengyun.udf.uaa.dto.response.system.MenuTreeDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wangg on 2019/7/5.
 */
@Repository
public interface MenuMapper extends BaseMapper<Menu> {
    List<MenuTreeDTO> selectListOrderBySeq(final @Param("isOperation") String isOperation, final @Param("menuBelong") String menuBelong, @Param("appIds") List<String> appIds, @Param("clientType") String clientType);

    List<MenuTreeDTO> selectOperationListOrderBySeq(final @Param("isOperation") String isOperation, final @Param("menuBelong") String menuBelong);

    List<String> selectMenuPermissionByUser(final @Param("userId") String userId);

    List<MenuTreeDTO> selectOperationList(@Param("roleIds")List<String> roleIds);
}
