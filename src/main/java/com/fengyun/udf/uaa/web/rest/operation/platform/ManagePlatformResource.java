package com.fengyun.udf.uaa.web.rest.operation.platform;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.system.PlatformFieldQueryDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldListDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldUpdateDTO;
import com.fengyun.udf.uaa.service.system.PlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/platform")
@Api(value = "平台管理", tags = "平台管理")
public class ManagePlatformResource extends BaseResource {

    @Autowired
    private PlatformService platformService;

    @GetMapping("/attribute/list")
    @CustomBaseException
    @AuditLog(method = "/api/platform/attribute/list", methodDescription = "平台管理属性列表", httpMethod = "GET")
    @ApiOperation(value = "平台管理属性列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> attributeList() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), platformService.getAttributeList());
    }

    @PostMapping("/attribute/save")
    @CustomBaseException
    @AuditLog(method = "/api/platform/attribute/save", methodDescription = "平台管理属性保存", httpMethod = "POST")
    @ApiOperation(value = "平台管理属性保存")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> saveAttribute(@RequestBody Map<String,String> map) {
        platformService.saveAttribute(map);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/field/list")
    @CustomBaseException
    @AuditLog(method = "/api/platform/field/list", methodDescription = "平台管理字段列表", httpMethod = "GET")
    @ApiOperation(value = "平台管理字段列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<List<PlatformFieldListDTO>>> attributeList(PlatformFieldQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), platformService.getFieldList(dto));
    }

    @PostMapping("/field/create")
    @CustomBaseException
    @AuditLog(method = "/api/platform/field/create", methodDescription = "新增平台管理字段", httpMethod = "POST")
    @ApiOperation(value = "新增平台管理字段")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> createPlatformField(@Validated @RequestBody PlatformFieldDTO dto) {
        platformService.createPlatformField(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/field/update")
    @CustomBaseException
    @AuditLog(method = "/api/platform/field/update", methodDescription = "修改平台管理字段", httpMethod = "POST")
    @ApiOperation(value = "修改平台管理字段")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> updatePlatformField(@Validated @RequestBody PlatformFieldUpdateDTO dto) {
        platformService.updatePlatformField(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/field/delete/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/platform/field/delete/{id}", methodDescription = "删除平台管理字段", httpMethod = "GET")
    @ApiOperation(value = "删除平台管理字段")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<List<PlatformFieldListDTO>>> deletePlatformField(@NotNull(message = "id必填") @PathVariable Integer id) {
        platformService.deletePlatformField(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    /**
     * 老接口兼容性保留
     */
    @PostMapping("/attribute/save/update")
    @CustomBaseException
    @AuditLog(method = "/api/platform/attribute/save/update", methodDescription = "平台管理属性保存", httpMethod = "POST")
    @ApiOperation(value = "平台管理属性保存")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> saveUpdateAttribute(@RequestBody Map<String,String> map) {
        platformService.saveAttribute(map);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
}
