package com.fengyun.udf.uaa.dto.response.expertpanel;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DateFormat;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.ZonedDateTime;
import java.util.List;

@ApiModel("专家团")
@Data
@EqualsAndHashCode(callSuper = false)
public class ExpertPanelDTO extends AbstractDTO {
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 专家团名称
     */
    @ApiModelProperty(value = "专家团名称")
    private String name;

    /**
     * 成员
     */
    @ApiModelProperty(value = "成员")
    private String member;

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    private String memberName;

    @ApiModelProperty(value = "状态，0-未关联，1-已关联")
    @DictData(codeType = "EXPERT_PANEL_STATUS", target = "statusStr")
    private String status;
    private String statusStr;

    @ApiModelProperty(value="创建日期")
    private ZonedDateTime createdDate;
    @ApiModelProperty(value="创建日期")
    @DateFormat(property = "createdDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createdDateStr;
}
