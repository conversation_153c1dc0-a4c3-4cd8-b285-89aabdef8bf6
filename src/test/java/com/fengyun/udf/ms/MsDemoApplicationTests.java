package com.fengyun.udf.ms;

import com.fengyun.udf.uaa.UaaApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = UaaApplication.class)
@ActiveProfiles("local")
public class MsDemoApplicationTests {

    @Autowired
    protected JavaMailSender javaMailSender;



    @Test
    public void test() {
        final SimpleMailMessage simpleMailMessage = new SimpleMailMessage();
        // 设置发送人
        simpleMailMessage.setFrom("<EMAIL>");
        // 设置标题
        simpleMailMessage.setSubject("明细确认");
        String mailContent = "%s您好，租赁仪器使用明细已经生成，请您到国家生物技术创新中心平台（https://www.nctib.org.cn/）-个人空间-明细确认模块中，确认近期使用明细，若超过三天未确认系统将自动确认。若有疑问，请电话联系%s。";
        mailContent = String.format(mailContent,"大佬","18896735932");
        // 设置文件内容
        simpleMailMessage.setText(mailContent);
        // 设置发收件人
        simpleMailMessage.setTo("<EMAIL>");
        // 设置发送时间
        simpleMailMessage.setSentDate(new Date());
        // 发送邮件
        javaMailSender.send(simpleMailMessage);
    }

    public static void main(String[] args) {
        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        System.out.println(passwordEncoder.encode("123@abcd"));
    }

}
