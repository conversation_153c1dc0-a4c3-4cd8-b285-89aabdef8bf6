package com.fengyun.udf.uaa.web.rest.declare;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declare.QueryDeclareEntYearlySummaryDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryListDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryDetailDTO;
import com.fengyun.udf.uaa.service.declare.DeclareEntYearlySummaryService;
import com.fengyun.udf.uaa.service.declareproject.DeclareProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <p>
 *  Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@RestController
@Slf4j
@Validated
@SuppressWarnings("all")
@RequiredArgsConstructor
@RequestMapping("/api/platform/declare/yearly-summary")
@Api(value = "管理端-年度总结", tags = "管理端-年度总结")
@PreAuthorize("@pms.hasPermission('management')")
public class ManagerDeclareEntYearlySummaryResource extends BaseResource {

    @NonNull
    private DeclareEntYearlySummaryService service;

    @NonNull
    private DeclareProjectService declareProjectService;

    @GetMapping("/initiate")
    @AuditLog(
            method = "/api/platform/declare/yearly-summary/initiate",
            methodDescription = "年度总结-发起",
            httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "父项目id, declareId", required = true, dataType = "String", paramType = "query"),
    })
    @ApiOperation(value = "年度总结-发起")
    public ResponseEntity<ReturnResultDTO<?>> toEndInspectionStage(String id) {
        declareProjectService.sendYearlySummary(id);
        return prepareReturnResult();
    }

    @GetMapping("/detail")
    @AuditLog(method = "/api/platform/declare/yearly-summary/detail", methodDescription = "详情", httpMethod = "GET")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "子项目ID", required = true, dataType = "String", paramType = "query"),
    })
    public ResponseEntity<ReturnResultDTO<DeclareEntYearlySummaryDetailDTO>> detail(
            @Valid @NotBlank(message = "projectId不能为空") String projectId) {
        return prepareReturnResult(service.detailByProjectIdPlatform(projectId));
    }

    @GetMapping("/page")
    @AuditLog(method = "/api/platform/declare/yearly-summary/page", methodDescription = "分页列表", httpMethod = "GET")
    @ApiOperation(value = "列表")
    public ResponseEntity<ReturnResultDTO<Page<DeclareEntYearlySummaryListDTO>>> page(
            Page<DeclareEntYearlySummaryListDTO> page,
            @ModelAttribute QueryDeclareEntYearlySummaryDTO dto) {
        return prepareReturnResult(service.page(page, dto));
    }

}
