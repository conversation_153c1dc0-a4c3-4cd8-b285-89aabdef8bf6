package com.fengyun.udf.uaa.service.area;


import com.fengyun.udf.cache.redis.client.AreaCacheClient;
import com.fengyun.udf.uaa.domain.area.AreaWorld;
import com.fengyun.udf.uaa.dto.response.area.AreaWorldDTO;
import com.fengyun.udf.uaa.mapper.area.AreaWorldMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Transactional
public class AreaWorldService {
    @Autowired
    private AreaWorldMapper areaWorldMapper;

    @Autowired
    private AreaCacheClient areaCacheClient;

    public static final String DATA_TYPE_WORLD = "world";

    public List<AreaWorldDTO> searchStates(final String continentId) {
        List<AreaWorld> tempResult;
        if(!StringUtils.isEmpty(continentId)){
            tempResult = areaWorldMapper.findAllByLevelAndParentIdAndStatusNotOrderBySequenceAsc(2,continentId,"D");
        }else{
            tempResult = areaWorldMapper.findAllByLevelAndStatusNotOrderBySequenceAsc(2,"D");
        }
        return prepareAreaWorld(tempResult);
    }

    private List<AreaWorldDTO> prepareAreaWorld(List<AreaWorld> areas){
        List<AreaWorldDTO> result = new ArrayList<>();
        if(areas != null && areas.size() > 0){
            for(AreaWorld area : areas){
                result.add(prepareAreaWorld(area));
            }
        }
        return result;
    }

    private AreaWorldDTO prepareAreaWorld(AreaWorld area){
        AreaWorldDTO dto = new AreaWorldDTO();
        BeanUtils.copyProperties(area, dto);

        return dto;
    }

    public List<AreaWorldDTO> searchContinents() {
        return prepareAreaWorld(areaWorldMapper.findAllByLevelAndStatusNotOrderBySequenceAsc(1,"D"));
    }

    private void putWorldIntoRedis(AreaWorld world){
        areaCacheClient.add(DATA_TYPE_WORLD, world.getId(), world.getNameCn());
    }

    public void initCacheData(){
        List<AreaWorld> all = areaWorldMapper.findAll();
        if(all != null && all.size() > 0){
            for(AreaWorld world : all){
                putWorldIntoRedis(world);
            }
        }
    }
}
