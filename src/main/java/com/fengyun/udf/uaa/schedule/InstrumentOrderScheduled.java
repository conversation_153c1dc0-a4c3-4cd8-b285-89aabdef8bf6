package com.fengyun.udf.uaa.schedule;

import com.fengyun.udf.uaa.service.instrument.InstrumentOrderDetailConfirmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InstrumentOrderScheduled {

    @Autowired
    private InstrumentOrderDetailConfirmService instrumentOrderDetailConfirmService;

    /**
     * 订单明细确认，3天自动确认
     */
    @Scheduled(cron = "0 0 10 * * ? ")
    public void autoConfirm() {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>  每天定时订单明细确认 start >>>>>>>>>>>>>>>>>>>>>>>>>>");
        instrumentOrderDetailConfirmService.autoConfirm();

        log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<  每天定时订单明细确认 end <<<<<<<<<<<<<<<<<<<<<<<<<<<");
    }
}
