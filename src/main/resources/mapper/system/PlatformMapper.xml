<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.system.PlatformMapper">
    <update id="saveAttribute">
        update t_platform set `value` = null ;
        <foreach collection="map.entrySet()" item="value" index="name">
            update t_platform
            set value = #{value}
            where name = #{name};
        </foreach>
    </update>

    <update id="updateAttribute">
        update t_platform
        set value = #{value}
        where name = #{name}
    </update>

    <select id="getPlatformFieldList" resultType="com.fengyun.udf.uaa.dto.response.system.PlatformFieldListDTO">
        SELECT * FROM
        t_platform p
        WHERE 1=1
        <if test="query.name != null and query.name != ''">
            AND p.name LIKE CONCAT('%',#{query.name},'%')
        </if>
        <if test="query.title != null and query.title != ''">
            AND p.title LIKE CONCAT('%',#{query.title},'%')
        </if>
        <if test="query.type != null and query.type != ''">
            AND p.type = #{query.type}
        </if>
        ORDER BY
        p.sort
    </select>
</mapper>
