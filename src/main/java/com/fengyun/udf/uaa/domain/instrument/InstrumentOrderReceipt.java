package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仪器预约收据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
@TableName("in_instrument_order_receipt")
public class InstrumentOrderReceipt extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private Integer id;

    /**
     * 交款单位
     */
    @TableField("pay_company")
    private String payCompany;

    /**
     * 收款事由
     */
    @TableField("pay_reason")
    private String payReason;

    /**
     * 收款方式
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 收款时间
     */
    @TableField("pay_date")
    private String payDate;

    /**
     * 收款金额（元）
     */
    @TableField("pay_amount")
    private Double payAmount;

    /**
     * 联系人
     */
    @TableField("contact")
    private String contact;

    /**
     * 联系方式
     */
    @TableField("contact_tel")
    private String contactTel;

    /**
     * 收据文件
     */
    @TableField("receipt_file")
    private String receiptFile;

    /**
     * 盖章收据
     */
    @TableField("receipt_stamp_file")
    private String receiptStampFile;

    /**
     * 工单发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;


}
