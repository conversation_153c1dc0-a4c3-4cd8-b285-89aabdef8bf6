package com.fengyun.udf.uaa.config.websocket;

import com.fengyun.udf.dto.AbstractDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Map;

/**
 * Created by gongzuming on 2020/3/23.
 */
@Data
public class ReceiveMessage extends AbstractDTO {

    @ApiModelProperty(value = "用户ip")
    private String ip;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "记录时间")
    private ZonedDateTime recordTime;

    @ApiModelProperty(value = "参数")
    private Map<String,Object> params;

    @ApiModelProperty(value = "页面url或者api地址")
    private String url;

    @ApiModelProperty(value = "行为类型（字典类型：SYS_BEHAVIOR_TYPE）")
    private String behaviorType;

    @ApiModelProperty(value = "行为描述")
    private String behaviorDesc;
}
