package com.fengyun.udf.uaa.fastdfs.client;

import java.io.Serializable;

/**
 * Created by dong on 2017/5/2.
 */
public class UploadManagerConfig implements Serializable {


    public static final String FILE_DEFAULT_WIDTH = "120";
    public static final String FILE_DEFAULT_HEIGHT = "120";
    public static final String FILE_DEFAULT_AUTHOR = "cube";

    public static final String PROTOCOL = "http://";
    public static final String SEPARATOR = "/";

    public static final String TRACKER_NGNIX_PORT = "80";

    public static final String CLIENT_CONFIG_FILE = "fdfs_client.conf";

    public static final String SUCCESS = "SUCCESS";
    public static final String ERROR_TYPE = "不允许的文件格式";
    public static final String ERROR_SIZE = "文件大小超出限制";
    public static final String ERROR_UNKNOWN = "上传出错";

}
