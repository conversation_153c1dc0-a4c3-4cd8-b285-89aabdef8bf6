<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.expertpanel.ExpertPanelMapper">

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertPanelDTO">
        select * from t_expert_panel
        <where>
            <if test="params.keyWord != null and params.keyWord != ''">
                and name like CONCAT('%', #{params.keyWord}, '%')
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status}
            </if>
        </where>
        order by created_date desc
    </select>

    <select id="getExpertGroupList" resultType="com.fengyun.udf.uaa.dto.response.expertpanel.ExpertPanelDTO">
        select * from t_expert_panel order by created_date desc
    </select>

</mapper>