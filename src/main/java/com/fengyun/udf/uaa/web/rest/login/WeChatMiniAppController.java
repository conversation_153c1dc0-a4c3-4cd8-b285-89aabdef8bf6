package com.fengyun.udf.uaa.web.rest.login;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.cache.redis.client.StringCacheClient;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.security.token.ThirdAuthenticationToken;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.domain.system.UserThird;
import com.fengyun.udf.uaa.dto.request.miniapp.MiniAppLoginDto;
import com.fengyun.udf.uaa.dto.request.miniapp.MiniAppLoginExpertDto;
import com.fengyun.udf.uaa.service.login.LoginService;
import com.fengyun.udf.uaa.service.register.RegisterService;
import com.fengyun.udf.uaa.service.system.UserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/mini-app")
@Slf4j
public class WeChatMiniAppController extends BaseResource {

    private AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new WebAuthenticationDetailsSource();

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    protected PasswordEncoder passwordEncoder;

    @Autowired
    private StringCacheClient stringCacheClient;

    @Autowired
    private UserService userService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private RegisterService registerService;

    @GetMapping("/open-id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "登录凭证", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "微信小程序登录获取openid")
    public ResponseEntity<ReturnResultDTO> openId(@NotBlank(message = "code不能为空") String code) throws WxErrorException {
        WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
        stringCacheClient.set(Constants.WECHAT_MINI_APP_SESSION_KEY + session.getOpenid(), session.getSessionKey(), 24 * 60);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), session.getOpenid());
    }

    @PostMapping("/login")
    @ApiOperation(value = "微信小程序登录")
    @AuditLog(method = "/mini-app/login", methodDescription = "微信小程序登录s", httpMethod = "POST", storageMode = {"file", "mysql"})
    public ResponseEntity<ReturnResultDTO> login(@RequestBody MiniAppLoginDto miniAppLoginDto, HttpServletRequest request
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        try {
            String sessionKey = stringCacheClient.get(Constants.WECHAT_MINI_APP_SESSION_KEY + miniAppLoginDto.getOpenid());
            if (sessionKey == null) {
                throw new ValidationException("openid", "no_sessionKey");
            }
            WxMaUserInfo wxMaUserInfo = wxMaService.getUserService().getUserInfo(sessionKey, miniAppLoginDto.getEncryptedData(), miniAppLoginDto.getIv());

            String unionId = wxMaUserInfo.getUnionId();

            if (StringUtils.isNotBlank(miniAppLoginDto.getTel()) && StringUtils.isNotBlank(miniAppLoginDto.getSmsCaptchaCode())) {
                if (!registerService.checkSmsCaptchaCode(miniAppLoginDto.getTel(), miniAppLoginDto.getSmsCaptchaCode())) {
                    throw new ValidationException("smsCaptchaCode", "短信验证码不正确");
                }

                List<String> userTypes = new ArrayList<>();
                userTypes.add(UserType.member.name());
                User user = userService.findUserByLoginNameOrTel(miniAppLoginDto.getTel(), userTypes);
                if (user == null) {
                    user = registerService.registerUser(miniAppLoginDto.getTel(), unionId, "WECHAT");
                    registerService.removeSmsCaptchaCodeFromCache(miniAppLoginDto.getTel());
                }else{
                    userService.bindThird(user.getId(), unionId, "WECHAT");
                }
            } else {
                User user = userService.findByOpenIdAndType(unionId, "WECHAT");
                if (user == null) {
                    throw new ValidationException("openid", "no_unionId");
                }
            }
            ThirdAuthenticationToken authRequest = new ThirdAuthenticationToken(unionId, "WECHAT", "");
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), loginService.getAccessToken(authentication, clientType));
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "error.login.client");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }

    @PostMapping("/expert-login")
    @ApiOperation(value = "专家小程序登录")
    @AuditLog(method = "/expert-app/login", methodDescription = "专家小程序登录", httpMethod = "POST", storageMode = {"file", "mysql"})
    public ResponseEntity<ReturnResultDTO> expertLogin(@RequestBody MiniAppLoginExpertDto miniAppLoginDto, HttpServletRequest request
            , @RequestHeader(value = "clientType", required = false) String clientType) {
        try {
            UserThird ud = userService.findByUserIdAndType(miniAppLoginDto.getUserId(),"WECHAT");
            if(ud != null ){
                throw new ValidationException("error", "用户已绑定");
            }
            String sessionKey = null;
            if(StringUtils.isNotBlank(miniAppLoginDto.getCode())){
                WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(miniAppLoginDto.getCode());
                sessionKey = session.getSessionKey();
                stringCacheClient.set(Constants.WECHAT_MINI_APP_SESSION_KEY + session.getOpenid(), session.getSessionKey(), 24 * 60);
            }else{
                sessionKey = stringCacheClient.get(Constants.WECHAT_MINI_APP_SESSION_KEY + miniAppLoginDto.getOpenid());
                if (sessionKey == null) {
                    throw new ValidationException("openid", "no_sessionKey");
                }
            }
            WxMaUserInfo wxMaUserInfo = wxMaService.getUserService().getUserInfo(sessionKey, miniAppLoginDto.getEncryptedData(), miniAppLoginDto.getIv());

            String unionId = miniAppLoginDto.getOpenid();
            User user = userService.findByOpenIdAndType(unionId, "WECHAT");
            if (user == null) {
                userService.bindThird(miniAppLoginDto.getUserId(), unionId, "WECHAT");
            } else if(!user.getId().equals(miniAppLoginDto.getUserId())) {
                throw new ValidationException("error", "用户已绑定");
            }
            ThirdAuthenticationToken authRequest = new ThirdAuthenticationToken(unionId, "WECHAT", "");
            authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
            Authentication authentication = authenticationManager.authenticate(authRequest);
            return prepareReturnResult(ReturnCode.SUCCESS.getCode(), loginService.getAccessToken(authentication, clientType));
        } catch (ClientRegistrationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("client", "error.login.client");
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new ValidationException("", e.getMessage());
        }
    }
}
