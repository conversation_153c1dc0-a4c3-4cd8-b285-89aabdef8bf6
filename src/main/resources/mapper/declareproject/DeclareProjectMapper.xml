<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.declareproject.DeclareProjectMapper">
   <select id="list" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareProjectShowDTO">
      SELECT
          *
      FROM
          (
              SELECT
                  t_declare_project.*,  IFNULL((
                      SELECT
                          ent_status
                      FROM
                          t_declare_ent
                      WHERE
                          t_declare_ent.declare_id = t_declare_project.id
                      AND t_declare_ent.created_id = #{condition.userId}
                      ORDER BY
                          t_declare_ent.created_date DESC
                      LIMIT 1
                  ),'PENDING') AS declareStatus,
                  (
                       SELECT
                        t_declare_ent.id
                       FROM
                        t_declare_ent
                       WHERE
                        t_declare_ent.declare_id = t_declare_project.id
                       AND t_declare_ent.created_id = #{condition.userId}
                       ORDER BY
                       t_declare_ent.created_date DESC
                       LIMIT 1
                       ) AS declareEntId
              FROM
                  t_declare_project
          ) m
      where 1=1
        <if test="condition.declareStatus != null and condition.declareStatus != ''">
        and  m.declareStatus = #{condition.declareStatus}
        </if>
        <if test="condition.keyWord != null and condition.keyWord != ''">
        and   m.declare_name like concat('%',#{condition.keyWord},'%')
        </if>
        <if test="condition.publicFlag != null and condition.publicFlag != ''">
        and  m.public_flag = #{condition.publicFlag}
        </if>
       order by m.created_date ASC
   </select>

    <select id="page" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareProjectShowDTO">
        SELECT
            a.id AS id,
            a.declare_name AS declareName,
            a.flow_id AS flowId,
            a.declare_begin_time AS declareBeginTime,
            a.declare_end_time AS declareEndTime,
            a.content AS content,
            a.file_content AS fileContent,
            a.attachments AS attachments,
            a.status AS status,
            a.top AS top,
            a.submit_status AS submitStatus,
            a.public_flag AS publicFlag,
            a.expert_score_template_id AS expertScoreTemplateId,
            a.created_id AS createdId,
            a.updated_id AS updateId,
            a.created_date AS createdDate,
            a.updated_date AS updatedDAte,
            a.review_template AS reviewTemplates,
            a.project_status AS projectStatus,
            a.project_no_prefix AS projectNoPrefix,
            a.second_expert_score_template_id AS secondExpertScoreTemplateId,
            a.yearly_summary AS yearlySummary,
            COUNT(tde.id) AS declareEntNums
        FROM
            t_declare_project a
        LEFT JOIN t_declare_ent tde ON tde.declare_id = a.id
        <where>
            <if test="params.keyWord != null and params.keyWord != ''">
                and a.declare_name like CONCAT('%', #{params.keyWord}, '%')
            </if>
            <if test="params.policyLevel != null and params.policyLevel != ''">
                and a.policy_level = #{params.policyLevel}
            </if>
            <if test="params.declareStatus != null and params.declareStatus != ''">
                <if test='params.declareStatus == "1"'>
                    and a.declare_begin_time <![CDATA[<=]]> CURRENT_DATE and declare_end_time >= CURRENT_DATE
                </if>
                <if test='params.declareStatus == "2"'>
                    and a.declare_end_time <![CDATA[<]]> CURRENT_DATE
                </if>
                <if test='params.declareStatus == "3"'>
                    and a.declare_begin_time > now()
                </if>
            </if>

            <if test="params.declareEndTime != null and params.declareEndTime != ''">
                and a.declare_begin_time <![CDATA[<=]]> #{params.declareEndTime}
            </if>
            <if test="params.declareBeginTime != null and params.declareBeginTime != ''">
                and a.declare_end_time >= #{params.declareBeginTime}
            </if>
            <if test="params.publishBeginTime != null and params.publishBeginTime != ''">
                and a.created_date >= #{params.publishBeginTime}
            </if>
            <if test="params.publishEndTime != null and params.publishEndTime != ''">
                and a.created_date <![CDATA[<=]]> #{params.publishEndTime}
            </if>
            <if test="params.status != null and params.status != ''">
                and a.status = #{params.status}
            </if>
            <if test="params.submitStatus != null and params.submitStatus != ''">
                and a.submit_status = #{params.submitStatus}
            </if>
            <if test="params.submitStatus == null">
                and a.submit_status != '0'
            </if>
            <if test="params.publicFlag != null and params.publicFlag != ''">
                and a.public_flag = #{params.publicFlag}
            </if>
            <if test="params.projectStatus != null and params.projectStatus != ''">
                and a.project_status = #{params.projectStatus}
            </if>
            <if test="params.projectStatusGe != null and params.projectStatusGe != ''">
                and a.project_status >= #{params.projectStatusGe}
            </if>
        </where>
        GROUP BY a.id,a.top,a.created_date
        ORDER BY a.top DESC,a.created_date DESC
    </select>

    <select id="getDeclareList" resultType="com.fengyun.udf.uaa.dto.response.declareproject.DeclareProjectShowDTO">
        select id,declare_name from t_declare_project
    </select>

    <select id="getTemplateById" resultType="Integer">
        select count(1) from t_declare_project where id!=#{id} and expert_score_template_id=#{oldExpertScoreTemplateId}
    </select>

</mapper>
