package com.fengyun.udf.uaa.dto.response.inspection.middle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 2025/2/13
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DeclareEntMiddleFundingRemarkDetailDTO {

    /**
     * 科目名称
     */
    @ApiModelProperty("科目名称")
    private String subjectName;

    /**
     * 明细
     */
    @ApiModelProperty("明细")
    private String detail;


    /**
     * 金额（万元）
     */
    @ApiModelProperty("金额（万元）")
    private BigDecimal amount;

    /**
     * 收款单位
     */
    @ApiModelProperty("收款单位")
    private String payee;

}
