package com.fengyun.udf.uaa.domain.inspection.end;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "declare_ent_end_completion")
@TableName(value = "declare_ent_end_completion", autoResultMap = true)
public class DeclareEntEndCompletion extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 第一阶段时间
     */
    @ApiModelProperty("第一阶段时间")
    @TableField("first_stage_date")
    private String firstStageDate;

    /**
     * 第一阶段进度安排
     */
    @ApiModelProperty("第一阶段进度安排")
    @TableField("first_schedule")
    private String firstSchedule;

    /**
     * 第一阶段完成情况
     */
    @ApiModelProperty("第一阶段完成情况")
    @TableField("first_completion")
    private String firstCompletion;

    /**
     * 第二阶段时间
     */
    @ApiModelProperty("第二阶段时间")
    @TableField("second_stage_date")
    private String secondStageDate;

    /**
     * 第二阶段进度安排
     */
    @ApiModelProperty("第二阶段进度安排")
    @TableField("second_schedule")
    private String secondSchedule;

    /**
     * 第二阶段完成情况
     */
    @ApiModelProperty("第二阶段完成情况")
    @TableField("second_completion")
    private String secondCompletion;

    /**
     * 第三阶段时间
     */
    @ApiModelProperty("第三阶段时间")
    @TableField("third_stage_date")
    private String thirdStageDate;

    /**
     * 第三阶段进度安排
     */
    @ApiModelProperty("第三阶段进度安排")
    @TableField("third_schedule")
    private String thirdSchedule;

    /**
     * 第三阶段完成情况
     */
    @ApiModelProperty("第三阶段完成情况")
    @TableField("third_completion")
    private String thirdCompletion;

}
