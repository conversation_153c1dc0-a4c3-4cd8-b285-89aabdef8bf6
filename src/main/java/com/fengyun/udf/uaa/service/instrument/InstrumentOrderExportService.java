package com.fengyun.udf.uaa.service.instrument;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.interceptor.CodeDecorator;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.instrument.InstrumentOrderExport;
import com.fengyun.udf.uaa.domain.system.DictValue;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportSendDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderExportUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportListDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportUserListDTO;
import com.fengyun.udf.uaa.fastdfs.ClientGlobal;
import com.fengyun.udf.uaa.fastdfs.client.UploadFile;
import com.fengyun.udf.uaa.fastdfs.client.UploadManager;
import com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderExportMapper;
import com.fengyun.udf.uaa.mapper.instrument.InstrumentOrderMapper;
import com.fengyun.udf.uaa.mapper.system.UserMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.system.DictService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class InstrumentOrderExportService extends BaseService {

    private final InstrumentOrderExportMapper instrumentOrderExportMapper;
    private final InstrumentOrderMapper instrumentOrderMapper;
    private final DictService dictService;
    private final CodeDecorator codeDecorator;
    private final UserMapper userMapper;

    public void create(InstrumentOrderExportCreateDTO dto) throws Exception {
        InstrumentOrderExport instrumentOrderExport = new InstrumentOrderExport();
        BeanUtils.copyProperties(dto, instrumentOrderExport);
        instrumentOrderExport.setIsSend(false);
        instrumentOrderExport.setIsConfirm(false);
        instrumentOrderExport.create(SecurityUtils.getUserId());

        //查询预约明细
        Double sumAmount = 0d;
        List<DictValue> timeList = dictService.getByCode("INSTRUMENT_ORDER_TIME");
        Map<String, String> timeMap = timeList.stream().collect(Collectors.toMap(DictValue::getValue, DictValue::getLabel));

        boolean isHaveConsumableField = dto.getExportField().contains("consumableName") || dto.getExportField().contains("consumableNum") || dto.getExportField().contains("consumableUnitPrice");

        List<InstrumentOrderExportDTO> list = instrumentOrderMapper.selectExportList(dto);
        List<InstrumentOrderExportDTO> result = new ArrayList<>();
        List<String> mergeIndexList = new ArrayList<>();
        int index = 1;
        for (InstrumentOrderExportDTO exportDTO : list) {
            sumAmount = exportDTO.getAmount() + sumAmount;
            exportDTO.setUserType(exportDTO.getUserType());
            exportDTO.setUserName(exportDTO.getUserName());

            codeDecorator.decorate(exportDTO);

            if (exportDTO.getLeasePrice() != null) {
                exportDTO.setLeasePriceStr(exportDTO.getLeasePrice().toString());
            }
            if (exportDTO.getLeaseFace()) {
                exportDTO.setLeasePriceStr("面议");
            }
            if (exportDTO.getTestPrice() != null) {
                exportDTO.setTestPriceStr(exportDTO.getTestPrice().toString());
            }
            if (exportDTO.getTestFace()) {
                exportDTO.setTestPriceStr("面议");
            }
            exportDTO.setSkilledUseStr("否");
            if (exportDTO.getSkilledUse()) {
                exportDTO.setSkilledUseStr("是");
            }
            if (exportDTO.getSignAgreement() == null) {
                exportDTO.setSignAgreementStr("");
            } else {
                exportDTO.setSignAgreementStr("否");
                if (exportDTO.getSignAgreement()) {
                    exportDTO.setSignAgreementStr("是");
                }
            }

            String exportOrderTimeStr = exportDTO.getOrderDate().toString();
            List<String> times = new ArrayList<>();
            for (String s : exportDTO.getOrderTime().split(",")) {
                times.add(timeMap.get(s));
            }
            exportOrderTimeStr = exportOrderTimeStr + " " + String.join(",", times);

            exportDTO.setExportOrderTimeStr(exportOrderTimeStr);

            if (isHaveConsumableField) {
                List<InstrumentOrderExportDTO> orderConsumableList = instrumentOrderMapper.selectOrderConsumable(exportDTO.getId());
                if (CollectionUtils.isNotEmpty(orderConsumableList)) {
                    InstrumentOrderExportDTO dto0 = orderConsumableList.get(0);
                    exportDTO.setConsumableName(dto0.getConsumableName());
                    exportDTO.setConsumableNum(dto0.getConsumableNum());
                    exportDTO.setConsumableUnitPrice(dto0.getConsumableUnitPrice());
                    result.add(exportDTO);
                    index++;
                    if (orderConsumableList.size() > 1) {
                        mergeIndexList.add(index - 1 + "," + orderConsumableList.size());
                        for (int i = 1; i < orderConsumableList.size(); i++) {
                            result.add(orderConsumableList.get(i));
                            index++;
                        }
                    }
                } else {
                    result.add(exportDTO);
                    index++;
                }
            } else {
                result.add(exportDTO);
            }

        }
        instrumentOrderExport.setAmount(new BigDecimal(sumAmount).setScale(2, RoundingMode.HALF_UP).doubleValue());
        instrumentOrderExport.setExcelFile(JSON.toJSONString(excel(dto, result, isHaveConsumableField, mergeIndexList)));
        instrumentOrderExportMapper.insert(instrumentOrderExport);
    }

    private AttachmentDTO excel(InstrumentOrderExportCreateDTO dto, List list, boolean isHaveConsumableField, List<String> mergeIndexList) throws Exception {
        ExcelWriter writer = ExcelUtil.getWriter(true);

        List<DictValue> dictValues = dictService.getByCode("INSTRUMENT_ORDER_EXPORT_FIELD");
        Map<String, String> dictMap = dictValues.stream().collect(Collectors.toMap(DictValue::getValue, DictValue::getLabel));

        List<Integer> indexList = new ArrayList<>();
        int index = 0;
        for (String s : dto.getExportField().split(",")) {
            writer.addHeaderAlias(s, dictMap.get(s));
            if ("consumableName".equals(s)) {
                indexList.add(index);
            }
            if ("consumableNum".equals(s)) {
                indexList.add(index);
            }
            if ("consumableUnitPrice".equals(s)) {
                indexList.add(index);
            }
            index++;
        }

        writer.setOnlyAlias(true);
        writer.write(list, true);
        if (isHaveConsumableField && CollectionUtils.isNotEmpty(mergeIndexList)) {
            for (int i = 0; i < index; i++) {
                if (!indexList.contains(i)) {
                    for (String s : mergeIndexList) {
                        int startRow = Integer.parseInt(s.split(",")[0]);
                        int endRow = startRow + Integer.parseInt(s.split(",")[1]) - 1;
                        writer.merge(startRow, endRow, i, i, null, false);
                    }
                }
            }
        }

        String fileName = "明细导出表格.xlsx";
        byte[] bytes;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        writer.flush(out, true);
        bytes = out.toByteArray();

        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);

        // 上传文件
        UploadFile uploadfile = new UploadFile("明细导出表格", bytes, "xlsx");
        String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
        // 保存并返回生成的模板
        AttachmentDTO attachmentDTO = new AttachmentDTO();
        attachmentDTO.setUrl(ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
        attachmentDTO.setSize((long) bytes.length);
        attachmentDTO.setName(fileName);

        return attachmentDTO;
    }

    public void update(InstrumentOrderExportUpdateDTO dto) {
        InstrumentOrderExport instrumentOrderExport = instrumentOrderExportMapper.selectById(dto.getId());
        if (instrumentOrderExport == null) {
            throw new ValidationException("error", "不存在");
        }
        if (instrumentOrderExport.getIsSend()) {
            throw new ValidationException("error", "已发送客户，无法修改");
        }
        instrumentOrderExport.setAmount(dto.getAmount());
        instrumentOrderExport.update(SecurityUtils.getUserId());

        instrumentOrderExportMapper.updateById(instrumentOrderExport);
    }

    public InstrumentOrderExportDetailDTO detail(String id, boolean isManager) {
        InstrumentOrderExportDetailDTO dto = new InstrumentOrderExportDetailDTO();
        InstrumentOrderExport instrumentOrderExport = instrumentOrderExportMapper.selectById(id);
        if (instrumentOrderExport == null) {
            throw new ValidationException("error", "不存在");
        }
        BeanUtils.copyProperties(instrumentOrderExport, dto);

        //企业端确认
        if (!isManager && !instrumentOrderExport.getIsConfirm()) {
            instrumentOrderExport.setIsConfirm(true);
            instrumentOrderExport.setConfirmTime(LocalDateTime.now());
            instrumentOrderExportMapper.updateById(instrumentOrderExport);
        }
        return dto;
    }

    public Page<InstrumentOrderExportListDTO> page(Page page, InstrumentOrderExportQueryDTO dto) {
        List<InstrumentOrderExportListDTO> list = instrumentOrderExportMapper.selectPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public void delete(String id) {
        instrumentOrderExportMapper.deleteById(id);
    }

    public void send(InstrumentOrderExportSendDTO dto) {
        InstrumentOrderExport instrumentOrderExport = instrumentOrderExportMapper.selectById(dto.getId());
        if (instrumentOrderExport == null) {
            throw new ValidationException("error", "不存在");
        }
        if (instrumentOrderExport.getIsSend()) {
            throw new ValidationException("error", "已发送客户");
        }
        instrumentOrderExport.setIsSend(true);
        instrumentOrderExport.setSendTime(LocalDateTime.now());
        instrumentOrderExport.setContactEmail(dto.getContactEmail());
        instrumentOrderExport.setContactTel(dto.getContactTel());
        instrumentOrderExport.update(SecurityUtils.getUserId());

        instrumentOrderExportMapper.updateById(instrumentOrderExport);

        String tel = dictCacheClient.getDict("INSTRUMENT_ORDER_DETAIL_CONFIRM_TEL", "tel");
        if (StringUtils.isBlank(tel)) {
            tel = Constants.CONTACT_TEL;
        }

        //短信
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("tel", tel);
        paramMap.put("userName", instrumentOrderExport.getUserName());
        try {
            sendSms(dto.getContactTel(), paramMap, smsProperties.getOrderWork());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        //邮箱
        String mailContent = "%s您好，租赁仪器使用明细已经生成，请您到国家生物技术创新中心平台（https://www.nctib.org.cn/）-个人空间-明细确认模块中，确认近期使用明细，若超过三天未确认系统将自动确认。若有疑问，请电话联系%s。";
        mailContent = String.format(mailContent, instrumentOrderExport.getUserName(), tel);
        try {
            sendMail(dto.getContactEmail(), mailContent, "明细确认");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public List<InstrumentOrderExportUserListDTO> userList(Integer userType) {
        return userMapper.queryOrderUserPage(userType);
    }


}

