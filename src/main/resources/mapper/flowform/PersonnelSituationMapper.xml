<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.flowform.PersonnelSituationMapper">

    <select id="selectPersonList" resultType="Map">
        select a.subject_name,c.responsible_name,b.project_participants from flow_form_condition a
        left join flow_form_personnel_situation b on a.project_id=b.project_id and a.CREATED_ID=b.CREATED_ID
        left join flow_form_responsible_unit c on a.project_id=c.project_id and a.CREATED_ID=c.CREATED_ID
        where b.project_participants is not null and b.project_participants!='[]'
        and c.responsible_name is not null and a.subject_name is not null and a.project_id=#{projectId}
    </select>

</mapper>
