package com.fengyun.udf.uaa.web.rest.expertpanel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.expertpanel.*;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertPanelDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO;
import com.fengyun.udf.uaa.dto.response.system.UserDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareProjectService;
import com.fengyun.udf.uaa.service.expertpanel.ExpertPanelService;
import com.fengyun.udf.uaa.service.expertpanel.ExpertScoreTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/expert/score/template")
@Api(value = "专家评分模板", tags = "专家评分模板")
@Validated
@PreAuthorize("@pms.hasPermission('management')")
public class ExpertScoreTemplateResource extends BaseResource {

    @Autowired
    private ExpertScoreTemplateService expertScoreTemplateService;

    @GetMapping({"/page"})
    @ApiOperation(value = "模板列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<ExpertScoreTemplateDTO>> page(Page page, ExpertScoreTemplateSearchDTO dto) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreTemplateService.page(page, dto));
    }

    @GetMapping({"/list"})
    @ApiOperation(value = "模板列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<ExpertScoreTemplateDTO>> getList() throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreTemplateService.getList());
    }

    @PostMapping({"/add"})
    @CustomBaseException
    @ApiOperation(value = "新增模板")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> add(@RequestBody ExpertScoreTemplateCreateDTO dto) throws Exception {
        expertScoreTemplateService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/update"})
    @CustomBaseException
    @ApiOperation(value = "更新模板")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody ExpertScoreTemplateCreateDTO dto) throws Exception {
        expertScoreTemplateService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/get/{id}"})
    @ApiOperation(value = "根据id查询模板")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<ExpertScoreTemplateDTO>> get(@PathVariable("id") String id) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreTemplateService.getById(id));
    }

    @PostMapping({"/delete/{id}"})
    @ApiOperation(value = "删除模板")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> delete(@PathVariable String id) throws Exception {
        expertScoreTemplateService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/get/relation"})
    @ApiOperation(value = "根据申报项目id查询模板")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<ExpertScoreTemplateDTO>> getRelationTemplate(String id,String index) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertScoreTemplateService.getRelationTemplate(id,index));
    }

}
