package com.fengyun.udf.uaa.service.declareproject;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.declareproject.*;
import com.fengyun.udf.uaa.dto.response.declareproject.RuNodeTaskHistoryShowDTO;
import com.fengyun.udf.uaa.mapper.declareproject.*;
import com.fengyun.udf.uaa.mapper.system.UserGroupMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.util.CollUtils;
import com.fengyun.udf.util.UUIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class DeclareFlowService extends BaseService {


    private volatile Set<String> FLOW_SET = new HashSet<>();

    @Autowired
    private DeclareProjectMapper declareProjectMapper;

    @Autowired
    private DeclareEntRoleMapper declareEntRoleMapper;

    @Autowired
    private ProcessModelMapper processModelMapper;

    @Autowired
    private RuNodeTaskHistoryMapper ruNodeTaskHistoryMapper;

    @Autowired
    private DeclareSubProjectMapper declareSubProjectMapper;

    @Autowired
    private UserGroupMapper userGroupMapper;

    public void start(DeclareEnt declareEnt) {
        // 查询流程
        DeclareProject declareProject = declareProjectMapper.selectById(declareEnt.getDeclareId());
        ProcessModel processModel = processModelMapper.selectById(declareProject.getFlowId());
        String flowModel = processModel.getModel();
        declareEnt.setFlow(flowModel);
        declareEnt.setPreRole("enterprise");
        String nextRole = getNextRole(flowModel, "enterprise");
        declareEnt.setCurrentRole(nextRole);
        changeDeclareEntRole(nextRole, declareEnt.getId(), "");
    }

    private Boolean changeDeclareEntRole(String nextRole, String declareEntId, String declareEntRoleId) {
        if (StringUtils.isNotBlank(declareEntRoleId)) {
            declareEntRoleMapper.deleteById(declareEntRoleId);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("declare_ent_id", declareEntId);
        List<DeclareEntRole> roles = declareEntRoleMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(roles)) {
            return false;
        }
        //        if ("enterprise".equals(nextRole)) {
        //            return true;
        //        }
        List<String> nextRoles = Arrays.asList(nextRole.split(","));
        nextRoles.forEach(a -> {
            DeclareEntRole role = new DeclareEntRole();
            role.setDeleted(false);
            role.setId(UUIDGenerator.getUUID());
            role.setCurrentRole(a);
            role.setDeclareEntId(declareEntId);
            role.create(SecurityUtils.getUserId());
            declareEntRoleMapper.insert(role);
        });
        return true;
    }

    private String getNextRole(String flowModel, String currentNode) {

        JSONObject flowModelObject = JSONObject.parseObject(flowModel, JSONObject.class);
        JSONObject nodeObject = JSONObject.parseObject(flowModelObject.get(currentNode) + "", JSONObject.class);
        Object next = flowModelObject.get(nodeObject.get("nextNode") + "");
        if (next == null) {
            return nodeObject.get("nextNode") + "";
        }
        JSONObject nextObject = JSONObject.parseObject(next + "", JSONObject.class);
        String role = String.valueOf(nextObject.get("role"));
        return role;
    }

    private String getBackRole(String flowModel, String currentNode) {
        JSONObject flowModelObject = JSONObject.parseObject(flowModel, JSONObject.class);
        JSONObject nodeObject = JSONObject.parseObject(flowModelObject.get(currentNode) + "", JSONObject.class);
        Object next = flowModelObject.get(nodeObject.get("backNode") + "");
        if (next == null) {
            return "enterprise";
        }
        JSONObject nextObject = JSONObject.parseObject(next + "", JSONObject.class);
        String role = String.valueOf(nextObject.get("role"));
        return role;
    }

    public void next(DeclareEnt declareEnt, String event, String remark, String currentRoleId) {
        lock(declareEnt.getId());
        nextFlow(declareEnt, event, remark, currentRoleId);
        rmlock(declareEnt.getId());

    }

    private void lock(String declareEntId) {
        if (!FLOW_SET.contains(declareEntId)) {
            synchronized (this) {
                if (FLOW_SET.contains(declareEntId)) {
                    while (FLOW_SET.contains(declareEntId)) {
                        log.info("自旋中");
                        ThreadUtil.sleep(1000);
                    }
                }
                FLOW_SET.add(declareEntId);
            }
        } else {
            throw new ValidationException("error", "流程正在被其他人员提交审核，请刷新页面");
        }
    }

    private void rmlock(String declareEntId) {
        if (FLOW_SET.contains(declareEntId)) {
            synchronized (this) {
                FLOW_SET.remove(declareEntId);
            }
        }
    }


    private void nextFlow(DeclareEnt declareEnt, String event, String remark, String declareEntRoleId) {

        String currentRole = declareEnt.getCurrentRole();
        String nextRole = "over";
        if ("pass".equals(event) || "restart".equals(event)) {
            nextRole = getNextRole(declareEnt.getFlow(), currentRole);
            declareEnt.setStatus(Constants.TDECLARE_STATUS.CONFIRM.name());
        } else if ("reject".equals(event)) {
            nextRole = "end";
            declareEnt.setStatus(Constants.TDECLARE_STATUS.CONFIRM.name());
        } else if ("back".equals(event)) {
            nextRole = getBackRole(declareEnt.getFlow(), currentRole);
            declareEnt.setStatus(Constants.TDECLARE_STATUS.CANCEL.name());
        }
        declareEnt.setPreRole(currentRole);

        if ("over".equals(nextRole) || "enterprise".equals(nextRole)) {
            // 发送短信
            DeclareProject declareProject = declareProjectMapper.selectById(declareEnt.getDeclareId());
            Map<String, String> param = new HashMap<>();
            param.put("declareName", declareProject.getDeclareName());
            try {
                if ("over".equals(nextRole)) {
                    // sendSms(declareEnt.getContactTel(), smsProperties.getDeclare(), param);
                } else {
                    // sendSms(declareEnt.getContactTel(), smsProperties.getDeclareback(), param);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        if (changeDeclareEntRole(nextRole, declareEnt.getId(), declareEntRoleId)) {
            declareEnt.setCurrentRole(nextRole);
            if ("back".equals(event)) {
                if ("enterprise".equals(nextRole)) {
                    declareEnt.setEntStatus(Constants.TDECLARE_STATUS.CANCEL.name());
                }
            }
        }
        // 设置更新时间
        declareEnt.update(SecurityUtils.getUserId());
        //建立历史节点信息
        createHistory(currentRole, declareEnt.getId(), remark, event);
    }

    /**
     * 提取历史创建方法
     * @param assignUserRole 签发角色
     * @param processId 申报Id
     * @param remark 结果内容
     * @param event 处理结果
     */
    public void createHistory(String assignUserRole, String processId, String remark, String event) {
        // 建立历史节点信息
        RuNodeTaskHistory ruNodeTaskHistory = new RuNodeTaskHistory();
        ruNodeTaskHistory.setId(UUIDGenerator.getUUID());
        ruNodeTaskHistory.setAssignUserId(SecurityUtils.getUserId());
        ruNodeTaskHistory.setAssignUserName(SecurityUtils.getUserName());
        ruNodeTaskHistory.setAssignUserRole(assignUserRole);
        ruNodeTaskHistory.setProcessTime(ZonedDateTime.now());
        ruNodeTaskHistory.setCreatedId(SecurityUtils.getUserId());
        ruNodeTaskHistory.setRuProcessId(processId);
        ruNodeTaskHistory.setAuditRemarks(remark);
        ruNodeTaskHistory.setSelectEvent(event);
        ruNodeTaskHistory.create(SecurityUtils.getUserId());
        ruNodeTaskHistoryMapper.insert(ruNodeTaskHistory);
    }

    /**
     * 获取审核记录
     * @param processId 流程Id
     * @return 审核记录列表
     */
    public List<RuNodeTaskHistoryShowDTO> getTaskHistory(String processId) {
        if(StrUtil.isBlank(processId)) {
            return Collections.emptyList();
        }
        return ruNodeTaskHistoryMapper.selectList(new LambdaQueryWrapper<RuNodeTaskHistory>()
                .eq(RuNodeTaskHistory::getRuProcessId, processId)
                // 创建时间倒叙
                .orderByDesc(RuNodeTaskHistory::getCreatedDate))
                .stream()
                .map(entity -> {
                    RuNodeTaskHistoryShowDTO dto = new RuNodeTaskHistoryShowDTO();
                    BeanUtil.copyProperties(entity, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }
}
