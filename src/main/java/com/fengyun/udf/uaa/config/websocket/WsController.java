package com.fengyun.udf.uaa.config.websocket;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * Created by gongzuming on 2020/3/23.
 */
@Controller
public class WsController {

    @Autowired
    private WebSocketService webSocketService;

    @MessageMapping(Constants.FORETOSERVERPATH)//@MessageMapping和@RequestMapping功能类似，用于设置URL映射地址，浏览器向服务器发起请求，需要通过该地址。
//    @SendTo(Constants.PRODUCERPATH)//如果服务器接受到了消息，就会对订阅了@SendTo括号中的地址传送消息。
    public void receive(List<ReceiveMessage> messages) throws Exception {
        webSocketService.receive(messages);
    }
}
