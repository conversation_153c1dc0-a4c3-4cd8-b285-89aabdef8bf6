package com.fengyun.udf.uaa.config.security.service;


import com.fengyun.udf.security.constant.SecurityConstants;
import com.fengyun.udf.security.dto.AuthUser;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.service.system.MenuService;
import com.fengyun.udf.uaa.service.system.RoleService;
import com.fengyun.udf.uaa.service.system.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by wangg on 2019/7/17.
 */
@Component
public class BaseUserDetailsService implements UserDetailsService {

    @Autowired
    protected MenuService menuService;

    @Autowired
    protected RoleService roleService;

    @Lazy
    @Autowired
    protected UserService userService;

    /**
     * 获取登录用户的权限信息（包含角色和菜单权限）
     *
     * @param userId
     * @return
     */
    protected Set<String> getAuthSet(String userId) {
        Set<String> authSet = new HashSet<>();
        //添加角色
        List<String> roles = roleService.getUserRoles(userId);
        if (CollectionUtils.isNotEmpty(roles)) {
            roles.forEach(role -> authSet.add(SecurityConstants.ROLE + role));
        }
        //添加菜单权限
        List<String> permissions = menuService.getUserMenuPermissions(userId);
        if (CollectionUtils.isNotEmpty(permissions)) {
            permissions.stream().filter(permission -> StringUtils.isNotBlank(permission)).forEach(p -> authSet.addAll(Arrays.asList(p.split(","))));
        }

        //如果当前用户没有任何权限则添加默认角色
        if (CollectionUtils.isEmpty(authSet)) {
            authSet.add(SecurityConstants.DEFAULT_ROLE);
        }

        return authSet;
    }

    protected List<String> prepareUserTypes(String loginType){
        List<String> userTypes = new ArrayList<>();
        if(UserType.tenant.name().equals(loginType)){ //租户管理用户登录
            userTypes.add(UserType.tenant.name());
        }else if(UserType.management.name().equals(loginType)){//运营平台用户登录
            userTypes.add(UserType.management.name());
        }else if(UserType.expert.name().equals(loginType)){//专家用户普通登陆
            userTypes.add(UserType.expert.name());
        } else{//租户普通用户登录
            userTypes.add(UserType.member.name());
        }

        return userTypes;
    }

    @Override
    public UserDetails loadUserByUsername(String id) throws UsernameNotFoundException {
        User user = userService.getUserById(id);
        Set<String> authSet = getAuthSet(user.getId());
        authSet.add(user.getType());
        Collection<? extends GrantedAuthority> authorities
                = AuthorityUtils.createAuthorityList(authSet.toArray(new String[0]));
        return new AuthUser(user.getId(), user.getNickName(),
                user.getId(), user.getPassword(),
                true, true, true, true, authorities);
    }
}
