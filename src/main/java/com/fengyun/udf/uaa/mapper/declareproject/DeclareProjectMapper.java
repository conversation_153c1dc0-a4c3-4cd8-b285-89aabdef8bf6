package com.fengyun.udf.uaa.mapper.declareproject;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.declareproject.DeclareProject;
import com.fengyun.udf.uaa.dto.request.declareproject.QueryDeclareProjectDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareProjectShowDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Administrator on 2021/2/2.
 */
@Repository
public interface DeclareProjectMapper extends BaseMapper<DeclareProject> {


    List<DeclareProjectShowDTO> list(Page page, @Param("condition") QueryDeclareProjectDTO obj);

    List<DeclareProjectShowDTO> page(Page page, @Param("params") QueryDeclareProjectDTO obj);

    List<DeclareProjectShowDTO> getDeclareList();

    int getTemplateById(@Param("id") String id, @Param("oldExpertScoreTemplateId") String oldExpertScoreTemplateId);

}