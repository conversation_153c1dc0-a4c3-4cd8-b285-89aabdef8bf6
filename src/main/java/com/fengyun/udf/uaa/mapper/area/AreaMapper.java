package com.fengyun.udf.uaa.mapper.area;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.domain.area.Area;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AreaMapper extends BaseMapper<Area> {

    List<Area> findByParentSnOrderBySequence(@Param("parentSn") String parentSn);

    List<Area> findByLevelAndWorldIdOrderBySequence(@Param("level") int level, @Param("worldId") String worldId);

    List<Area> selectByPrimaryKeys(List<String> sns);

    List<Area> findAll();

    List<Area> findTenantProvince(@Param("level") int level, @Param("worldId") String worldId, @Param("tenantId") String tenantId);

    List<Area> findTenantChildren(@Param("parentSn") String parentSn, @Param("tenantId") String tenantId);
}