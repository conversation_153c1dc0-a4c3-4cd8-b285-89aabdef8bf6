package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

@Data
@TableName("t_code_type")
public class DictType extends BaseDomain {
    @TableId("ID")
    private String id;

    @TableField("NAME")
    private String name;

    @TableField("CODE")
    private String code;

    @TableField("STATUS")
    private String status;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("IS_SYS")
    private Integer isSys;
}
