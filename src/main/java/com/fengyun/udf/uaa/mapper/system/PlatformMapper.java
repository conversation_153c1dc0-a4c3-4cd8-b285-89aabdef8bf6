package com.fengyun.udf.uaa.mapper.system;

import com.fengyun.udf.uaa.domain.system.Platform;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.system.PlatformFieldQueryDTO;
import com.fengyun.udf.uaa.dto.response.system.PlatformFieldListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-30
 */
@Repository
public interface PlatformMapper extends BaseMapper<Platform> {
    void saveAttribute(@Param("map") Map<String, String> map);
    void updateAttribute(@Param("name") String name,@Param("value") String value );
    List<PlatformFieldListDTO> getPlatformFieldList(@Param("query") PlatformFieldQueryDTO dto);
}
