package com.fengyun.udf.uaa.config.security.service;


import com.fengyun.udf.security.dto.AuthUser;
import com.fengyun.udf.uaa.domain.system.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by wangg on 2019/7/5.
 */
@Component
public class PhoneUserDetailsService extends BaseUserDetailsService {

    private final static String TEL_NOT_FOUND = "error.login.telNotFound";

    @Autowired
    private MessageSource messageSource;

    public UserDetails loadUserByTelAndTenantCode(String tel, String loginType) throws UsernameNotFoundException {
        List<String> userTypes = prepareUserTypes(loginType);

        User user = userService.findByTel(tel, userTypes);
        if (user == null) {
            throw new UsernameNotFoundException(messageSource.getMessage(TEL_NOT_FOUND, null, null, LocaleContextHolder.getLocale()));
        }

        Set<String> authSet = getAuthSet(user.getId());
        authSet.add(user.getType());
        Collection<? extends GrantedAuthority> authorities
                = AuthorityUtils.createAuthorityList(authSet.toArray(new String[0]));
        return new AuthUser(user.getId(), user.getNickName(),
                user.getId(), user.getPassword(),
                true, true, true, true, authorities);
    }
}
