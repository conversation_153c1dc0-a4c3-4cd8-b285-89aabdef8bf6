package com.fengyun.udf.uaa.dto.response.instrument;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
public class InstrumentOrderListDTO extends AbstractDTO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("仪器id")
    private String instrumentId;

    @ApiModelProperty("设备名称")
    private String instrumentName;

    @ApiModelProperty("租赁类型")
    @DictData(target = "leaseTypeStr", codeType = "INSTRUMENT_ORDER_LEASE_TYPE")
    private String leaseType;
    private String leaseTypeStr;

    @ApiModelProperty("企业名称")
    private String enName;

    @ApiModelProperty("预约联系人")
    private String contact;

    @ApiModelProperty("预约联系方式")
    private String contactTel;

    @ApiModelProperty("预约联系邮箱")
    private String contactEmail;

    @ApiModelProperty("是否熟练使用仪器")
    private Boolean skilledUse;

    @ApiModelProperty("状态")
    @DictData(target = "statusStr", codeType = "INSTRUMENT_ORDER_STATUS")
    private String status;
    private String statusStr;

    @ApiModelProperty("评价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime evaluateTime;

    @ApiModelProperty("结算状态")
    @DictData(target = "settlementStatusStr", codeType = "INSTRUMENT_ORDER_SETTLEMENT_STATUS")
    private String settlementStatus;
    private String settlementStatusStr;

    @ApiModelProperty("创建时间")
    private String createdDate;

    @ApiModelProperty("核准金额")
    private Double amount;

    @ApiModelProperty("工单状态，final_commit")
    @DictData(target = "workStatusStr", codeType = "INSTRUMENT_ORDER_WORK_STATUS")
    private String workStatus;
    private String workStatusStr;

    @ApiModelProperty("收据发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptSendTime;

    @ApiModelProperty("收据文件")
    private String receiptStampFile;

    @ApiModelProperty("收据文件")
    private String receiptFile;

    @ApiModelProperty("评价内容")
    private String evaluateContent;

    @ApiModelProperty(hidden = true)
    private Integer index;
    @ApiModelProperty(hidden = true)
    private String exportOrderTimeStr;
    @ApiModelProperty(hidden = true)
    private String skilledUseStr;

    private String workBtnStatus;

    @ApiModelProperty("预约时间str")
    private String orderDateStr;

    @ApiModelProperty("发票文件")
    private String invoiceFile;

    @ApiModelProperty("发票发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendInvoiceTime;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("发票状态,INSTRUMENT_ORDER_INVOICE_STATUS")
    @DictData(target = "invoiceStatusStr", codeType = "INSTRUMENT_ORDER_INVOICE_STATUS")
    private String invoiceStatus;
    private String invoiceStatusStr;

    @ApiModelProperty("预约编号")
    private String orderNum;


}