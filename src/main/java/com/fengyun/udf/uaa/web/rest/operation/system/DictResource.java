package com.fengyun.udf.uaa.web.rest.operation.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.interceptor.KeyConnector;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.system.*;
import com.fengyun.udf.uaa.dto.response.system.DictTypeDTO;
import com.fengyun.udf.uaa.dto.response.system.DictValueDTO;
import com.fengyun.udf.uaa.service.system.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.RedisClientInfo;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
@Api(value = "字典管理", tags = "字典管理")
@Validated
public class DictResource extends BaseResource {
    @Autowired
    private DictService dictService;

    @PostMapping("/dict/type")
    @CustomBaseException
    @AuditLog(method = "/api/dict/type", methodDescription = "新增字典类型", httpMethod = "POST")
    @ApiOperation(value = "新增字典类型")
    //@PreAuthorize("@pms.hasPermission('dictTypeAdd')")
    public ResponseEntity<ReturnResultDTO> addDictType(@RequestBody @Validated CreateDictTypeDTO createDictTypeDTO){
        if (dictService.dictTypeAlreadyExisting(createDictTypeDTO.getCode())){
            throw new ValidationException("dictType", "error.dictType.alreadyExists");
        }
        dictService.addDictType(createDictTypeDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/dict/type/update")
    @CustomBaseException
    @AuditLog(method = "/api/dict/type/update", methodDescription = "编辑字典类型", httpMethod = "POST")
    @ApiOperation(value = "编辑字典类型")
    //@PreAuthorize("@pms.hasPermission('dictTypeUpdate')")
    public ResponseEntity<ReturnResultDTO> updateDictType(@RequestBody @Validated UpdateDictTypeDTO updateDictTypeDTO){
        if (!dictService.dictTypeIsExisting(updateDictTypeDTO.getId())){
            throw new ValidationException("dictType", "error.dictType.notExists");
        }
        dictService.updateDictType(updateDictTypeDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/dict/type")
    @CustomBaseException
    @AuditLog(method = "/api/dict/type", methodDescription = "查询字典类型", httpMethod = "GET")
    @ApiOperation(value = "获取字典类型")
    //@PreAuthorize("@pms.hasPermission('dictTypeSearch')")
    public ResponseEntity<ReturnResultDTO<Page<DictTypeDTO>>> searchDictType(@ModelAttribute SearchDictTypeDTO searchDictTypeDTO, Page page){
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), dictService.searchDictType(searchDictTypeDTO, page));
    }

    @GetMapping("/dict/type/delete")
    @CustomBaseException
    @AuditLog(method = "/api/dict/type/delete", methodDescription = "删除字典类型", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "字典类型id", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "删除字典类型")
    //@PreAuthorize("@pms.hasPermission('dictTypeDelete')")
    public ResponseEntity<ReturnResultDTO> deleteDictType(@NotBlank(message = "{error.dictTypeId.blank}") String id) {
        if (!dictService.dictTypeIsExisting(id)){
            throw new ValidationException("dictType", "error.dictType.notExists");
        }
        dictService.deleteDictType(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/dict/value")
    @CustomBaseException
    @AuditLog(method = "/api/dict/value", methodDescription = "新增字典值", httpMethod = "POST")
    @ApiOperation(value = "新增字典值")
    //@PreAuthorize("@pms.hasPermission('dictValueAdd')")
    public ResponseEntity<ReturnResultDTO> addDictValue(@RequestBody @Validated CreateDictValueDTO createDictValueDTO){
        if (dictService.dictValueAlreadyExisting(createDictValueDTO.getTypeCode(), createDictValueDTO.getValue())){
            throw new ValidationException("dictValue", "error.dictValue.alreadyExists");
        }
        dictService.addDictValue(createDictValueDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/dict/value/update")
    @CustomBaseException
    @AuditLog(method = "/api/dict/value/update", methodDescription = "编辑字典值", httpMethod = "POST")
    @ApiOperation(value = "编辑字典值")
    //@PreAuthorize("@pms.hasPermission('dictValueUpdate')")
    public ResponseEntity<ReturnResultDTO> updateDictValue(@RequestBody @Validated UpdateDictValueDTO updateDictValueDTO){
        if (!dictService.dictValueIsExisting(updateDictValueDTO.getId())){
            throw new ValidationException("dictValue", "error.dictValue.notExists");
        }
        dictService.updateDictValue(updateDictValueDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/dict/value")
    @CustomBaseException
    @AuditLog(method = "/api/dict/value", methodDescription = "获取字典值", httpMethod = "GET")
    @ApiOperation(value = "获取字典值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeCode", value = "类型码", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<List<DictValueDTO>>> searchDictValue(@NotBlank(message = "{error.dictTypeCode.blank}") String typeCode){
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), dictService.searchDictValue(typeCode));
    }

    @GetMapping("/dict/value/delete")
    @CustomBaseException
    @AuditLog(method = "/api/dict/value/delete", methodDescription = "删除字典值", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "字典值id", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "删除字典值")
    //@PreAuthorize("@pms.hasPermission('dictValueDelete')")
    public ResponseEntity<ReturnResultDTO> deleteDictValue(@NotBlank(message = "{error.dictValueId.blank}") String id) {
        if (!dictService.dictValueIsExisting(id)){
            throw new ValidationException("dictValue", "error.dictValue.notExists");
        }
        dictService.deleteDictValue(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/dict/refresh")
    @CustomBaseException
    @AuditLog(method = "/api/dict/refresh", methodDescription = "刷新字典缓存", httpMethod = "GET")
    @ApiOperation(value = "刷新字典缓存")
    //@PreAuthorize("@pms.hasPermission('dictRefresh')")
    public ResponseEntity<ReturnResultDTO> refreshDictCache(){
        dictService.refreshDictCache();
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/dict/value/multiple")
    @CustomBaseException
    @AuditLog(method = "/api/dict/value/multiple", methodDescription = "获取多个字典值", httpMethod = "GET")
    @ApiOperation(value = "获取多个字典值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeCode", value = "类型码，逗号隔开", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO> searchMultipleDictValue(@NotBlank(message = "{error.dictTypeCode.blank}") String typeCode){
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), dictService.searchMultipleDictValue(typeCode));
    }
}
