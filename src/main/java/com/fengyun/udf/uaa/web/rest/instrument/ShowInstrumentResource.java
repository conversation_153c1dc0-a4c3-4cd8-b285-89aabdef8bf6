package com.fengyun.udf.uaa.web.rest.instrument;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.instrument.*;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentListDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentShowListDTO;
import com.fengyun.udf.uaa.service.instrument.InstrumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-29
 */

@RestController
@RequestMapping("/api/ua/instrument")
@Api(value = "企业端-仪器", tags = "企业端-仪器")
@Validated
@Slf4j
@AllArgsConstructor
public class ShowInstrumentResource extends BaseResource {
    private final InstrumentService instrumentService;

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/ua/instrument/view", methodDescription = "仪器详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "仪器详情")
    public ResponseEntity<ReturnResultDTO<InstrumentDetailDTO>> detail(@NotBlank(message = "id不能为空") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentService.detail(id));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/ua/instrument", methodDescription = "仪器列表", httpMethod = "GET")
    @ApiOperation(value = "仪器列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentShowListDTO>>> page(Page page, InstrumentQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentService.pageShow(page, dto));
    }

}
