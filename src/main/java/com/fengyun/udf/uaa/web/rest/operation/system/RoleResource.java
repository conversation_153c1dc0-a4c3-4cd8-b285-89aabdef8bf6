package com.fengyun.udf.uaa.web.rest.operation.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.domain.system.Role;
import com.fengyun.udf.uaa.dto.request.system.CreateRoleDTO;
import com.fengyun.udf.uaa.dto.request.system.SearchRoleDTO;
import com.fengyun.udf.uaa.dto.request.system.UpdateRoleDTO;
import com.fengyun.udf.uaa.dto.response.system.RoleDTO;
import com.fengyun.udf.uaa.dto.response.system.RoleListDTO;
import com.fengyun.udf.uaa.service.system.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

import static com.fengyun.udf.uaa.constant.Constants.YES;

/**
 * Created by zhouyr on 2019/7/4.
 */
@RestController
@RequestMapping("/api")
@Api(value = "角色管理", tags = "运营平台-系统管理")
@Validated
public class RoleResource extends BaseResource {

    @Autowired
    private RoleService roleService;

    @PostMapping("/role")
    @CustomBaseException
    @AuditLog(method = "/api/role", methodDescription = "新增角色", httpMethod = "POST")
    @ApiOperation(value = "新增角色")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> addRole(@RequestBody @Validated CreateRoleDTO createRoleDTO) {
        if (roleService.roleIsExisting(createRoleDTO.getRole())) {
            throw new ValidationException("role", "error.role.existing");
        }
        roleService.addRole(createRoleDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/role/list")
    @CustomBaseException
    @AuditLog(method = "/api/role/list", methodDescription = "查询角色列表", httpMethod = "GET")
    @ApiOperation(value = "查询角色列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<Page<RoleListDTO>>> searchRole(@ModelAttribute SearchRoleDTO searchRoleDTO, Page page) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), roleService.searchRole(searchRoleDTO, page));
    }

    @GetMapping("/role")
    @CustomBaseException
    @AuditLog(method = "/api/role", methodDescription = "查询角色详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "角色ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查询角色详情")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<RoleDTO>> getRole(@NotBlank(message = "{error.roleId.blank}") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), roleService.getRoleDetail(id));
    }

    @PostMapping("/role/update")
    @CustomBaseException
    @AuditLog(method = "/api/role/update", methodDescription = "编辑角色", httpMethod = "POST")
    @ApiOperation(value = "编辑角色")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> updateRole(@RequestBody @Validated UpdateRoleDTO updateRoleDTO) {
        Role role = roleService.getRole(updateRoleDTO.getRoleId());
        //验证角色ID是否存在
        if (role == null) {
            throw new ValidationException("roleId", "error.roleId.notExisting");
        }
        //验证角色是否是系统默认
        if (StringUtils.equals(YES, role.getIsSys())) {
            throw new ValidationException("roleId", "error.role.update.forbidden");
        }
        //当角色标识改变时，验证角色标识是否重复
        if (!StringUtils.equals(role.getRole(), updateRoleDTO.getRole())) {
            if (roleService.roleIsExisting(updateRoleDTO.getRole())) {
                throw new ValidationException("role", "error.role.existing");
            }
        }
        roleService.updateRole(updateRoleDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/role/delete")
    @CustomBaseException
    @AuditLog(method = "/api/role/delete", methodDescription = "删除角色", httpMethod = "GET")
    @ApiOperation(value = "删除角色")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> deleteRole(@NotBlank(message = "{error.roleId.blank}") String id) {
        Role role = roleService.getRole(id);
        //验证角色ID是否存在
        if (role == null) {
            throw new ValidationException("id", "error.roleId.notExisting");
        }
        //验证角色是否是系统默认
        if (StringUtils.equals(YES, role.getIsSys())) {
            throw new ValidationException("id", "error.role.delete.forbidden");
        }
        roleService.deleteRole(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
}
