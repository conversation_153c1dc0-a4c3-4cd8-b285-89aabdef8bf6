package com.fengyun.udf.uaa.web.rest.operation.system;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.config.validate.Mandatory;
import com.fengyun.udf.uaa.config.validate.Optional;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.dto.request.system.*;
import com.fengyun.udf.uaa.dto.response.system.RegisterUserList;
import com.fengyun.udf.uaa.dto.response.system.UserDetailDTO;
import com.fengyun.udf.uaa.dto.response.system.UserListDTO;
import com.fengyun.udf.uaa.dto.response.system.UserMemberListDTO;
import com.fengyun.udf.uaa.service.system.GroupService;
import com.fengyun.udf.uaa.service.system.RoleService;
import com.fengyun.udf.uaa.service.system.UserService;
import com.fengyun.udf.uaa.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by zhouyr on 2019/7/4.
 */
@RestController
@RequestMapping("/api")
@Api(value = "运营平台用户管理", tags = "运营平台-系统管理")
@Validated
public class UserResource extends BaseResource {
    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private GroupService groupService;

    @PostMapping("/user")
    @CustomBaseException
    @AuditLog(method = "/api/user", methodDescription = "运营管理平台-新增用户", httpMethod = "POST")
    @ApiOperation(value = "运营管理平台-新增用户")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> addUser(@RequestBody @Validated({Mandatory.class, Optional.class}) CreateUserDTO createUserDTO) {
        if (createUserDTO.getRoleIds() != null && createUserDTO.getRoleIds().size() > 0) {
            if (!roleService.roleIsExisting(createUserDTO.getRoleIds())) {
                throw new ValidationException("roleId", "error.roleId.notExisting");
            }
        }
        createUserDTO.setUserType(UserType.management.name());
        userService.addUser(createUserDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/user/list")
    @CustomBaseException
    @AuditLog(method = "/api/user/list", methodDescription = "查询运营平台用户列表", httpMethod = "GET")
    @ApiOperation(value = "查询运营平台用户列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<Page<UserListDTO>>> searchUser(@ModelAttribute SearchUserDTO dto, Page page) {
        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.management.name());
        dto.setUserTypes(userTypes);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), userService.searchUser(dto, page));
    }

    @GetMapping("/user")
    @CustomBaseException
    @AuditLog(method = "/api/user", methodDescription = "查询用户详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查询用户详情")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<UserDetailDTO>> getUser(@NotBlank(message = "{error.userId.blank}") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), userService.getUserDetail(id));
    }

    @PostMapping("/user/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/update", methodDescription = "运营管理平台-更新用户", httpMethod = "POST")
    @ApiOperation(value = "运营管理平台-更新用户")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> updateUser(@RequestBody @Validated({Mandatory.class}) UpdateUserDTO updateUserDTO) {
        if (!userService.isUserExists(updateUserDTO.getUserId())) {
            throw new ValidationException("userId", "error.userId.notExisting");
        }
        if (updateUserDTO.getRoleIds() != null && updateUserDTO.getRoleIds().size() > 0) {
            if (!roleService.roleIsExisting(updateUserDTO.getRoleIds())) {
                throw new ValidationException("roleId", "error.roleId.notExisting");
            }
        }
        updateUserDTO.setUserType(UserType.management.name());
        userService.updateUser(updateUserDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/user/reset/password/update")
    @CustomBaseException
    @AuditLog(method = "/api/user/reset/password/update", methodDescription = "重设密码", httpMethod = "POST")
    @ApiOperation(value = "重设密码")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> resetUserPassword(@RequestBody @Validated({Mandatory.class}) ResetUserPasswordDTO dto) {
        if (!userService.isUserExists(dto.getUserId())) {
            throw new ValidationException("userId", "error.userId.notExisting");
        }
        userService.resetUserPassword(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/user/delete")
    @CustomBaseException
    @AuditLog(method = "/api/user/delete", methodDescription = "删除用户", httpMethod = "GET")
    @ApiOperation(value = "删除用户")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO> deleteUser(@NotBlank(message = "{error.userId.blank}") String id) {
        if (!userService.isUserExists(id)) {
            throw new ValidationException("id", "error.userId.notExisting");
        }
        userService.deleteUser(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/user/member/list")
    @CustomBaseException
    @AuditLog(method = "/api/user/member/list", methodDescription = "用户列表", httpMethod = "GET")
    @ApiOperation(value = "用户列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<Page<UserMemberListDTO>>> searchMemberUser(@ModelAttribute SearchMemberUserDTO dto, Page page) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), userService.searchMemberUser(dto, page));
    }

    @GetMapping("/user/getRegisteredList")
    @CustomBaseException
    @AuditLog(method = "/api/user/getRegisteredList", methodDescription = "获取用户注册列表", httpMethod = "GET")
    @ApiOperation(value = "获取用户注册列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public void getRegisteredList(HttpServletResponse response) {
        try {
            String fileName = "RegisteredUser ".concat(DateUtils.formatDate(new Date(System.currentTimeMillis()))).concat(".xlsx");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");

            response.setHeader("content-disposition", "attachment;filename=".concat(fileName));
            userService.getRegisteredUserList(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ValidationException("error", "注册列表导出失败");
        }
    }

    @GetMapping("/user/registerUser")
    @CustomBaseException
    @AuditLog(method = "/api/user/registerUser", methodDescription = "注册用户列表", httpMethod = "GET")
    @ApiOperation(value = "注册用户列表")
    @PreAuthorize("@pms.hasPermission('management')")
    public ResponseEntity<ReturnResultDTO<Page<RegisterUserList>>> registerUser(QueryRegisterUserDto dto, Page page) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), userService.queryRegisterUserList(dto, page));
    }
}
