package com.fengyun.udf.uaa.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.dto.request.system.QueryRegisterUserDto;
import com.fengyun.udf.uaa.dto.request.system.QuerySpaceUserDTO;
import com.fengyun.udf.uaa.dto.request.system.SearchMemberUserDTO;
import com.fengyun.udf.uaa.dto.request.system.SearchUserDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportUserListDTO;
import com.fengyun.udf.uaa.dto.response.system.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wangg on 2019/7/5.
 */
@Repository
public interface UserMapper extends BaseMapper<User> {
    List<UserListDTO> searchUser(Page page, @Param("condition") SearchUserDTO searchUserDTO);

    User findUserByLoginNameOrTel(@Param("loginName") String loginName
            , @Param("userTypes") List<String> userTypes);

    List<UserListDTO> findAllActiveUser();

    List<User> findUserList(Page page, @Param("condition") QuerySpaceUserDTO dto);

    int updateEnterpriseUserIdByCreditCode(@Param("creditCode") String creditCode, @Param("userId") String userId);

    List<UserMemberListDTO> selectMemberUser(Page page, @Param("condition") SearchMemberUserDTO dto);

    void insertAll(@Param("condition") List<User> addUsers);

    List<UserRegisterInfoList> queryRegisterUserList();

    List<RegisterUserList> queryRegisterUserPage(@Param("condition") QueryRegisterUserDto dto, Page page);

    String searchUserNames(@Param("userIds") List<String> userIds);

    List<UserDTO> getExpertList();

    List<InstrumentOrderExportUserListDTO> queryOrderUserPage(Integer userType);
}
