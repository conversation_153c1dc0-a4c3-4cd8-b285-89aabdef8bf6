package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.dto.AbstractDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProjectFundingDetailDTO", description = "ProjectFundingDetailDTO")
public class ProjectFundingDetailDTO extends AbstractDTO {

    @ApiModelProperty(value = "项目经费到位情况")
    private DeclareEntMiddleFundingDetailDTO funding;

    @ApiModelProperty(value = "项目经费使用情况")
    private DeclareEntMiddleFundingUsageDetailDTO fundingUsage;

    @ApiModelProperty(value = "经费使用详细说明")
    private List<DeclareEntMiddleFundingRemarkDetailDTO> fundingRemarks;

}
