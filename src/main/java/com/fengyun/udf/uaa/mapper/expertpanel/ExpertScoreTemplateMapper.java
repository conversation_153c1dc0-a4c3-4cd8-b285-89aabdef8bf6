package com.fengyun.udf.uaa.mapper.expertpanel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.expertpanel.ExpertScoreTemplate;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertScoreTemplateSearchDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertScoreTemplateDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ExpertScoreTemplateMapper extends com.baomidou.mybatisplus.core.mapper.BaseMapper<ExpertScoreTemplate> {

    List<ExpertScoreTemplateDTO> page(Page page, @Param("params") ExpertScoreTemplateSearchDTO dto);

    List<ExpertScoreTemplateDTO> getList();

    ExpertScoreTemplateDTO getRelationTemplate(@Param("declareEntId") String declareEntId);

    ExpertScoreTemplateDTO getSecondRelationTemplate(@Param("declareEntId") String declareEntId);

}
