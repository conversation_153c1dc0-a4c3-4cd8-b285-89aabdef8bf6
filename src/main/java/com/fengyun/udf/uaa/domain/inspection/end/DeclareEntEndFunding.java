package com.fengyun.udf.uaa.domain.inspection.end;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "declare_ent_end_funding", description = "资金来源")
@TableName(value = "declare_ent_end_funding", autoResultMap = true)
public class DeclareEntEndFunding extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 单位自有投资
     */
    @ApiModelProperty("单位自有投资")
    @TableField("owned")
    private BigDecimal owned;

    /**
     * 单位自有投资 已到位
     */
    @ApiModelProperty("单位自有投资 已到位")
    @TableField("owned_in_place")
    private BigDecimal ownedInPlace;

    /**
     * 银行贷款
     */
    @ApiModelProperty("银行贷款")
    @TableField("loan")
    private BigDecimal loan;

    /**
     * 银行贷款 已到位
     */
    @ApiModelProperty("银行贷款 已到位")
    @TableField("loan_in_place")
    private BigDecimal loanInPlace;

    /**
     * 风险投资
     */
    @ApiModelProperty("风险投资")
    @TableField("vc")
    private BigDecimal vc;

    /**
     * 风险投资 已到位
     */
    @ApiModelProperty("风险投资 已到位")
    @TableField("vc_in_place")
    private BigDecimal vcInPlace;

    /**
     * 资助资金
     */
    @ApiModelProperty("资助资金")
    @TableField("funding")
    private BigDecimal funding;

    /**
     * 资助资金 已到位
     */
    @ApiModelProperty("资助资金 已到位")
    @TableField("funding_in_place")
    private BigDecimal fundingInPlace;

    /**
     * 其他资金
     */
    @ApiModelProperty("其他资金")
    @TableField("other")
    private BigDecimal other;

    /**
     * 其他资金 已到位
     */
    @ApiModelProperty("其他资金 已到位")
    @TableField("other_in_place")
    private BigDecimal otherInPlace;

}
