<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fengyun.udf.uaa.mapper.system.UserAuditMapper">

    <select id="selectAuditRegisterPage" resultType="com.fengyun.udf.uaa.dto.response.system.EnRegisterListDto">
        SELECT
        u.id id,
        r.`code` creditCode,
        r.en_name `name`,
        u.tel tel,
        u.email email,
        r.corporate legalRep,
        r.certificate_type,
        r.certificate_number,
        r.certificate_start,
        r.certificate_end,
        u.audit_status `status`,
        u.CREATED_DATE,
        u.attachment_license,
        u.order_state
        FROM
        t_user u
        LEFT JOIN
        t_en_register r ON u.id = r.user_id
        WHERE
        u.user_type = 1 and u.audit_status is not null and u.audit_status != ''
        <if test="condition.from != null and condition.from != ''">
            AND u.CREATED_DATE >= #{condition.from}
        </if>
        <if test="condition.to != null and condition.to != ''">
            AND u.CREATED_DATE &lt;= #{condition.to}
        </if>
        <if test="condition.auditStatus != null and condition.auditStatus != ''">
            AND u.audit_status = #{condition.auditStatus}
        </if>
        <if test="condition.name != null and condition.name != ''">
            AND r.en_name like concat('%',#{condition.name},'%')
        </if>
        <if test="condition.orderField != null and condition.orderField != ''">
            <if test='condition.sort == "0"'>
                <if test='condition.orderField == "1"'>
                    order by u.CREATED_DATE
                </if>
            </if>
            <if test='condition.sort == "1"'>
                <if test='condition.orderField == "1"'>
                    order by u.CREATED_DATE desc
                </if>
            </if>
        </if>
    </select>
</mapper>
