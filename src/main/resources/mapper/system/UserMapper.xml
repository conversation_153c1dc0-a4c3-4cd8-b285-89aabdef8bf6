<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.system.UserMapper">
    <insert id="insertAll">
		INSERT
		t_user(
			ID,
			LOGIN_NAME,
			TEL,
			NICK_NAME,
			PASSWORD,
			STATUS,
			IS_SYS,
			TYPE,
			channel,
			VERSION,
			CREATED_ID,
			CREATED_DATE,
			UPDATED_ID,
			UPDATED_DATE)
		VALUES
		<foreach collection="condition" item="item" separator=",">
			(
			#{item.id},
			#{item.loginName},
			#{item.tel},
			#{item.nickName},
			#{item.password},
			#{item.status},
			#{item.isSys},
			#{item.type},
			#{item.type},
			#{item.channel},
			#{item.createdId},
			#{item.createdDate},
			#{item.updatedId},
			#{item.updatedDate}
			)
		</foreach>

	</insert>
    <select id="searchUser" resultType="com.fengyun.udf.uaa.dto.response.system.UserListDTO">
		SELECT
		u.ID AS id,
		u.TYPE AS type,
		u.LOGIN_NAME AS loginName,
		u.TEL AS tel,
		u.NICK_NAME AS nickName,
		u.STATUS AS STATUS,
		CREATED_ID as createdIdtemp,
		(select LOGIN_NAME from t_user  where id = createdIdtemp) AS createdId,
		u.CREATED_DATE AS createdDate,
		u.IS_SYS AS isSys,
		(SELECT GROUP_CONCAT(DISTINCT(role.ID)) FROM t_role role INNER JOIN t_user_role ur ON ur.ROLE_ID = role.ID
		WHERE
		ur.USER_ID = u.ID
		ORDER BY ROLE_NAME
		) AS roleIds,
		(SELECT GROUP_CONCAT(DISTINCT(ROLE_NAME)) FROM t_role role INNER JOIN t_user_role ur ON ur.ROLE_ID = role.ID
			WHERE
				ur.USER_ID = u.ID
			ORDER BY ROLE_NAME
		) AS roleDesc,
		(SELECT GROUP_CONCAT(DISTINCT(ROLE)) FROM t_role role INNER JOIN t_user_role ur ON ur.ROLE_ID = role.ID
		WHERE
		ur.USER_ID = u.ID
		ORDER BY ROLE_NAME
		) AS roleCode
		FROM
		t_user u
		where u.STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
		<if test="condition.loginName != null and condition.loginName != ''">
			AND (u.LOGIN_NAME LIKE #{condition.loginName} or u.NICK_NAME LIKE #{condition.loginName} or u.tel LIKE #{condition.loginName} )
		</if>
		<if test="condition.status != null and condition.status != ''">
			AND u.STATUS = #{condition.status}
		</if>

		<if test="condition.createDateBegin != null and condition.createDateBegin != ''">
			AND u.CREATED_DATE &gt;= #{condition.createDateBegin}
		</if>

		<if test="condition.createDateEnd != null and condition.createDateEnd != ''">
			AND u.CREATED_DATE  &lt;= #{condition.createDateEnd}
		</if>
		AND u.`type` in
		<foreach collection="condition.userTypes" item="type" open="(" separator="," close=")">
			#{type}
		</foreach>
		order by u .CREATED_DATE desc
	</select>

	<select id="findUserByLoginNameOrTel" resultType="com.fengyun.udf.uaa.domain.system.User">
		SELECT *
		FROM t_user u
		WHERE u.STATUS = '${@com.fengyun.udf.uaa.constant.Constants@STATUS_ACTIVE}'
		AND (u.tel = #{loginName} or u.login_name = #{loginName})
		AND u.`type` in
		<foreach collection="userTypes" item="type" open="(" separator="," close=")">
			#{type}
		</foreach>
	</select>

	<select id="findAllActiveUser" resultType="com.fengyun.udf.uaa.dto.response.system.UserListDTO">
		SELECT ID AS id, LOGIN_NAME AS loginName, NICK_NAME AS nickName, type
		FROM t_user
		WHERE STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
	</select>

	<select id="findUserList" resultType="com.fengyun.udf.uaa.domain.system.User">
		SELECT *
		FROM t_user u
		WHERE u.STATUS = '${@com.fengyun.udf.uaa.constant.Constants@STATUS_ACTIVE}'
		AND u.`type` = 'member'
		<if test="condition.nickname != null and condition.nickname != ''">
			AND u.NICK_NAME LIKE CONCAT('%', #{condition.nickname}, '%')
		</if>

		<if test="condition.tel != null and condition.tel != ''">
			AND ((u.contact_number IS NULL AND u.tel LIKE CONCAT('%', #{condition.tel}, '%')) OR u.contact_number LIKE CONCAT('%', #{condition.tel}, '%'))
		</if>

		<if test="condition.fullName != null and condition.fullName != ''">
			AND u.full_name LIKE CONCAT('%', #{condition.fullName}, '%')
		</if>

		<if test="condition.channel != null and condition.channel != ''">
			AND u.channel = #{condition.channel}
		</if>

		<if test="condition.createDateBegin != null and condition.createDateBegin != ''">
			AND u.CREATED_DATE &gt;= #{condition.createDateBegin}
		</if>

		<if test="condition.createDateEnd != null and condition.createDateEnd != ''">
			AND u.CREATED_DATE  &lt;= #{condition.createDateEnd}
		</if>

		ORDER BY u.CREATED_DATE DESC
	</select>
	<update id="updateEnterpriseUserIdByCreditCode">
		UPDATE po_en_basic set user_id =#{userId} where credit_code=#{creditCode}
	</update>

	<select id="selectMemberUser" resultType="com.fengyun.udf.uaa.dto.response.system.UserMemberListDTO">
		SELECT
		u.ID AS userId,
		u.LOGIN_NAME AS loginName
		from t_user u
		where u.STATUS != '${@com.fengyun.udf.uaa.constant.Constants@STATUS_DELETED}'
		and u.type = 'member'
		<if test="condition.loginName != null and condition.loginName != ''">
			AND u.LOGIN_NAME LIKE #{condition.loginName}
		</if>
		order by u .CREATED_DATE desc
	</select>

    <select id="queryRegisterUserList" resultType="com.fengyun.udf.uaa.dto.response.system.UserRegisterInfoList">
		SELECT
			LOGIN_NAME loginName,
			TEL tel,
			EMAIL email,
			e.en_name enName,
			e.corporate corporate,
			u.CREATED_DATE createdDate
		FROM
			t_user u
		LEFT JOIN
			t_en_register e ON u.id = e.user_id
		WHERE
			u.TYPE = 'member'
		Limit 10000
	</select>
	<select id="queryRegisterUserPage" resultType="com.fengyun.udf.uaa.dto.response.system.RegisterUserList">
		SELECT
			LOGIN_NAME loginName,
			TEL tel,
			e.en_name enName,
			e.corporate corporate,
			u.CREATED_DATE createdDate
		FROM
			t_user u
				LEFT JOIN
			t_en_register e ON u.id = e.user_id
		WHERE
			u.TYPE = 'member'
		<if	test="condition.from != null and condition.from != '' ">
			and u.CREATED_DATE >= #{condition.from}
		</if>
		<if	test="condition.to != null and condition.to != '' ">
			and u.CREATED_DATE &lt;= #{condition.to}
		</if>

		ORDER BY u.CREATED_DATE DESC
	</select>

	<select id="searchUserNames" resultType="String">
		select GROUP_CONCAT(NICK_NAME) from t_user where ID in
		<foreach collection="userIds" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</select>

	<select id="getExpertList" resultType="com.fengyun.udf.uaa.dto.response.system.UserDTO">
		select a.* from t_user a
		left join t_user_role b on a.ID=b.USER_ID
		left join t_role c on b.ROLE_ID=c.ID
		where c.ROLE='zjps' and a.`STATUS`='A'
	</select>

	<select id="queryOrderUserPage" resultType="com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderExportUserListDTO">
		SELECT
		u.id as userId,
		<if test="userType == 2">
		u.LOGIN_NAME as userName
		</if>
		<if test="userType == 1">
		e.en_name as userName
		</if>
		FROM
		t_user u
		<if test="userType == 1">
		LEFT JOIN t_en_register e ON u.id = e.user_id
		</if>
		WHERE
		u.TYPE = 'member'
		and u.user_type = #{userType}
		ORDER BY u.CREATED_DATE DESC
	</select>
</mapper>
