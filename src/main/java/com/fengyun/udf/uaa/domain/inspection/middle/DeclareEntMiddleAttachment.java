package com.fengyun.udf.uaa.domain.inspection.middle;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "附件上传表单")
@TableName(value = "declare_ent_middle_attachment", autoResultMap = true)
public class DeclareEntMiddleAttachment extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 企业营业执照
     */
    @ApiModelProperty("企业营业执照")
    @TableField(value = "business_license",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> businessLicense;

    /**
     * 年度审计报告
     */
    @ApiModelProperty("年度审计报告")
    @TableField(value = "annual_audit_report",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> annualAuditReport;

    /**
     * 最近一期社保缴费汇总表
     */
    @ApiModelProperty("最近一期社保缴费汇总表")
    @TableField(value = "latest_social_security_payment_summary",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> latestSocialSecurityPaymentSummary;

    /**
     * 苏州工业园区公司
     */
    @ApiModelProperty("苏州工业园区公司")
    @TableField(value = "suzhou_industrial_park_company",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> suzhouIndustrialParkCompany;

    /**
     * 租赁合同
     */
    @ApiModelProperty("租赁合同")
    @TableField(value = "lease_contract",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> leaseContract;

    /**
     * 证明材料
     */
    @ApiModelProperty("证明材料")
    @TableField(value = "certifying_documents",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> certifyingDocuments;

    /**
     * 重大进展、重大奖项
     */
    @ApiModelProperty("重大进展、重大奖项")
    @TableField(value = "major_progress_and_awards",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> majorProgressAndAwards;

    /**
     * 专利及论文
     */
    @ApiModelProperty("专利及论文")
    @TableField(value = "patents_and_papers",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> patentsAndPapers;

    /**
     * 各级人才称号
     */
    @ApiModelProperty("各级人才称号")
    @TableField(value = "talent_titles_at_all_levels",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> talentTitlesAtAllLevels;

    /**
     * 经费的支出明细账或流水
     */
    @ApiModelProperty("经费的支出明细账或流水")
    @TableField(value = "expense_detail_account_or_transaction",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> expenseDetailAccountOrTransaction;

    @ApiModelProperty("盖章版申报书")
    @TableField(value = "stamped_application_form",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> stampedApplicationForm;

    @ApiModelProperty("龙头企业关键核心技术攻关合作证明")
    @TableField(value = "ent_key_tec_proof",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> entKeyTecProof;

    @ApiModelProperty("专利交易或技术转移转化证明材料")
    @TableField(value = "patent_trans_proof",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> patentTransProof;

    /**
     * 租赁合同项目合同
     */
    @ApiModelProperty("项目合同")
    @TableField(value = "project_contract",
            typeHandler = AttachmentDTO.AttachmentListTypeHandler.class)
    private List<AttachmentDTO> projectContract;


}
