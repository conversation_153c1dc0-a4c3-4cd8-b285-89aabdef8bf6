package com.fengyun.udf.uaa.web.rest.flow;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.response.flowform.*;
import com.fengyun.udf.uaa.service.flowform.FlowFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "项目申报-企业", tags = "项目申报-企业")
@RestController
@RequestMapping({"/api/flowForm/manage"})
@Validated
@PreAuthorize("@pms.hasPermission('management')")
public class ManageFlowFormResource extends BaseResource {

    @Autowired
    private FlowFormService formService;

    @GetMapping("/condition/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/manage/condition/detail", methodDescription = "项目详情介绍详情", httpMethod = "GET")
    @ApiOperation(value = "项目详情介绍详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ConditionDetailDto>> conditionDetail(String projectId, String userId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.conditionDetail(projectId, userId));
    }

    @GetMapping("/responsible/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/manage/responsible/detail", methodDescription = "承担单位情况详情", httpMethod = "GET")
    @ApiOperation(value = "承担单位情况详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ResponsibleDetailDto>> responsibleDetail(String projectId, String userId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.responsibleDetail(projectId, userId));
    }

    @GetMapping("/person/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/manage/person/detail", methodDescription = "人员情况详情", httpMethod = "GET")
    @ApiOperation(value = "人员情况详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<SituationDetailDto>> personDetail(String projectId, String userId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.situationDetail(projectId, userId));
    }

    @GetMapping("/schedule/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/manage/schedule/detail", methodDescription = "项目执行计划详情", httpMethod = "GET")
    @ApiOperation(value = "项目执行计划详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ScheduleDetailDto>> scheduleDetail(String projectId, String userId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.scheduleDetail(projectId, userId));
    }

    @GetMapping("/funds/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/manage/funds/detail", methodDescription = "资金预算支出详情", httpMethod = "GET")
    @ApiOperation(value = "资金预算支出详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<FundDetailDto>> fundsDetail(String projectId, String userId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.fundDetail(projectId, userId));
    }

    @GetMapping("/attachment/detail")
    @CustomBaseException
    @AuditLog(method = "/api/flowForm/manage/attachment/detail", methodDescription = "附件详情", httpMethod = "GET")
    @ApiOperation(value = "附件详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<AttachmentDetailDto>> attachmentDetail(String projectId, String userId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), formService.attachmentDetail(projectId, userId));
    }

}
