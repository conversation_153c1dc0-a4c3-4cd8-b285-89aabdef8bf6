package com.fengyun.udf.uaa.web.rest.area;


import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.response.area.AreaWorldDTO;
import com.fengyun.udf.uaa.service.area.AreaWorldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(value = "地区-国家", description = "获得地区-国家", tags = "地区")
@RestController
@RequestMapping("/api/world")
public class AreaWorldResource extends BaseResource {

    @Autowired
    private AreaWorldService areaWorldService;

    @GetMapping("/continents/states")
    @ApiOperation(value = "国家地区搜索", notes = "国家地区搜索")
    public ResponseEntity<?> searchStates(@ApiParam("所属洲的id") @RequestParam(name ="continentId",required = false) String  continentId) {
        List<AreaWorldDTO> result = areaWorldService.searchStates(continentId);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }

    @GetMapping("/continents")
    @ApiOperation(value = "洲搜索", notes = "洲搜索")
    public ResponseEntity<?> searchContinents() {
        List<AreaWorldDTO> result = areaWorldService.searchContinents();
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }
}
