package com.fengyun.udf.uaa.web.rest.declareproject;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declareproject.DeclareEntHandlerDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntDetailDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntOverShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntShowDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.RuNodeTaskHistoryShowDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareEntService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@Api(value = "流程处理", tags = "流程处理")
@RestController
@RequestMapping({"/api/declare/flow"})
@Validated
@RequiredArgsConstructor
public class DeclareEntFlowResource extends BaseResource {

    private final DeclareEntService declareEntService;

    @GetMapping("/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/list", methodDescription = "流程处理-审核列表", httpMethod = "GET")
    @ApiOperation(value = "流程处理-审核列表")
    public ResponseEntity<ReturnResultDTO<Page<DeclareEntShowDTO>>> getDeclareProjectList(
            Page<DeclareEntShowDTO> page, String role, String keyWord, String declareId, String declareType) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                declareEntService.findList(page, role, keyWord, declareId, declareType));
    }

    @GetMapping("/export")
    @CustomBaseException
    @ApiOperation(value = "导出审核列表")
    public void exportDeclareProjectList(HttpServletResponse response, String keyWord, String declareType) {
        declareEntService.exportDeclareProjectList(response, keyWord, declareType);
    }

    @GetMapping("/export-all")
    @CustomBaseException
    @ApiOperation(value = "导出待审核+历史审核")
    public void exportAllDeclareProjectList(HttpServletResponse response, String keyWord, String declareType) {
        declareEntService.exportAllDeclareProjectList(response, keyWord, declareType);
    }

    @GetMapping("/export-projectperson")
    @CustomBaseException
    @ApiOperation(value = "导出项目人员")
    public void exportProjectPerson(HttpServletResponse response, String id) {
        declareEntService.exportProjectPerson(response, id);
    }

    @PostMapping("/back")
    @CustomBaseException
    @ApiOperation(value = "退回处理")
    public ResponseEntity<ReturnResultDTO> backFlow(String id) {
        declareEntService.backFlow(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/handler/update")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/handler/update", methodDescription = "流程处理-用户处理", httpMethod = "POST")
    @ApiOperation(value = "流程处理-用户处理")
    public ResponseEntity<ReturnResultDTO> handler(@RequestBody @Validated DeclareEntHandlerDTO declareEntHandlerDTO) {
        declareEntService.handler(declareEntHandlerDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/history/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/history/list", methodDescription = "流程处理-历史审核列表", httpMethod = "GET")
    @ApiOperation(value = "流程处理-历史审核列表")
    public ResponseEntity<ReturnResultDTO<Page<DeclareEntShowDTO>>> getHistoryList(Page<DeclareEntShowDTO> page,
                                                                                   String keyWord,
                                                                                   String declareType) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                declareEntService.getHistoryList(page, keyWord, declareType));
    }

    @GetMapping("/historynode/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/historynode/list", methodDescription = "流程处理-审核历史列表", httpMethod = "GET")
    @ApiOperation(value = "流程处理-审核历史记录")
    public ResponseEntity<ReturnResultDTO<Page<RuNodeTaskHistoryShowDTO>>> getHistoryNodeList(
            Page<RuNodeTaskHistoryShowDTO> page, String declareEntId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                declareEntService.getHistoryNodeList(page, declareEntId));
    }


    @GetMapping("/api/declare/flow/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/{id}", methodDescription = "流程处理-详情", httpMethod = "GET")
    @ApiOperation(value = "流程处理-详情")
    public ResponseEntity<ReturnResultDTO<DeclareEntDetailDTO>> getDetail(@PathVariable("id") String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.getDetail(id));
    }

    @GetMapping("/confirm/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/confirm/list", methodDescription = "流程处理-申报汇总列表", httpMethod = "GET")
    @ApiOperation(value = "流程处理-申报汇总列表")
    public ResponseEntity<ReturnResultDTO<Page<DeclareEntOverShowDTO>>> getDeclareConfirmProjectList(
            Page page, String keyWord) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.findOverList(page, keyWord));
    }

    @GetMapping("/firstPass/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/firstPass/list", methodDescription = "流程处理-获取第一次审核通过列表", httpMethod = "GET")
    @ApiOperation(value = "流程处理-获取第一次审核通过列表")
    public ResponseEntity<ReturnResultDTO<Page<DeclareEntShowDTO>>> getFirstPass(
            Page<DeclareEntShowDTO> page, String keyWord, String declareType, String isUploadedPPTVideo) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                declareEntService.getFirstPassList(page, keyWord, declareType,isUploadedPPTVideo));
    }

    @GetMapping("/second/relation/list")
    @CustomBaseException
    @AuditLog(method = "/api/declare/flow/second/relation/list", methodDescription = "根据用户-获取该用户配置过第二次评审的申报列表", httpMethod = "GET")
    @ApiOperation(value = "流程处理-获取第一次审核通过列表")
    public ResponseEntity<ReturnResultDTO<Page<String>>> getSecondRelation(Page<String> page,String type) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareEntService.getSecondRelationList(page,type));
    }

}

