package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 仪器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("in_instrument")
public class Instrument extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 设备名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备编号
     */
    @TableField("num")
    private String num;

    /**
     * 型号
     */
    @TableField("model")
    private String model;

    /**
     * 厂家
     */
    @TableField("factory")
    private String factory;

    /**
     * 放置地点
     */
    @TableField("address")
    private String address;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 每时间段人数
     */
    @TableField("person_num")
    private Integer personNum;

    /**
     * 仪器租赁展示价格
     */
    @TableField("lease_price_str")
    private String leasePriceStr;

    /**
     * 仪器租赁单价
     */
    @TableField("lease_price")
    private Double leasePrice;

    /**
     * 仪器租赁价格单位
     */
    @TableField("lease_price_unit")
    private String leasePriceUnit;

    /**
     * 仪器租赁最低收费
     */
    @TableField("lease_price_min")
    private Double leasePriceMin;

    /**
     * 委托测试展示价格
     */
    @TableField("test_price_str")
    private String testPriceStr;

    /**
     * 委托测试单价
     */
    @TableField("test_price")
    private Double testPrice;

    /**
     * 委托测试价格单位
     */
    @TableField("test_price_unit")
    private String testPriceUnit;

    /**
     * 计算方式
     */
    @TableField("calculate_method")
    private String calculateMethod;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 技术参数内容
     */
    @TableField("tec_content")
    private String tecContent;

    /**
     * 功能特色内容
     */
    @TableField("feature_content")
    private String featureContent;

    /**
     * 注意事项内容
     */
    @TableField("care_content")
    private String careContent;

    /**
     * 图片
     */
    @TableField("image")
    private String image;

    /**
     * 区域
     */
    @TableField("instrument_area")
    private String instrumentArea;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否全天预约
     */
    @TableField("is_all_day")
    private Boolean isAllDay;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    private Boolean deleted;


}
