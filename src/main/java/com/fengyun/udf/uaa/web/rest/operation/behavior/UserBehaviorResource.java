package com.fengyun.udf.uaa.web.rest.operation.behavior;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.feign.dto.FeignAuditLogDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.config.websocket.WebSocketService;
import com.fengyun.udf.uaa.dto.request.user.UserBehaviorDTO;
import com.fengyun.udf.uaa.dto.response.log.AuditLogDTO;
import com.fengyun.udf.uaa.service.log.AuditLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;

/**
 * Created by wangg on 2019/7/15.
 */
@RestController
@RequestMapping("/api")
@Api(value = "用户行为日志", tags = "用户行为日志")
@Validated
public class UserBehaviorResource extends BaseResource {

    @Autowired
    private WebSocketService webSocketService;

    @PostMapping("/log/user/behavior")
    @CustomBaseException
    @AuditLog(method = "/api/log/user/behavior", methodDescription = "用户行为日志", httpMethod = "POST")
    @ApiOperation(value = "用户行为日志")
    public ResponseEntity<ReturnResultDTO> saveUserBehavior(@RequestBody UserBehaviorDTO userBehaviorDTO, HttpServletRequest request) {
        userBehaviorDTO.setIp(getIpAddr(request));
        webSocketService.saveUserBehavior(userBehaviorDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }


    private String getIpAddr(HttpServletRequest request) {
        String ipAddress = null;
        try {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (ipAddress.equals("127.0.0.1")) {
                    // 根据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    ipAddress = inet.getHostAddress();
                }
            }
            // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
                // = 15
                if (ipAddress.indexOf(",") > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
                }
            }
        } catch (Exception e) {
            ipAddress = "";
        }
        return ipAddress;
    }

}
