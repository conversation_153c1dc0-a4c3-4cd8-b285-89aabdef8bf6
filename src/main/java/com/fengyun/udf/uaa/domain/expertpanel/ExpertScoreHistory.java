package com.fengyun.udf.uaa.domain.expertpanel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

@TableName("t_expert_score_history")
@Data
public class ExpertScoreHistory {

    @TableId("id")
    private String id;

    @TableField("declare_ent_id")
    private String declareEntId;

    @TableField("score_config")
    private String scoreConfig;

    @TableField("total_score")
    private String totalScore;

    @TableField("status")
    private String status;

    @TableField("created_id")
    private String createdId;

    @TableField("updated_id")
    private String updatedId;

    @TableField("created_date")
    private ZonedDateTime createdDate;

    @TableField("updated_date")
    private ZonedDateTime updatedDate;

    @TableField("score_index")
    private String scoreIndex;

}