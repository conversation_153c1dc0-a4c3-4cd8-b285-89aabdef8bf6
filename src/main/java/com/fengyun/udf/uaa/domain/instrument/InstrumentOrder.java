package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仪器预约
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
@TableName("in_instrument_order")
public class InstrumentOrder extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 仪器id
     */
    @TableField("instrument_id")
    private String instrumentId;

    /**
     * 租赁类型
     */
    @TableField("lease_type")
    private String leaseType;

    /**
     * 企业名称
     */
    @TableField("en_name")
    private String enName;

    /**
     * 预约联系人
     */
    @TableField("contact")
    private String contact;

    /**
     * 预约联系方式
     */
    @TableField("contact_tel")
    private String contactTel;

    /**
     * 预约联系邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 是否熟练使用仪器
     */
    @TableField("skilled_use")
    private Boolean skilledUse;

    /**
     * 是否已签框架协议
     */
    @TableField("sign_agreement")
    private Boolean signAgreement;

    /**
     * 预计使用样品数（个）
     */
    @TableField("use_sample_num")
    private String useSampleNum;

    /**
     * 样品名称
     */
    @TableField("sample_name")
    private String sampleName;
    /**
     * 样品包装
     */
    @TableField("sample_package")
    private String samplePackage;
    /**
     * 样品规格
     */
    @TableField("sample_spec")
    private String sampleSpec;
    /**
     * 样品存放要求
     */
    @TableField("sample_deposit")
    private String sampleDeposit;
    /**
     * 样品特性
     */
    @TableField("sample_feature")
    private String sampleFeature;
    /**
     * 检测项目
     */
    @TableField("test_project")
    private String testProject;
    /**
     * 检测条件
     */
    @TableField("test_condition")
    private String testCondition;
    /**
     * 检测方法
     */
    @TableField("test_method")
    private String testMethod;
    /**
     * 实验内容
     */
    @TableField("content")
    private String content;
    /**
     * 证明材料
     */
    @TableField("file")
    private String file;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 评价时间
     */
    @TableField("evaluate_time")
    private LocalDateTime evaluateTime;

    /**
     * 评价内容
     */
    @TableField("evaluate_content")
    private String evaluateContent;

    /**
     * 评价人
     */
    @TableField("evaluate_user")
    private String evaluateUser;

    /**
     * 结算状态
     */
    @TableField("settlement_status")
    private String settlementStatus;

    /**
     * 结算时间
     */
    @TableField("settlement_time")
    private LocalDateTime settlementTime;

    /**
     * 结算人
     */
    @TableField("settlement_user")
    private String settlementUser;

    /**
     * 支付方式
     */
    @TableField("pay_mode")
    private String payMode;

    /**
     * 支付方式其他
     */
    @TableField("pay_mode_other")
    private String payModeOther;

    /**
     * 发票
     */
    @TableField("invoice_file")
    private String invoiceFile;

    /**
     * 发送发票时间
     */
    @TableField("send_invoice_time")
    private LocalDateTime sendInvoiceTime;

    /**
     * 预约编号
     */
    @TableField("order_num")
    private String orderNum;


}
