package com.fengyun.udf.uaa.domain.inspection.middle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "declare_ent_middle_participant")
@TableName(value = "declare_ent_middle_participant", autoResultMap = true)
public class DeclareEntMiddleParticipant extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @TableField("project_id")
    private String projectId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @TableField("name")
    private String name;

    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    @TableField("belonging_company")
    private String belongingCompany;

    /**
     * 职务
     */
    @ApiModelProperty("职务")
    @TableField("position")
    private String position;

    /**
     * 在本项目中承担主要工作
     */
    @ApiModelProperty("在本项目中承担主要工作")
    @TableField("work")
    private String work;

    /**
     * 备注(如有人员增减，请说明原因)
     */
    @ApiModelProperty("备注(如有人员增减，请说明原因)")
    @TableField("remarks")
    private String remarks;

    /**
     * 是否为国创中心双聘人才
     */
    @ApiModelProperty("是否为国创中心双聘人才")
    private Boolean doubleHire;

    @ApiModelProperty("排序")
    @TableField("sort_num")
    private Integer sortNum;

}
