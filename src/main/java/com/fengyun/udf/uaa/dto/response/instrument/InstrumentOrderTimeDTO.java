package com.fengyun.udf.uaa.dto.response.instrument;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class InstrumentOrderTimeDTO extends AbstractDTO {

    @ApiModelProperty("预约日期")
    private LocalDate orderDate;

    @ApiModelProperty("预约时间")
    @DictData(target = "orderTimeStr",codeType = "INSTRUMENT_ORDER_TIME")
    private String orderTime;
    private String orderTimeStr;

    @ApiModelProperty("仪器总人数")
    private Integer personNum;

    @ApiModelProperty("当前预约人数")
    private Integer nowPersonNum = 0;

    @ApiModelProperty("是否能预约")
    private Boolean canOrder;
}
