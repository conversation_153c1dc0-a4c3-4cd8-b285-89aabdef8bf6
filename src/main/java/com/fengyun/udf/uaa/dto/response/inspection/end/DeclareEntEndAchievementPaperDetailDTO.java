package com.fengyun.udf.uaa.dto.response.inspection.end;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntEndAchievementPaperDetailDTO", description = "DeclareEntEndAchievementPaperDetailDTO")
public class DeclareEntEndAchievementPaperDetailDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 论文标题
     */
    @ApiModelProperty("论文标题")
    private String paperTitle;

    /**
     * 第一作者单位
     */
    @ApiModelProperty("第一作者单位")
    private String firstAuthorAffiliation;

    /**
     * 期刊名称
     */
    @ApiModelProperty("期刊名称")
    private String journalName;

    /**
     * 录用日期
     */
    @ApiModelProperty("录用日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8", locale = "zh")
    private LocalDate acceptanceDate;

    /**
     * 影响因子
     */
    @ApiModelProperty("影响因子")
    private String impactFactor;
}
