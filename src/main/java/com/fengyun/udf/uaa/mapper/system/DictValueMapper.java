package com.fengyun.udf.uaa.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.domain.system.DictValue;
import com.fengyun.udf.uaa.dto.response.system.DictValueDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DictValueMapper extends BaseMapper<DictValue> {
    List<Integer> selectSeqByCode(String typeCode);

    List<DictValueDTO> selectByTypeCode(String typeCode);

    List<DictValueDTO> selectByMultipleTypeCode(@Param("types") List<String> types);
}
