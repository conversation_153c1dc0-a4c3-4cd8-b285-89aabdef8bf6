package com.fengyun.udf.uaa.web.rest.flow;


import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.flowform.CreateShenBaoDto;
import com.fengyun.udf.uaa.service.flowform.ShenBaoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "项目申报-企业-简版", tags = "项目申报-企业-简版")
@RestController
@RequestMapping({"/api/shenbao"})
@Validated
public class CustomShenbaoResource extends BaseResource {

    @Autowired
    private ShenBaoService shenBaoService;

    @PostMapping("/submit")
    @CustomBaseException
    @AuditLog(method = "/api/shenbao/submit", methodDescription = "申报项目", httpMethod = "POST")
    @ApiOperation(value = "申报项目")
    public ResponseEntity<ReturnResultDTO> submit(CreateShenBaoDto dto) {
        shenBaoService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

}
