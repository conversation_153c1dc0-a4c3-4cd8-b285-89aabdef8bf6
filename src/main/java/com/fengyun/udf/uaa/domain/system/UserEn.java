package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_user_en")
public class UserEn {
    @TableId("ID")
    private String id;

    @TableField("USER_ID")
    private String userId;

    @TableField("EN_ID")
    private String enId;
}
