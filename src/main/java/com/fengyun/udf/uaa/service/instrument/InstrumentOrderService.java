package com.fengyun.udf.uaa.service.instrument;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.interceptor.CodeDecorator;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.sms.util.SmsTemplate;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.instrument.*;
import com.fengyun.udf.uaa.domain.system.DictValue;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.instrument.*;
import com.fengyun.udf.uaa.dto.response.instrument.*;
import com.fengyun.udf.uaa.fastdfs.ClientGlobal;
import com.fengyun.udf.uaa.fastdfs.client.UploadFile;
import com.fengyun.udf.uaa.fastdfs.client.UploadManager;
import com.fengyun.udf.uaa.mapper.instrument.*;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.holiday.HolidayService;
import com.fengyun.udf.uaa.service.system.DictService;
import com.spire.doc.Document;
import com.spire.doc.FileFormat;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仪器预约 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class InstrumentOrderService extends BaseService {

    private final InstrumentOrderMapper instrumentOrderMapper;
    private final InstrumentOrderAuditRecordMapper auditRecordMapper;
    private final InstrumentMapper instrumentMapper;
    private final InstrumentForbiddenTimeMapper forbiddenTimeMapper;
    private final DictService dictService;
    private final HolidayService holidayService;
    private final InstrumentOrderReceiptMapper orderReceiptMapper;
    private final InstrumentOrderWorkMapper orderWorkMapper;
    private final ConsumableMapper consumableMapper;
    private final InstrumentOrderConsumableMapper orderConsumableMapper;
    private final InstrumentConsumableMapper instrumentConsumableMapper;
    private final InstrumentOrderDateMapper orderDateMapper;

    private final CodeDecorator codeDecorator;

    public void create(InstrumentOrderCreateDTO dto) {
        Instrument instrument = instrumentMapper.selectById(dto.getInstrumentId());
        if (instrument == null || instrument.getDeleted()) {
            throw new ValidationException("error", "仪器不存在");
        }
        if (!Constants.INSTRUMENT_STATUS.A.name().equals(instrument.getStatus())) {
            throw new ValidationException("error", "仪器不可预约状态");
        }

        if ("lease".equals(dto.getLeaseType())) {
            for (InstrumentOrderDateDTO orderDateDTO : dto.getOrderDateDTOList()) {
                LocalDate orderDate = orderDateDTO.getOrderDate();

                //判断是否工作日
                checkHoliday(orderDate);

                //判断是否在禁用时间段
                checkForbidden(dto.getInstrumentId(), orderDateDTO.getOrderTime(), orderDate);

                //判断该时间段是否已约满
                checkOrderAgreed(dto.getInstrumentId(), orderDateDTO.getOrderTime(), instrument.getPersonNum(), orderDate);
            }
        }

        InstrumentOrder instrumentOrder = new InstrumentOrder();
        BeanUtils.copyProperties(dto, instrumentOrder);

        instrumentOrder.setStatus(Constants.INSTRUMENT_ORDER_STATUS.pre.name());
        instrumentOrder.setSettlementStatus(Constants.INSTRUMENT_ORDER_SETTLEMENT_STATUS.NO.name());
        instrumentOrder.create(SecurityUtils.getUserId());

        instrumentOrderMapper.insert(instrumentOrder);

        //预约时间
        createOrderDate(instrumentOrder.getId(), dto.getOrderDateDTOList());

        //预约耗材
        createConsumable(instrumentOrder.getId(), dto.getList());

        createAuditRecord(instrumentOrder.getId(), Constants.INSTRUMENT_ORDER_AUDIT_NODE.qy.name(), Constants.INSTRUMENT_ORDER_STATUS.pre.name(), null);
    }

    private void createOrderDate(Integer id, List<InstrumentOrderDateDTO> orderDateDTOList) {
        if (CollectionUtils.isNotEmpty(orderDateDTOList)) {
            for (InstrumentOrderDateDTO instrumentOrderDateDTO : orderDateDTOList) {
                InstrumentOrderDate instrumentOrderDate = new InstrumentOrderDate();
                instrumentOrderDate.setOrderId(id);
                instrumentOrderDate.setOrderDate(instrumentOrderDateDTO.getOrderDate());
                instrumentOrderDate.setOrderTime(instrumentOrderDateDTO.getOrderTime());
                orderDateMapper.insert(instrumentOrderDate);
            }
        }
    }

    private void createConsumable(Integer id, List<InstrumentOrderConsumableDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (InstrumentOrderConsumableDTO dto : list) {
                Consumable consumable = consumableMapper.selectById(dto.getConsumableId());
                if (consumable != null) {
                    if (dto.getNum() > consumable.getNumResidue()) {
                        throw new ValidationException("error", "耗材库存不够");
                    }
                    InstrumentOrderConsumable orderConsumable = new InstrumentOrderConsumable();
                    orderConsumable.setOrderId(id);
                    orderConsumable.setConsumableId(dto.getConsumableId());
                    orderConsumable.setNum(dto.getNum());
                    orderConsumableMapper.insert(orderConsumable);
                }
            }
        }

    }

    private void checkOrderAgreed(String instrumentId, String orderTime, int personNum, LocalDate orderDate) {
        List<String> timeList = new ArrayList<>();
        Map<String, Integer> map = getAgreedTime(instrumentId, orderDate);
        if (map != null && map.size() > 0) {
            for (String s : orderTime.split(",")) {
                if (map.containsKey(s)) {
                    int value = map.get(s);
                    if (value >= personNum) {
                        timeList.add(dictCacheClient.getDict("INSTRUMENT_ORDER_TIME", s));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(timeList)) {
                throw new ValidationException("error", orderDate.toString() + "以下时间段已约满：" + String.join(",", timeList));
            }
        }
    }

    private void checkForbidden(String instrumentId, String orderTime, LocalDate orderDate) {
        Set<String> set = getForbiddenIndex(instrumentId, orderDate);
        if (CollectionUtils.isNotEmpty(set)) {
            List<String> timeList = new ArrayList<>();
            for (String s : orderTime.split(",")) {
                if (set.contains(s)) {
                    timeList.add(dictCacheClient.getDict("INSTRUMENT_ORDER_TIME", s));
                }
            }
            if (CollectionUtils.isNotEmpty(timeList)) {
                throw new ValidationException("error", orderDate.toString() + "以下时间段已禁用：" + String.join(",", timeList));
            }
        }
    }

    public Set<String> getForbiddenIndex(String instrumentId, LocalDate orderDate) {
        Set<String> set = new HashSet<>();
        List<InstrumentForbiddenTime> forbiddenTimes = forbiddenTimeMapper.selectList(new LambdaQueryWrapper<InstrumentForbiddenTime>()
                .eq(InstrumentForbiddenTime::getInstrumentId, instrumentId)
                .lt(InstrumentForbiddenTime::getStartTime, orderDate.plusDays(1))
                .ge(InstrumentForbiddenTime::getEndTime, orderDate));
        if (CollectionUtils.isNotEmpty(forbiddenTimes)) {
            long st = orderDate.atTime(9, 0).toEpochSecond(ZoneOffset.ofHours(8));
            long e = orderDate.atTime(17, 0).toEpochSecond(ZoneOffset.ofHours(8));
            for (InstrumentForbiddenTime forbiddenTime : forbiddenTimes) {
                long sf = forbiddenTime.getStartTime().toEpochSecond(ZoneOffset.ofHours(8));
                long ef = forbiddenTime.getEndTime().toEpochSecond(ZoneOffset.ofHours(8));
                if (!(st >= ef || e <= sf)) {
                    int startIndex = 0;
                    int endIndex = 15;
                    if (sf > st && e > ef) {
                        startIndex = (int) ((sf - st) / 1800);
                        endIndex = 15 - (int) ((e - ef) / 1800);
                    } else if (sf > st && e <= ef) {
                        startIndex = (int) ((sf - st) / 1800);
                    } else if (sf <= st && e > ef) {
                        endIndex = 15 - (int) ((e - ef) / 1800);
                    }
                    for (int i = startIndex; i <= endIndex; i++) {
                        set.add(i + "");
                    }
                }
            }
        }
        return set;
    }

    public Map<String, Integer> getAgreedTime(String instrumentId, LocalDate orderDate) {
        Map<String, Integer> map = new HashMap<>();
        List<String> list = instrumentOrderMapper.selectOrderDateTime(instrumentId, orderDate);

        if (CollectionUtils.isNotEmpty(list)) {
            for (String orderTime : list) {
                for (String s : orderTime.split(",")) {
                    int value = 1;
                    if (map.containsKey(s)) {
                        value = map.get(s) + 1;
                    }
                    map.put(s, value);
                }
            }
        }
        return map;
    }

    private void createAuditRecord(Integer orderId, String node, String status, String remark) {
        InstrumentOrderAuditRecord auditRecord = new InstrumentOrderAuditRecord();
        auditRecord.setOrderId(orderId);
        auditRecord.setNode(node);
        auditRecord.setStatus(status);
        auditRecord.setRemark(remark);
        auditRecord.setAuditUser(SecurityUtils.getUserId());
        auditRecord.setAuditTime(LocalDateTime.now());
        auditRecordMapper.insert(auditRecord);
    }

    private void checkHoliday(LocalDate orderDate) {
//        DayOfWeek dayOfWeek = orderDate.getDayOfWeek();
        //判断预约时间是否为节假日
        if (holidayService.isHoliday(orderDate.toString())) {
            throw new ValidationException("error", orderDate.toString() + "节假日无法预约");
        }
        //判断预约时间是否为调休
//        if (dayOfWeek.getValue() > 5 && !holidayService.isMakeUp(orderDate.toString())) {
//            throw new ValidationException("error", orderDate.toString() + "周末无法预约");
//        }
    }

    public void update(InstrumentOrderUpdateDTO dto) {
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.back.name().equals(instrumentOrder.getStatus())
                && !Constants.INSTRUMENT_ORDER_STATUS.cancel.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "不能修改");
        }

        Instrument instrument = instrumentMapper.selectById(dto.getInstrumentId());
        if (instrument == null || instrument.getDeleted()) {
            throw new ValidationException("error", "仪器不存在");
        }
        if (!Constants.INSTRUMENT_STATUS.A.name().equals(instrument.getStatus())) {
            throw new ValidationException("error", "仪器不可预约状态");
        }

        if ("lease".equals(dto.getLeaseType())) {
            for (InstrumentOrderDateDTO orderDateDTO : dto.getOrderDateDTOList()) {
                LocalDate orderDate = orderDateDTO.getOrderDate();

                //判断是否工作日
                checkHoliday(orderDate);

                //判断是否在禁用时间段
                checkForbidden(dto.getInstrumentId(), orderDateDTO.getOrderTime(), orderDate);

                //判断该时间段是否已约满
                checkOrderAgreed(dto.getInstrumentId(), orderDateDTO.getOrderTime(), instrument.getPersonNum(), orderDate);
            }
        }

        BeanUtils.copyProperties(dto, instrumentOrder);
        instrumentOrder.setStatus(Constants.INSTRUMENT_ORDER_STATUS.pre.name());
        instrumentOrder.update(SecurityUtils.getUserId());

        instrumentOrderMapper.updateById(instrumentOrder);

        //预约时间
        orderDateMapper.delete(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, instrumentOrder.getId()));
        createOrderDate(instrumentOrder.getId(), dto.getOrderDateDTOList());

        //预约耗材
        orderConsumableMapper.delete(new LambdaQueryWrapper<InstrumentOrderConsumable>().eq(InstrumentOrderConsumable::getOrderId, instrumentOrder.getId()));
        createConsumable(instrumentOrder.getId(), dto.getList());

        createAuditRecord(instrumentOrder.getId(), Constants.INSTRUMENT_ORDER_AUDIT_NODE.qy.name(), Constants.INSTRUMENT_ORDER_STATUS.pre.name(), null);
    }

    public InstrumentOrderDetailDTO detail(Integer id) {
        return getInstrumentOrderDetailDTO(id, null);
    }

    public InstrumentOrderDetailDTO getInstrumentOrderDetailDTO(Integer id, String userId) {
        InstrumentOrderDetailDTO dto = new InstrumentOrderDetailDTO();
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(id);
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (StringUtils.isNotBlank(userId) && !userId.equals(instrumentOrder.getCreatedId())) {
            throw new ValidationException("error", "不存在");
        }
        BeanUtils.copyProperties(instrumentOrder, dto);

        InstrumentOrderWork orderWork = orderWorkMapper.selectById(id);
        if (orderWork != null && !"draft".equals(orderWork.getStatus())) {
            dto.setAmount(orderWork.getAmount());
        }

        Instrument instrument = instrumentMapper.selectById(instrumentOrder.getInstrumentId());
        if (instrument != null) {
            dto.setInstrumentName(instrument.getName());
            dto.setCalculateMethod(instrument.getCalculateMethod());
        }

        List<InstrumentOrderAuditRecord> list = auditRecordMapper.selectList(new LambdaQueryWrapper<InstrumentOrderAuditRecord>()
                .eq(InstrumentOrderAuditRecord::getOrderId, id).orderByDesc(InstrumentOrderAuditRecord::getAuditTime));

        List<InstrumentOrderAuditRecordDTO> records = new ArrayList<>();
        for (InstrumentOrderAuditRecord auditRecord : list) {
            InstrumentOrderAuditRecordDTO recordDTO = new InstrumentOrderAuditRecordDTO();
            BeanUtils.copyProperties(auditRecord, recordDTO);
            records.add(recordDTO);
        }
        dto.setRecords(records);

        List<InstrumentOrderConsumableDTO> orderConsumableList = new ArrayList<>();
        List<ConsumableListDTO> consumableList = getInstrumentConsumable(instrumentOrder.getInstrumentId());
        List<InstrumentOrderConsumable> consumables = orderConsumableMapper.selectList(new LambdaQueryWrapper<InstrumentOrderConsumable>().eq(InstrumentOrderConsumable::getOrderId, instrumentOrder.getId()));
        if (CollectionUtils.isNotEmpty(consumables)) {
            for (InstrumentOrderConsumable consumable : consumables) {
                InstrumentOrderConsumableDTO orderConsumableDTO = new InstrumentOrderConsumableDTO();
                BeanUtils.copyProperties(consumable, orderConsumableDTO);
                orderConsumableList.add(orderConsumableDTO);
            }
        }
        dto.setConsumableList(consumableList);
        dto.setOrderConsumableList(orderConsumableList);

        List<InstrumentOrderDateDTO> orderDateDTOList = new ArrayList<>();
        String orderDateStr = null;
        if ("lease".equals(instrumentOrder.getLeaseType())) {
            List<InstrumentOrderDate> orderDates = orderDateMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, id));
            orderDateStr = getOrderDateStr(orderDates);
            for (InstrumentOrderDate orderDate : orderDates) {
                InstrumentOrderDateDTO orderDateDTO = new InstrumentOrderDateDTO();
                BeanUtils.copyProperties(orderDate, orderDateDTO);
                orderDateDTOList.add(orderDateDTO);
            }
        }
        dto.setOrderDateDTOList(orderDateDTOList);
        dto.setOrderDateStr(orderDateStr);

        return dto;
    }

    private String getOrderDateStr(List<InstrumentOrderDate> orderDates) {
        String orderDateStr = "";
        List<DictValue> dictValues = dictService.getByCode("INSTRUMENT_ORDER_TIME");
        Map<String, String> map = dictValues.stream().collect(Collectors.toMap(DictValue::getValue, DictValue::getLabel));
        for (InstrumentOrderDate orderDate : orderDates) {
            List<Integer> list = Arrays.stream(orderDate.getOrderTime().split(",")).map(s -> Integer.parseInt(s)).sorted().collect(Collectors.toList());

            String str = orderDate.getOrderDate().toString() + " ";

            List<String> stringList = new ArrayList<>();

            String label = map.get(list.get(0).toString());
            String[] labels = label.split("-");
            String startStr = labels[0];
            String endStr = labels[1];
            if (list.size() == 1) {
                str = str + label;
            } else {
                for (int i = 1; i < list.size(); i++) {
                    String l = map.get(list.get(i).toString());
                    String[] ls = l.split("-");
                    if (endStr.equals(ls[0])) {
                        endStr = ls[1];
                    } else {
                        stringList.add(startStr + "-" + endStr);
                        startStr = ls[0];
                        endStr = ls[1];
                    }
                }
                stringList.add(startStr + "-" + endStr);
                str = str + String.join(",", stringList);
            }

            if ("".equals(orderDateStr)) {
                orderDateStr = orderDateStr + str;
            } else {
                orderDateStr = orderDateStr + ";" + str;
            }
        }

        return orderDateStr;
    }

    public List<ConsumableListDTO> getInstrumentConsumable(String id) {
        List<ConsumableListDTO> list = new ArrayList<>();
        List<InstrumentConsumable> consumables = instrumentConsumableMapper.selectList(
                new LambdaQueryWrapper<InstrumentConsumable>().eq(InstrumentConsumable::getInstrumentId, id));
        if (CollectionUtils.isNotEmpty(consumables)) {
            List<String> consumableIds = consumables.stream().map(InstrumentConsumable::getConsumableId).collect(Collectors.toList());
            ConsumableQueryDTO queryDTO = new ConsumableQueryDTO();
            queryDTO.setStatus(Constants.CONSUMABLE_STATUS.YES.name());
            queryDTO.setIds(consumableIds);
            list = consumableMapper.selectPage(null, queryDTO);
        }
        return list;
    }

    public InstrumentOrderDetailDTO detailPre() {
        InstrumentOrderDetailDTO dto = new InstrumentOrderDetailDTO();
        String userId = SecurityUtils.getUserId();
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectOne(new LambdaQueryWrapper<InstrumentOrder>()
                .eq(InstrumentOrder::getCreatedId, userId).orderByDesc(InstrumentOrder::getCreatedDate).last("limit 1"));
        if (instrumentOrder != null) {
            BeanUtils.copyProperties(instrumentOrder, dto);
        }
        return dto;
    }

    public Page<InstrumentOrderListDTO> page(Page page, InstrumentOrderQueryDTO dto) {
        List<InstrumentOrderListDTO> list = instrumentOrderMapper.selectPage(page, dto);
        for (InstrumentOrderListDTO instrumentOrderListDTO : list) {
            instrumentOrderListDTO.setWorkBtnStatus(instrumentOrderListDTO.getWorkStatus());
            if (!Constants.INSTRUMENT_ORDER_WORK_STATUS.final_commit.name().equals(instrumentOrderListDTO.getWorkStatus())) {
                instrumentOrderListDTO.setWorkStatus("final_commit_no");
            }

            String orderDateStr = null;
            if ("lease".equals(instrumentOrderListDTO.getLeaseType())) {
                List<InstrumentOrderDate> orderDates = orderDateMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, instrumentOrderListDTO.getId()));
                orderDateStr = getOrderDateStr(orderDates);
            }
            instrumentOrderListDTO.setOrderDateStr(orderDateStr);

            instrumentOrderListDTO.setInvoiceStatus("NO");
            if (instrumentOrderListDTO.getSendInvoiceTime() != null) {
                instrumentOrderListDTO.setInvoiceStatus("YES");
            }
        }
        page.setRecords(list);
        return page;
    }

    public List<InstrumentOrderListDTO> export(InstrumentOrderQueryDTO dto) {
        if (StringUtils.isNotBlank(dto.getId())) {
            List<Integer> ids = new ArrayList<>();
            for (String s : dto.getId().split(",")) {
                ids.add(Integer.parseInt(s));
            }
            dto.setIds(ids);
        }

        List<InstrumentOrderListDTO> list = instrumentOrderMapper.selectPage(null, dto);
        int i = 1;
        for (InstrumentOrderListDTO listDTO : list) {
            if (!Constants.INSTRUMENT_ORDER_WORK_STATUS.final_commit.name().equals(listDTO.getWorkStatus())) {
                listDTO.setWorkStatus("final_commit_no");
            }

            codeDecorator.decorate(listDTO);
            listDTO.setIndex(i);
            listDTO.setSkilledUseStr("是");
            if (!listDTO.getSkilledUse()) {
                listDTO.setSkilledUseStr("否");
            }
            String orderDateStr = null;
            if ("lease".equals(listDTO.getLeaseType())) {
                List<InstrumentOrderDate> orderDates = orderDateMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, listDTO.getId()));
                orderDateStr = getOrderDateStr(orderDates);
            }
            listDTO.setOrderDateStr(orderDateStr);
            i++;
        }
        return list;
    }

    public List<InstrumentOrderTimeDTO> getOrderTime(String instrumentId, String orderDateStr) {
        Instrument instrument = instrumentMapper.selectById(instrumentId);
        if (instrument == null || instrument.getDeleted()) {
            throw new ValidationException("error", "仪器不存在");
        }
        if (!Constants.INSTRUMENT_STATUS.A.name().equals(instrument.getStatus())) {
            throw new ValidationException("error", "仪器不可预约状态");
        }

        List<InstrumentOrderTimeDTO> times = new ArrayList<>();
        List<DictValue> dictValues = getOrderTimeDict(instrument.getIsAllDay());

        LocalDate orderDate = LocalDate.parse(orderDateStr);
        boolean canOrder = true;
//        DayOfWeek dayOfWeek = orderDate.getDayOfWeek();

        //判断是否为过期日期
        if (orderDate.isBefore(LocalDate.now())) {
            canOrder = false;
        }
        //判断预约时间是否为节假日
        if (holidayService.isHoliday(orderDate.toString())) {
            canOrder = false;
        }
        //判断预约时间是否为调休
//        if (dayOfWeek.getValue() > 5 && !holidayService.isMakeUp(orderDate.toString())) {
//            canOrder = false;
//        }
        Set<String> set = null;
        Map<String, Integer> map = null;
        if (canOrder) {
            //获取禁用时间
            set = getForbiddenIndex(instrumentId, orderDate);
            //获取已约时间
            map = getAgreedTime(instrumentId, orderDate);
        }

        LocalDateTime now = LocalDateTime.now();
        for (DictValue dictValue : dictValues) {
            InstrumentOrderTimeDTO timeDTO = new InstrumentOrderTimeDTO();
            timeDTO.setOrderDate(orderDate);
            timeDTO.setOrderTime(dictValue.getValue());
            timeDTO.setOrderTimeStr(dictValue.getLabel());
            timeDTO.setCanOrder(canOrder);
            timeDTO.setPersonNum(instrument.getPersonNum());
            if (canOrder) {
                LocalDateTime localDateTime = LocalDateTime.parse(orderDate.toString() + " " + dictValue.getLabel().split("-")[0], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                if (localDateTime.isBefore(now)) {
                    timeDTO.setCanOrder(false);
                } else {
                    if (set.contains(dictValue.getValue())) {
                        timeDTO.setCanOrder(false);
                    }
                    if (map.containsKey(dictValue.getValue())) {
                        int nowPersonNum = map.get(dictValue.getValue());
                        if (nowPersonNum >= instrument.getPersonNum()) {
                            timeDTO.setCanOrder(false);
                        }
                        timeDTO.setNowPersonNum(nowPersonNum);
                    }
                }
            }
            times.add(timeDTO);
        }
        return times;
    }

    public List<DictValue> getOrderTimeDict(boolean isAllDay) {
        List<DictValue> dictValues = dictService.getByCode("INSTRUMENT_ORDER_TIME");
        if (!isAllDay) {
            return dictValues.stream().filter(i -> Integer.parseInt(i.getValue()) <= 15).collect(Collectors.toList());
        } else {
            return dictValues;
        }
    }

    public void cancel(Integer id) {
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(id);
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        String userId = SecurityUtils.getUserId();
        if (!userId.equals(instrumentOrder.getCreatedId())) {
            throw new ValidationException("error", "只能撤回本人提交的预约");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pre.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "只有待审核才能撤回");
        }
        instrumentOrder.setStatus(Constants.INSTRUMENT_ORDER_STATUS.cancel.name());
        instrumentOrder.update(userId);
        instrumentOrderMapper.updateById(instrumentOrder);

        createAuditRecord(instrumentOrder.getId(), Constants.INSTRUMENT_ORDER_AUDIT_NODE.qy.name(), Constants.INSTRUMENT_ORDER_STATUS.cancel.name(), null);
    }

    public void receipt(InstrumentOrderReceiptDTO dto) throws Exception {
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "审核未通过");
        }

        InstrumentOrderReceipt instrumentOrderReceipt = orderReceiptMapper.selectById(dto.getId());
        boolean isEdit = false;
        if (instrumentOrderReceipt == null) {
            instrumentOrderReceipt = new InstrumentOrderReceipt();
        } else {
            isEdit = true;
            if (instrumentOrderReceipt.getSendTime() != null) {
                throw new ValidationException("error", "已确认发送无法修改");
            }
        }
        BeanUtils.copyProperties(dto, instrumentOrderReceipt);
        if ("send".equals(dto.getStatus())) {
            instrumentOrderReceipt.setSendTime(LocalDateTime.now());
        }
        if (isEdit) {
            instrumentOrderReceipt.update(SecurityUtils.getUserId());
            orderReceiptMapper.updateById(instrumentOrderReceipt);
        } else {
            instrumentOrderReceipt.create(SecurityUtils.getUserId());
            orderReceiptMapper.insert(instrumentOrderReceipt);
        }

    }

    public InstrumentOrderReceiptDetailDTO getReceipt(Integer id) {
        InstrumentOrderReceiptDetailDTO detailDTO = new InstrumentOrderReceiptDetailDTO();
        InstrumentOrderReceipt instrumentOrderReceipt = orderReceiptMapper.selectById(id);
        if (instrumentOrderReceipt != null) {
            BeanUtils.copyProperties(instrumentOrderReceipt, detailDTO);
        }
        return detailDTO;
    }

    public synchronized void audit(InstrumentOrderAuditDTO dto) {
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(dto.getStatus())
                && !Constants.INSTRUMENT_ORDER_STATUS.reject.name().equals(dto.getStatus())
                && !Constants.INSTRUMENT_ORDER_STATUS.back.name().equals(dto.getStatus())) {
            throw new ValidationException("error", "审核状态不正确");
        }

        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pre.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "非待审核预约");
        }
        //审核通过验证耗材库存
        if (Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(dto.getStatus())) {
            List<InstrumentOrderConsumable> consumables = orderConsumableMapper.selectList(new LambdaQueryWrapper<InstrumentOrderConsumable>().eq(InstrumentOrderConsumable::getOrderId, instrumentOrder.getId()));
            if (CollectionUtils.isNotEmpty(consumables)) {
                for (InstrumentOrderConsumable orderConsumable : consumables) {
                    Consumable consumable = consumableMapper.selectById(orderConsumable.getConsumableId());
                    if (consumable == null || consumable.getDeleted() || Constants.CONSUMABLE_STATUS.NO.name().equals(consumable.getStatus())) {
                        throw new ValidationException("error", "预约耗材已删除或者已下架");
                    }
                    if (orderConsumable.getNum() > consumable.getNumResidue()) {
                        throw new ValidationException("error", "预约耗材库存不足");
                    }
                    consumable.setNumResidue(consumable.getNumResidue() - orderConsumable.getNum());
                    consumableMapper.updateById(consumable);
                }
            }

            //生成单号
            String orderNum = getOrderNum();
            instrumentOrder.setOrderNum(orderNum);
        }

        instrumentOrder.setStatus(dto.getStatus());
        instrumentOrder.update(SecurityUtils.getUserId());
        instrumentOrderMapper.updateById(instrumentOrder);

        createAuditRecord(instrumentOrder.getId(), Constants.INSTRUMENT_ORDER_AUDIT_NODE.audit.name(), dto.getStatus(), dto.getRemark());

        //短信
        Map<String, String> paramMap = new HashMap<>();
        SmsTemplate smsTemplate = null;
        if (Constants.INSTRUMENT_ORDER_STATUS.back.name().equals(dto.getStatus())) {
            smsTemplate = smsProperties.getOrderBack();
        } else if (Constants.INSTRUMENT_ORDER_STATUS.reject.name().equals(dto.getStatus())) {
            smsTemplate = smsProperties.getOrderReject();
        } else if (Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(dto.getStatus())) {
            smsTemplate = smsProperties.getOrderPass();
        }
        try {
            if (smsTemplate != null) {
                sendSms(instrumentOrder.getContactTel(), paramMap, smsTemplate);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private synchronized String getOrderNum() {
        LocalDate now = LocalDate.now();
        String year = now.getYear() + "-";
        String orderNum = instrumentOrderMapper.selectNextOrderNum(year);
        int i = 1;
        if (StringUtils.isNotBlank(orderNum)) {
            i = Integer.parseInt(orderNum.replaceFirst(year, "")) + 1;
        }
        return year + NumberUtil.decimalFormat("000000", i);
    }

    public void auditBack(InstrumentOrderAuditDTO dto) {
        if (StringUtils.isBlank(dto.getRemark())) {
            throw new ValidationException("error", "审核意见不能为空");
        }
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "审核通过才能强制退回");
        }

        //耗材库存修改
        List<InstrumentOrderConsumable> consumables = orderConsumableMapper.selectList(new LambdaQueryWrapper<InstrumentOrderConsumable>().eq(InstrumentOrderConsumable::getOrderId, instrumentOrder.getId()));
        if (CollectionUtils.isNotEmpty(consumables)) {
            for (InstrumentOrderConsumable orderConsumable : consumables) {
                Consumable consumable = consumableMapper.selectById(orderConsumable.getConsumableId());
                if (consumable == null || consumable.getDeleted() || Constants.CONSUMABLE_STATUS.NO.name().equals(consumable.getStatus())) {
                    throw new ValidationException("error", "预约耗材已删除或者已下架");
                }
                consumable.setNumResidue(consumable.getNumResidue() + orderConsumable.getNum());
                consumableMapper.updateById(consumable);
            }
        }
        instrumentOrder.setEvaluateTime(null);
        instrumentOrder.setEvaluateContent(null);
        instrumentOrder.setEvaluateUser(null);
        instrumentOrder.setSettlementStatus(Constants.INSTRUMENT_ORDER_SETTLEMENT_STATUS.NO.name());
        instrumentOrder.setSettlementUser(null);
        instrumentOrder.setSettlementTime(null);
        instrumentOrder.setInvoiceFile(null);
        instrumentOrder.setSendInvoiceTime(null);
        instrumentOrder.setStatus(Constants.INSTRUMENT_ORDER_STATUS.back.name());
        instrumentOrder.update(SecurityUtils.getUserId());
        instrumentOrderMapper.updateById(instrumentOrder);

        //删除收据、工单
        orderReceiptMapper.deleteById(dto.getId());
        orderWorkMapper.deleteById(dto.getId());
        //审核历史
        createAuditRecord(instrumentOrder.getId(), Constants.INSTRUMENT_ORDER_AUDIT_NODE.audit.name(), Constants.INSTRUMENT_ORDER_STATUS.back.name(), dto.getRemark());

        //短信
        Map<String, String> paramMap = new HashMap<>();
        SmsTemplate smsTemplate = smsProperties.getOrderBack();
        try {
            if (smsTemplate != null) {
                sendSms(instrumentOrder.getContactTel(), paramMap, smsTemplate);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void evaluate(InstrumentOrderEvaluateDTO dto) {
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "审核未通过");
        }
        instrumentOrder.setEvaluateTime(LocalDateTime.now());
        instrumentOrder.setEvaluateContent(dto.getEvaluateContent());
        instrumentOrder.setEvaluateUser(SecurityUtils.getUserId());

        instrumentOrder.update(SecurityUtils.getUserId());
        instrumentOrderMapper.updateById(instrumentOrder);
    }

    /**
     * 生成收据pdf
     *
     * @return 附件信息
     * @throws Exception 生成pdf异常
     */
    public AttachmentDTO generateReceiptPdf(InstrumentOrderReceiptDTO dto) throws Exception {
        // 生成收据编号
        String no = NumberUtil.decimalFormat("0000000", dto.getId());
        // 获取收据模板
        Workbook book = new Workbook();
        try (InputStream in = new ClassPathResource("static/templates/orderReceipt.xlsx").getInputStream()) {
            book.loadFromStream(in);
        }
        Worksheet sheet = book.getWorksheets().get(0);
        // 收据编号
        sheet.get(2, 19).setText(String.valueOf(no));
        // 收款时间 非必填判断
        if (StringUtils.isNotBlank(dto.getPayDate())) {
            LocalDate date = LocalDate.parse(dto.getPayDate());
            // 年
            sheet.get(3, 16).setText(String.valueOf(date.getYear()));
            // 月
            sheet.get(3, 19).setText(String.valueOf(date.getMonthValue()));
            // 日
            sheet.get(3, 21).setText(String.valueOf(date.getDayOfMonth()));
        }
        //交款单位
        sheet.get(3, 2).setText("交款单位：" + dto.getPayCompany());
        // 格式化金额并分隔为单个字符串
        BigDecimal amount = BigDecimal.valueOf(dto.getPayAmount()).setScale(2, RoundingMode.DOWN);
        List<String> amountList = Arrays.stream(amount.toString().replace(".", "").split(""))
                .map(String::valueOf).collect(Collectors.toList());
        // 小写金额
        for (int i = 0; i < amountList.size() && i < 9; i++) {
            sheet.get(6, 22 - i).setText(amountList.get(amountList.size() - 1 - i));
        }
        // 人民币标识
        sheet.get(6, 22 - amountList.size()).setText("￥");
        // 大写金额
        for (int i = 0; i < 10; i++) {
            sheet.get(7, 21 - i * 2).setText(amountList.size() - i > 0
                    ? Constants.n2cMap.get(amountList.get(amountList.size() - 1 - i)) : "/");
        }

        //收款事由
        sheet.get(6, 2).setText(dto.getPayReason());
        String payTypeStr = "";
        if (StringUtils.isNotBlank(dto.getPayType())) {
            payTypeStr = dictCacheClient.getDict("INSTRUMENT_ORDER_RECEIPT_PAY_TYPE", dto.getPayType());
        }
        //收款方式
        sheet.get(6, 10).setText(payTypeStr);

        // 联系人
        sheet.get(8, 3).setText(dto.getContact());
        // 联系电话
        sheet.get(9, 3).setText(dto.getContactTel());
        // 保存为字节数组用于上传
        byte[] bytes;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            sheet.saveToPdfStream(out);
            bytes = out.toByteArray();
        }
        // 上传文件
        UploadFile uploadfile = new UploadFile("收据", bytes, "pdf");
        String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
        // 异步删除旧的PDF文件
//        asyncDeleteOldReceiptPdf(projectReceipt);
        // 保存并返回生成的模板
        AttachmentDTO attachmentDTO = new AttachmentDTO();
        attachmentDTO.setUrl(ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
        attachmentDTO.setSize((long) bytes.length);
        attachmentDTO.setName("收据.pdf");
//        projectReceipt.setTemplate(JSON.toJSONString(attachmentDTO));
//        projectReceipt.update(SecurityUtils.getUserId());
//        projectReceiptMapper.update(projectReceipt, new LambdaUpdateWrapper<ProjectReceipt>()
//                .set(ProjectReceipt::getTemplate,JSON.toJSONString(attachmentDTO))
//                .eq(ProjectReceipt::getId,projectReceipt.getId()));
        return attachmentDTO;
    }

    public AttachmentDTO editWork(InstrumentOrderEditWorkDTO dto) throws Exception {
        InstrumentOrderWork orderWork = orderWorkMapper.selectById(dto.getId());
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "审核未通过");
        }
        Instrument instrument = instrumentMapper.selectById(instrumentOrder.getInstrumentId());
        if (instrument == null || instrument.getDeleted()) {
            throw new ValidationException("error", "仪器不存在");
        }

        AttachmentDTO attachmentDTO = null;
        if (orderWork == null) {
            orderWork = new InstrumentOrderWork();
            orderWork.setLeasePrice(instrument.getLeasePrice());
            orderWork.setLeasePriceUnit(instrument.getLeasePriceUnit());
            orderWork.setLeasePriceMin(instrument.getLeasePriceMin());
            orderWork.setTestPrice(instrument.getTestPrice());
            orderWork.setTestPriceUnit(instrument.getTestPriceUnit());
            orderWork.setCalculateMethod(instrument.getCalculateMethod());
            BeanUtils.copyProperties(dto, orderWork);
            orderWork.create(SecurityUtils.getUserId());

            attachmentDTO = createWorkPdf(instrumentOrder, instrument, orderWork);
            orderWork.setWorkPdf(JSON.toJSONString(attachmentDTO));
            orderWorkMapper.insert(orderWork);
        } else {
            BeanUtils.copyProperties(dto, orderWork);
            orderWork.update(SecurityUtils.getUserId());

            attachmentDTO = createWorkPdf(instrumentOrder, instrument, orderWork);
            orderWork.setWorkPdf(JSON.toJSONString(attachmentDTO));
            orderWorkMapper.updateById(orderWork);
        }
        return attachmentDTO;

    }

    public void finalWork(InstrumentOrderFinalWorkDTO dto) {
        InstrumentOrderWork orderWork = orderWorkMapper.selectById(dto.getId());
        if (orderWork == null) {
            throw new ValidationException("error", "工单不存在");
        }
        BeanUtils.copyProperties(dto, orderWork);
        if ("final_commit".equals(dto.getStatus())) {
            orderWork.setSendTime(LocalDateTime.now());
        }
        orderWork.update(SecurityUtils.getUserId());
        orderWorkMapper.updateById(orderWork);
    }

    public void finalWorkEdit(InstrumentOrderFinalWorkEditDTO dto) {
        InstrumentOrderWork orderWork = orderWorkMapper.selectById(dto.getId());
        if (orderWork == null) {
            throw new ValidationException("error", "工单不存在");
        }
        if (!"final_commit".equals(orderWork.getStatus())) {
            throw new ValidationException("error", "工单状态错误");
        }
        orderWork.setWorkStampPdf(dto.getWorkStampPdf());
        orderWork.update(SecurityUtils.getUserId());
        orderWorkMapper.updateById(orderWork);
    }

    public void settlement(String ids) {
        for (String id : ids.split(",")) {
            InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(Integer.parseInt(id));
            if (instrumentOrder == null) {
                throw new ValidationException("error", "不存在");
            }
            if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
                throw new ValidationException("error", "审核未通过");
            }
            instrumentOrder.setSettlementStatus(Constants.INSTRUMENT_ORDER_SETTLEMENT_STATUS.YES.name());
            instrumentOrder.setSettlementTime(LocalDateTime.now());
            instrumentOrder.setSettlementUser(SecurityUtils.getUserId());
            instrumentOrder.update(SecurityUtils.getUserId());
            instrumentOrderMapper.updateById(instrumentOrder);
        }
    }

    public void cancelSettlement(String ids) {
        for (String id : ids.split(",")) {
            InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(Integer.parseInt(id));
            if (instrumentOrder == null) {
                throw new ValidationException("error", "不存在");
            }
            instrumentOrder.setSettlementStatus(Constants.INSTRUMENT_ORDER_SETTLEMENT_STATUS.NO.name());
            instrumentOrder.setSettlementTime(null);
            instrumentOrder.setSettlementUser(null);
            instrumentOrder.update(SecurityUtils.getUserId());
            instrumentOrderMapper.updateById(instrumentOrder);
        }
    }

    public InstrumentOrderWorkDetailDTO getManagerWork(Integer id) {
        InstrumentOrderWorkDetailDTO detailDTO = new InstrumentOrderWorkDetailDTO();

        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(id);
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "审核未通过");
        }
        Instrument instrument = instrumentMapper.selectById(instrumentOrder.getInstrumentId());
        if (instrument == null || instrument.getDeleted()) {
            throw new ValidationException("error", "仪器不存在");
        }

        List<InstrumentOrderConsumableDTO> orderConsumableList = new ArrayList<>();
        List<ConsumableListDTO> consumableList = getInstrumentConsumable(instrumentOrder.getInstrumentId());
        List<InstrumentOrderConsumable> consumables = orderConsumableMapper.selectList(new LambdaQueryWrapper<InstrumentOrderConsumable>().eq(InstrumentOrderConsumable::getOrderId, instrumentOrder.getId()));
        if (CollectionUtils.isNotEmpty(consumables)) {
            for (InstrumentOrderConsumable consumable : consumables) {
                InstrumentOrderConsumableDTO orderConsumableDTO = new InstrumentOrderConsumableDTO();
                BeanUtils.copyProperties(consumable, orderConsumableDTO);
                orderConsumableList.add(orderConsumableDTO);
            }
        }
        detailDTO.setConsumableList(consumableList);
        detailDTO.setOrderConsumableList(orderConsumableList);
        detailDTO.setId(id);
        detailDTO.setLeaseType(instrumentOrder.getLeaseType());

        InstrumentOrderWork orderWork = orderWorkMapper.selectById(id);
        if (orderWork == null) {
            detailDTO.setLeasePrice(instrument.getLeasePrice());
            detailDTO.setLeasePriceUnit(instrument.getLeasePriceUnit());
            detailDTO.setLeasePriceMin(instrument.getLeasePriceMin());
            detailDTO.setTestPrice(instrument.getTestPrice());
            detailDTO.setTestPriceUnit(instrument.getTestPriceUnit());
            detailDTO.setCalculateMethod(instrument.getCalculateMethod());
        } else {
            BeanUtils.copyProperties(orderWork, detailDTO);
        }

        String orderDateStr = "";
        List<InstrumentOrderDateDTO> orderDateDTOList = new ArrayList<>();
        if ("lease".equals(instrumentOrder.getLeaseType())) {
            List<InstrumentOrderDate> orderDates = orderDateMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, id));
            orderDateStr = getOrderDateStr(orderDates);
            int size = 0;
            for (InstrumentOrderDate orderDate : orderDates) {
                InstrumentOrderDateDTO orderDateDTO = new InstrumentOrderDateDTO();
                BeanUtils.copyProperties(orderDate, orderDateDTO);
                orderDateDTOList.add(orderDateDTO);
                size = size + orderDate.getOrderTime().split(",").length;
            }
            orderDateStr = orderDateStr + "  " + "共计";
            orderDateStr = orderDateStr + new BigDecimal(size).multiply(new BigDecimal(0.5)).setScale(1, RoundingMode.HALF_UP).toString() + "h";
        }
        detailDTO.setOrderDateDTOList(orderDateDTOList);
        detailDTO.setOrderDateStr(orderDateStr);

        detailDTO.setUseSampleNum(instrumentOrder.getUseSampleNum());

        return detailDTO;
    }

    public InstrumentOrderWorkDetailDTO getShowWork(Integer id) {
        InstrumentOrderWorkDetailDTO detailDTO = new InstrumentOrderWorkDetailDTO();
        InstrumentOrderWork orderWork = orderWorkMapper.selectById(id);
        if (orderWork != null) {
            BeanUtils.copyProperties(orderWork, detailDTO);
        }

        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(id);
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }

        List<InstrumentOrderConsumableDTO> orderConsumableList = new ArrayList<>();
        List<ConsumableListDTO> consumableList = getInstrumentConsumable(instrumentOrder.getInstrumentId());
        List<InstrumentOrderConsumable> consumables = orderConsumableMapper.selectList(new LambdaQueryWrapper<InstrumentOrderConsumable>().eq(InstrumentOrderConsumable::getOrderId, id));
        if (CollectionUtils.isNotEmpty(consumables)) {
            for (InstrumentOrderConsumable consumable : consumables) {
                InstrumentOrderConsumableDTO orderConsumableDTO = new InstrumentOrderConsumableDTO();
                BeanUtils.copyProperties(consumable, orderConsumableDTO);
                orderConsumableList.add(orderConsumableDTO);
            }
        }
        detailDTO.setConsumableList(consumableList);
        detailDTO.setOrderConsumableList(orderConsumableList);
        detailDTO.setId(id);
        detailDTO.setLeaseType(instrumentOrder.getLeaseType());

        String orderDateStr = "";
        List<InstrumentOrderDateDTO> orderDateDTOList = new ArrayList<>();
        if ("lease".equals(instrumentOrder.getLeaseType())) {
            List<InstrumentOrderDate> orderDates = orderDateMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, id));
            orderDateStr = getOrderDateStr(orderDates);
            int size = 0;
            for (InstrumentOrderDate orderDate : orderDates) {
                InstrumentOrderDateDTO orderDateDTO = new InstrumentOrderDateDTO();
                BeanUtils.copyProperties(orderDate, orderDateDTO);
                orderDateDTOList.add(orderDateDTO);
                size = size + orderDate.getOrderTime().split(",").length;
            }
            orderDateStr = orderDateStr + "  " + "共计";
            orderDateStr = orderDateStr + new BigDecimal(size).multiply(new BigDecimal(0.5)).setScale(1, RoundingMode.HALF_UP).toString() + "h";
        }
        detailDTO.setOrderDateDTOList(orderDateDTOList);
        detailDTO.setOrderDateStr(orderDateStr);

        detailDTO.setUseSampleNum(instrumentOrder.getUseSampleNum());
        return detailDTO;
    }

    private AttachmentDTO createWorkPdf(InstrumentOrder instrumentOrder, Instrument instrument, InstrumentOrderWork orderWork) throws Exception {
        //设置velocity资源加载器
        Properties prop = new Properties();
        prop.put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        Velocity.init(prop);

        String templateName = "lease.xml";
        if ("test".equals(instrumentOrder.getLeaseType())) {
            templateName = "test.xml";
        }

        //创建Velocity容器
        VelocityContext context = new VelocityContext();
        context.put("enName", nullToBlank(instrumentOrder.getEnName()));
        context.put("contactTel", nullToBlank(instrumentOrder.getContactTel()));
        context.put("contact", nullToBlank(instrumentOrder.getContact()));
        context.put("contactEmail", nullToBlank(instrumentOrder.getContactEmail()));
        context.put("address", nullToBlank(instrumentOrder.getAddress()));
        context.put("instrumentName", nullToBlank(instrument.getName()));
        context.put("sampleName", nullToBlank(instrumentOrder.getSampleName()));
        context.put("useSampleNum", instrumentOrder.getUseSampleNum() == null ? "" : instrumentOrder.getUseSampleNum());
        context.put("samplePackage", nullToBlank(instrumentOrder.getSamplePackage()));
        context.put("sampleSpec", nullToBlank(instrumentOrder.getSampleSpec()));
        context.put("sampleDeposit", nullToBlank(instrumentOrder.getSampleDeposit()));
        context.put("sampleFeature", nullToBlank(instrumentOrder.getSampleFeature()));
        context.put("testProject", nullToBlank(instrumentOrder.getTestProject()));
        context.put("testCondition", nullToBlank(instrumentOrder.getTestCondition()));
        context.put("testMethod", nullToBlank(instrumentOrder.getTestMethod()));
        context.put("orderNum", nullToBlank(instrumentOrder.getOrderNum()));

        String exportOrderTimeStr = "";
        String orderDateStr = "";
        if ("lease".equals(instrumentOrder.getLeaseType())) {
            List<InstrumentOrderDate> orderDates = orderDateMapper.selectList(new LambdaQueryWrapper<InstrumentOrderDate>().eq(InstrumentOrderDate::getOrderId, instrumentOrder.getId()));
            orderDateStr = getOrderDateStr(orderDates);
            exportOrderTimeStr = orderDateStr;
            int size = 0;
            for (InstrumentOrderDate orderDate : orderDates) {
                size = size + orderDate.getOrderTime().split(",").length;
            }
            exportOrderTimeStr = exportOrderTimeStr + "  " + "共计";
            exportOrderTimeStr = exportOrderTimeStr + new BigDecimal(size).multiply(new BigDecimal(0.5)).setScale(1, RoundingMode.HALF_UP).toString() + "h";
            if (size >= 16 && !instrument.getIsAllDay()) {
                exportOrderTimeStr = exportOrderTimeStr + "，";
                int day = size / 16;
                int hour = size % 16;
                exportOrderTimeStr = exportOrderTimeStr + "为" + day + "天";
                if (hour > 0) {
                    exportOrderTimeStr = exportOrderTimeStr + new BigDecimal(hour).multiply(new BigDecimal(0.5)).setScale(1, RoundingMode.HALF_UP).toString() + "h";
                }
            }
        }

        context.put("exportOrderTimeStr", exportOrderTimeStr);
        context.put("remark", orderWork.getRemark());

        String priceStr = "";
        if ("lease".equals(instrumentOrder.getLeaseType())) {
            if (instrument.getLeasePriceStr() != null) {
                priceStr = instrument.getLeasePriceStr();
            }
        } else {
            if (instrument.getTestPriceStr() != null) {
                priceStr = instrument.getTestPriceStr();
            }
        }
        context.put("priceStr", priceStr);
        context.put("amount", orderWork.getAmount() + "元");

        String auditUserName = "";
        String auditYear = "";
        String auditMonth = "";
        String auditDay = "";
        InstrumentOrderAuditRecordDTO recordDTO = auditRecordMapper.selectAuditUser(instrumentOrder.getId());
        if (recordDTO != null) {
            codeDecorator.decorate(recordDTO);
            auditUserName = nullToBlank(recordDTO.getAuditUserName());
            auditYear = recordDTO.getAuditTime().getYear() + "";
            auditMonth = recordDTO.getAuditTime().getMonthValue() + "";
            auditDay = recordDTO.getAuditTime().getDayOfMonth() + "";
        }

        context.put("auditUserName", auditUserName);
        context.put("auditYear", auditYear);
        context.put("auditMonth", auditMonth);
        context.put("auditDay", auditDay);

        //加载模板
        Template tpl = Velocity.getTemplate("static/templates/" + templateName, "UTF-8");

        Writer writer = new StringWriter();
        //合并数据到模板
        tpl.merge(context, writer);
        InputStream in = new ByteArrayInputStream(writer.toString().getBytes(Charset.defaultCharset()));
        //释放资源
        writer.close();

        Document doc = new Document();
        doc.loadFromStream(in, FileFormat.Auto);

        byte[] bytes;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            doc.saveToStream(out, FileFormat.PDF);
            bytes = out.toByteArray();
        }
        // 上传文件
        UploadFile uploadfile = new UploadFile("收据", bytes, "pdf");
        String url = UploadManager.upload(uploadfile, SecurityUtils.getUserName(), ClientGlobal.default_group);
        // 保存并返回生成的模板
        String fileName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        AttachmentDTO attachmentDTO = new AttachmentDTO();
        attachmentDTO.setUrl(ClientGlobal.http_url + ClientGlobal.default_group + "/" + url);
        attachmentDTO.setSize((long) bytes.length);
        attachmentDTO.setName(fileName + "工单.pdf");
        return attachmentDTO;
    }

    private String nullToBlank(String str) {
        return str == null ? "" : str;
    }


    public void invoice(InstrumentOrderInvoiceDTO dto) {
        InstrumentOrder instrumentOrder = instrumentOrderMapper.selectById(dto.getId());
        if (instrumentOrder == null) {
            throw new ValidationException("error", "不存在");
        }
        if (!Constants.INSTRUMENT_ORDER_STATUS.pass.name().equals(instrumentOrder.getStatus())) {
            throw new ValidationException("error", "审核未通过");
        }
        instrumentOrder.setInvoiceFile(dto.getInvoiceFile());
        if ("send".equals(dto.getStatus())) {
            instrumentOrder.setSendInvoiceTime(LocalDateTime.now());
        }
        instrumentOrder.update(SecurityUtils.getUserId());
        instrumentOrderMapper.updateById(instrumentOrder);
    }


}

