package com.fengyun.udf.uaa.web.rest.area;


import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.response.area.AreaDTO;
import com.fengyun.udf.uaa.service.area.AreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "地区-国内省市区", description = "获得省市县的数据信息", tags = "地区")
@RestController
@RequestMapping("/api/states")
public class AreaResource extends BaseResource {

    @Autowired
    private AreaService areaService;

    @ApiOperation(value = "省级行政区划", notes = "得到所有的省级行政区划信息")
    @GetMapping("/provinces")
    public ResponseEntity<List<AreaDTO>> getAllProvinces(@ApiParam("所属国家的id,不传默认为中国id") @RequestParam(name = "worldId", required = false) String worldId) {
        final List<AreaDTO> result = areaService.getAllProvinces(worldId);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }

    @ApiOperation(value = "地级行政区", notes = "根据省级行政区划的SN，得到下面地级行政区的信息")
    @GetMapping("/provinces/{sn}/cities")
    public ResponseEntity<List<AreaDTO>> getCitiesByprovinceId(final @PathVariable("sn") String provinceId) {
        final List<AreaDTO> result = areaService.getCitiessByprovinceId(provinceId);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }

    @ApiOperation(value = "县级行政区", notes = "根据地级行政区的SN，得到下面县级行政区的信息")
    @GetMapping("/provinces/cities/{sn}/counties")
    public ResponseEntity<List<AreaDTO>> getCountiesByCityid(final @PathVariable("sn") String cityId) {
        final List<AreaDTO> result = areaService.getCountiesByCityid(cityId);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), result);
    }
}
