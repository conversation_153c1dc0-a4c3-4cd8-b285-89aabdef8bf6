package com.fengyun.udf.uaa.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;

import java.util.List;

/**
 * Created by wangg on 2019/7/4.
 */
@Data
@Component
@ConfigurationProperties(prefix = "cube")
public class CubeProperties {

    private final CorsConfiguration cors = new CorsConfiguration();

    private String projectCode;

    private int connectTimeout;
    private int networkTimeout;
    private String charset;
    private final CubeProperties.Http http = new CubeProperties.Http();
    private final CubeProperties.Wechat wechat = new CubeProperties.Wechat();

    private List<String> trackerServer;
    private String storageServer;
    private int storagePort;

    private String fileServerPath;
    private String fileGroup;

    public static class Http {
        private int trackerHttpPort;
        private boolean antiStealToken;
        private String secretKey;

        public int getTrackerHttpPort() {
            return trackerHttpPort;
        }

        public void setTrackerHttpPort(int trackerHttpPort) {
            this.trackerHttpPort = trackerHttpPort;
        }

        public boolean isAntiStealToken() {
            return antiStealToken;
        }

        public void setAntiStealToken(boolean antiStealToken) {
            this.antiStealToken = antiStealToken;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }
    }

    public static class Wechat {
        private String appId;
        private String appSecret;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppSecret() {
            return appSecret;
        }

        public void setAppSecret(String appSecret) {
            this.appSecret = appSecret;
        }
    }
}
