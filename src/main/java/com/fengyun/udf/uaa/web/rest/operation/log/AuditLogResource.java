package com.fengyun.udf.uaa.web.rest.operation.log;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.feign.dto.FeignAuditLogDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.response.log.AuditLogDTO;
import com.fengyun.udf.uaa.service.log.AuditLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Created by wangg on 2019/7/15.
 */
@RestController
@RequestMapping("/api")
@Api(value = "访问日志", tags = "访问日志")
@Validated
public class AuditLogResource extends BaseResource {

    @Autowired
    private AuditLogService auditLogService;

    @PostMapping("/log")
    @CustomBaseException
    @ApiOperation(value = "访问日志添加", hidden = true)
    public ResponseEntity<ReturnResultDTO> addLog(@RequestBody FeignAuditLogDTO feignAuditLogDTO) {
        auditLogService.addLog(feignAuditLogDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/log/list")
    @CustomBaseException
    @AuditLog(method = "/api/log/list", methodDescription = "查询访问日志列表", httpMethod = "GET", storageMode = {"file", "mysql"})
    @ApiOperation(value = "查询访问日志列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "methodDescription", value = "标题", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态(正常：1，异常：0)", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数（默认：10）", dataType = "long", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页（默认：1）", dataType = "long", paramType = "query")
    })
    //@PreAuthorize("@pms.hasPermission('logList')")
    public ResponseEntity<ReturnResultDTO<Page<AuditLogDTO>>> searchLogs(Integer status, String methodDescription, Page page) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), auditLogService.findLogs(page, status, methodDescription, null));
    }
}
