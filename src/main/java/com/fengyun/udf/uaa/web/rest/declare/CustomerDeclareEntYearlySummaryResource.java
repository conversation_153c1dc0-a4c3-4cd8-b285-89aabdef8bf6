package com.fengyun.udf.uaa.web.rest.declare;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declare.AddDeclareEntYearlySummaryDTO;
import com.fengyun.udf.uaa.dto.request.declare.QueryDeclareEntYearlySummaryDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryListDTO;
import com.fengyun.udf.uaa.dto.response.declare.DeclareEntYearlySummaryDetailDTO;
import com.fengyun.udf.uaa.service.declare.DeclareEntYearlySummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <p>
 *  Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@RestController
@Slf4j
@SuppressWarnings("all")
@RequiredArgsConstructor
@RequestMapping("/api/customer/declare/yearly-summary")
@Api(value = "用户端-年度总结", tags = "用户端-年度总结")
public class CustomerDeclareEntYearlySummaryResource extends BaseResource {

    @NonNull
    private DeclareEntYearlySummaryService service;

    @PostMapping("/create")
    @AuditLog(method = "/api/manager/declare/create", methodDescription = "添加", httpMethod = "POST")
    @ApiOperation(value = "添加")
    public ResponseEntity<ReturnResultDTO<?>> create(
            @RequestBody @Valid AddDeclareEntYearlySummaryDTO dto) {
        service.save(dto);
        return prepareReturnResult();
    }

    @GetMapping("/detail")
    @AuditLog(method = "/api/customer/declare/detail", methodDescription = "详情", httpMethod = "GET")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "projectId", required = true, dataType = "String", paramType = "query"),
    })
    public ResponseEntity<ReturnResultDTO<DeclareEntYearlySummaryDetailDTO>> detail(
            @NotBlank(message = "项目projectId不能为空") String projectId) {
        return prepareReturnResult(service.detailByProjectIdCustomer(projectId));
    }

}
