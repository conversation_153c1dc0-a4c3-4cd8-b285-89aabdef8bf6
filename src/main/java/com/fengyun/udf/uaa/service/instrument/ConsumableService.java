package com.fengyun.udf.uaa.service.instrument;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.domain.instrument.Consumable;
import com.fengyun.udf.uaa.dto.request.instrument.ConsumableCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.ConsumableQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.ConsumableUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.ConsumableDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.ConsumableListDTO;
import com.fengyun.udf.uaa.mapper.instrument.ConsumableMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.util.UUIDGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 耗材 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class ConsumableService extends BaseService {

    private final ConsumableMapper consumableMapper;

    public void create(ConsumableCreateDTO dto) {
        Consumable consumable = new Consumable();
        BeanUtils.copyProperties(dto, consumable);
        consumable.setId(UUIDGenerator.getUUID());
        consumable.setStatus(Constants.CONSUMABLE_STATUS.YES.name());
        consumable.setDeleted(false);
        consumable.setNumResidue(dto.getNum());
        consumable.create(SecurityUtils.getUserId());

        consumableMapper.insert(consumable);
    }

    public void update(ConsumableUpdateDTO dto) {
        Consumable consumable = consumableMapper.selectById(dto.getId());
        if (consumable == null) {
            throw new ValidationException("error", "不存在");
        }
        if(!dto.getNum().equals(consumable.getNum())){
            consumable.setNumResidue(consumable.getNumResidue() + dto.getNum() - consumable.getNum());
        }
        BeanUtils.copyProperties(dto, consumable);
        consumable.update(SecurityUtils.getUserId());

        consumableMapper.updateById(consumable);
    }

    public ConsumableDetailDTO detail(String id) {
        ConsumableDetailDTO dto = new ConsumableDetailDTO();
        Consumable consumable = consumableMapper.selectById(id);
        if (consumable == null) {
            throw new ValidationException("error", "不存在");
        }
        BeanUtils.copyProperties(consumable, dto);
        return dto;
    }

    public Page<ConsumableListDTO> page(Page page, ConsumableQueryDTO dto) {
        List<ConsumableListDTO> list = consumableMapper.selectPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public void delete(String id) {
        Consumable consumable = consumableMapper.selectById(id);
        if(consumable != null){
            consumable.setDeleted(true);
            consumable.update(SecurityUtils.getUserId());
            consumableMapper.updateById(consumable);
        }
    }

    public void status(String id) {
        Consumable consumable = consumableMapper.selectById(id);
        if (consumable == null) {
            throw new ValidationException("error", "不存在");
        }
        if(Constants.CONSUMABLE_STATUS.YES.name().equals(consumable.getStatus())){
            consumable.setStatus(Constants.CONSUMABLE_STATUS.NO.name());
        }else{
            consumable.setStatus(Constants.CONSUMABLE_STATUS.YES.name());
        }
        consumable.update(SecurityUtils.getUserId());

        consumableMapper.updateById(consumable);
    }


}

