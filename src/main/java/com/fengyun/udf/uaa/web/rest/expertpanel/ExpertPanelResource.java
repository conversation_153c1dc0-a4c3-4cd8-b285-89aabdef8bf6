package com.fengyun.udf.uaa.web.rest.expertpanel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertPanelCreateDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.ExpertPanelSearchDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.QueryRelationProjectDTO;
import com.fengyun.udf.uaa.dto.request.expertpanel.RelationExpertPanelDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.DeclareEntExpertShowDTO;
import com.fengyun.udf.uaa.dto.response.expertpanel.ExpertPanelDTO;
import com.fengyun.udf.uaa.dto.response.system.UserDTO;
import com.fengyun.udf.uaa.service.declareproject.DeclareProjectService;
import com.fengyun.udf.uaa.service.expertpanel.ExpertPanelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/expert/panel")
@Api(value = "专家团管理", tags = "专家团管理")
@Validated
@PreAuthorize("@pms.hasPermission('management')")
public class ExpertPanelResource extends BaseResource {

    @Autowired
    private ExpertPanelService expertPanelService;

    @Autowired
    private DeclareProjectService declareProjectService;

    @GetMapping({"/page"})
    @ApiOperation(value = "专家团列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<ExpertPanelDTO>> page(Page page, ExpertPanelSearchDTO dto) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertPanelService.page(page, dto));
    }

    @PostMapping({"/add"})
    @CustomBaseException
    @ApiOperation(value = "新增专家团")
    public ResponseEntity<ReturnResultDTO> add(@RequestBody ExpertPanelCreateDTO dto) throws Exception {
        expertPanelService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/update"})
    @CustomBaseException
    @ApiOperation(value = "更新专家团")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody ExpertPanelCreateDTO dto) throws Exception {
        expertPanelService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/get/{id}"})
    @ApiOperation(value = "根据id查询专家团")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<ExpertPanelDTO>> get(@PathVariable("id") String id) throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertPanelService.getById(id));
    }

    @PostMapping({"/delete/{id}"})
    @ApiOperation(value = "删除专家团")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<?> delete(@PathVariable String id) throws Exception {
        expertPanelService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping({"/expert/list"})
    @ApiOperation(value = "专家评审人员列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功")})
    public ResponseEntity<ReturnResultDTO<UserDTO>> getExpertList() throws Exception {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertPanelService.getExpertList());
    }

    @GetMapping("/relation/page")
    @CustomBaseException
    @ApiOperation(value = "查询关联列表")
    public ResponseEntity<ReturnResultDTO<DeclareEntExpertShowDTO>> getRelationList(Page page, QueryRelationProjectDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getRelationList(page, dto));
    }

    @GetMapping("/expert/group/list")
    @CustomBaseException
    @ApiOperation(value = "专家团列表")
    public ResponseEntity<ReturnResultDTO<ExpertPanelDTO>> getExpertGroupList() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), expertPanelService.getExpertGroupList());
    }

    @PostMapping({"/relation"})
    @CustomBaseException
    @ApiOperation(value = "关联专家团")
    public ResponseEntity<ReturnResultDTO> relationExpertGroup(@RequestBody RelationExpertPanelDTO dto) throws Exception {
        if(StringUtils.isEmpty(dto.getIndex()) || "1".equals(dto.getIndex())){
            expertPanelService.relationExpertGroup(dto);
        }else if("2".equals(dto.getIndex())){
            expertPanelService.secondRelationExpertGroup(dto);
        }

        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping({"/cancel/relation"})
    @CustomBaseException
    @ApiOperation(value = "取消关联专家团")
    public ResponseEntity<ReturnResultDTO> cancelRelationExpertGroup(String id ,String index) throws Exception {
        if(StringUtils.isEmpty(index) || "1".equals(index)){
            expertPanelService.cancelRelationExpertGroup(id);
        } else if("2".equals(index)){
            expertPanelService.cancelSecondRelationExpertGroup(id);
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

//    @PostMapping({"/second/relation"})
//    @CustomBaseException
//    @ApiOperation(value = "关联专家团")
//    public ResponseEntity<ReturnResultDTO> secondRelationExpertGroup(@RequestBody RelationExpertPanelDTO dto) throws Exception {
//        expertPanelService.secondRelationExpertGroup(dto);
//        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
//    }

//    @PostMapping({"/second/cancel/relation"})
//    @CustomBaseException
//    @ApiOperation(value = "取消关联专家团")
//    public ResponseEntity<ReturnResultDTO> cancelSecondRelationExpertGroup(String id) throws Exception {
//        expertPanelService.cancelSecondRelationExpertGroup(id);
//        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
//    }
//    @GetMapping("/relation/page2")
//    @CustomBaseException
//    @ApiOperation(value = "查询二次关联列表")
//    public ResponseEntity<ReturnResultDTO<DeclareEntExpertShowDTO>> getRelation2List(Page page, QueryRelationProjectDTO dto) {
//        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), declareProjectService.getRelationList2(page, dto));
//    }

}
