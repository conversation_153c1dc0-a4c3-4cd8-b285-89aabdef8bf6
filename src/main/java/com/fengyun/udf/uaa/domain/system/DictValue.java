package com.fengyun.udf.uaa.domain.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

@Data
@TableName("t_code_value")
public class DictValue extends BaseDomain {
    @TableId("ID")
    private String id;

    @TableField("TYPE_CODE")
    private String typeCode;

    @TableField("VALUE")
    private String value;

    @TableField("LABEL")
    private String label;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("SEQ")
    private Integer dictSort;

    @TableField("STATUS")
    private String status;

    @TableField("IS_Vis")
    private Integer isVis;

}
