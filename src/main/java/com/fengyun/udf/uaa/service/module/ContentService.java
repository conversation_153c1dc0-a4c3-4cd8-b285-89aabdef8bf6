package com.fengyun.udf.uaa.service.module;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.domain.module.Content;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.module.QueryContentDto;
import com.fengyun.udf.uaa.dto.response.module.ContentDetailDto;
import com.fengyun.udf.uaa.dto.response.module.ContentListDto;
import com.fengyun.udf.uaa.dto.response.module.SaveContentDto;
import com.fengyun.udf.uaa.dto.response.system.DictValueDTO;
import com.fengyun.udf.uaa.mapper.module.ContentMapper;
import com.fengyun.udf.uaa.mapper.system.DictValueMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.util.BeanUtils;
import com.fengyun.udf.util.UUIDGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/17 15:32
 * @Version 1.0
 */

@Service
public class ContentService extends BaseService {

    @Autowired
    private ContentMapper contentMapper;

    @Autowired
    private DictValueMapper valueMapper;

    public void save(SaveContentDto dto) {

        boolean create = false;
        Content content = contentMapper.selectById(dto.getId());
        if (content == null) {
            create = true;
        }
        if (create) {
            List<Content> contents = contentMapper.selectList(new LambdaQueryWrapper<Content>()
                    .eq(Content::getName, dto.getName()));
            if (contents.size() > 0) {
                throw new ValidationException("error", "该名称已存在");
            }
            content = new Content();
            if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
                content.setAttachments(JSON.toJSONString(dto.getAttachments()));
            }
            BeanUtils.copyProperties(dto, content);
            content.setCover(toJSONString(dto.getCover()));
            content.setId(UUIDGenerator.getUUID());
            content.create(SecurityUtils.getUserId());
            contentMapper.insert(content);
        } else {
            if (CollectionUtils.isNotEmpty(dto.getAttachments())) {
                content.setAttachments(JSON.toJSONString(dto.getAttachments()));
            }
            BeanUtils.copyPropertiesExcludeNull(dto, content);
            content.setCover(toJSONString(dto.getCover()));
            content.update(SecurityUtils.getUserId());
            contentMapper.updateById(content);
        }
    }

    public ContentDetailDto detail(String id) {
        Content content = contentMapper.selectById(id);
        if (content == null) {
            throw new ValidationException("error", "该条内容不存在,请刷新列表");
        }
        ContentDetailDto result = new ContentDetailDto();
        BeanUtils.copyProperties(content, result);
        result.setCover(parseObject(content.getCover(), AttachmentDTO.class));
        if (StringUtils.isNotEmpty(content.getAttachments())) {
            result.setAttachments(JSON.parseArray(content.getAttachments(), AttachmentDTO.class));
        }
        return result;
    }

    public void delete(String id) {
        contentMapper.deleteById(id);
    }

    public Page<ContentListDto> page(Page page, QueryContentDto dto) {
        IPage<Content> contents = contentMapper.selectPage(page, new LambdaQueryWrapper<Content>()
                .select(Content::getId, Content::getName, Content::getOwningModule,
                        Content::getSubDate, Content::getCover, Content::getIntroduction)
                .like(notNull(dto.getName()), Content::getName, dto.getName())
                .eq(notNull(dto.getOwningModule()), Content::getOwningModule, dto.getOwningModule())
                .orderByDesc(Content::getSubDate));
        List<ContentListDto> result = new ArrayList<>();
        contents.getRecords().forEach(item -> {
            result.add(new ContentListDto(item.getId(), item.getName(), item.getOwningModule(),
                    parseObject(item.getCover(), AttachmentDTO.class), item.getIntroduction(), item.getSubDate()));
        });
        page.setRecords(result);
        return page;
    }

    public Page<ContentListDto> contentPage(Page page, String keyWord) {
        String owningModuleIds = "";
        List<DictValueDTO> dictValueDTOS = valueMapper.selectByTypeCode("HOME_OWNING_MOUDLE_IDS");
        if (CollectionUtils.isNotEmpty(dictValueDTOS)) {
            owningModuleIds = dictValueDTOS.get(0).getDescription();
        }
        if (StringUtils.isNotBlank(owningModuleIds)) {
            List<String> ids = splitParam(owningModuleIds);
            List<ContentListDto> result = contentMapper.selectContentByOwningIds(page, keyWord, ids);
            page.setRecords(result);
        }
        return page;
    }

}
