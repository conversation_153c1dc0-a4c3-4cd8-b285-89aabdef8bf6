package com.fengyun.udf.uaa.web.rest.instrument;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmCreateDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmQueryDTO;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmUpdateDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmDetailDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmListDTO;
import com.fengyun.udf.uaa.service.instrument.InstrumentOrderDetailConfirmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */

@RestController
@RequestMapping("/api/manager/instrument-order-detail-confirm")
@Api(value = "管理端-仪器预约明细确认", tags = "管理端-仪器预约明细确认")
@Validated
@Slf4j
@AllArgsConstructor
@PreAuthorize("@pms.hasPermission('management')")
public class ManagerInstrumentOrderDetailConfirmResource extends BaseResource {
    private final InstrumentOrderDetailConfirmService instrumentOrderDetailConfirmService;

    @PostMapping("/new")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm/new", methodDescription = "添加仪器预约明细确认", httpMethod = "POST")
    @ApiOperation(value = "添加仪器预约明细确认")
    public ResponseEntity<ReturnResultDTO> create(@RequestBody @Validated InstrumentOrderDetailConfirmCreateDTO dto) {
        instrumentOrderDetailConfirmService.create(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/edit")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm/edit", methodDescription = "修改仪器预约明细确认", httpMethod = "POST")
    @ApiOperation(value = "修改仪器预约明细确认")
    public ResponseEntity<ReturnResultDTO> update(@RequestBody @Validated InstrumentOrderDetailConfirmUpdateDTO dto) {
        instrumentOrderDetailConfirmService.update(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/view")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm/view", methodDescription = "仪器预约明细确认详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约明细确认ID", required = true, dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "仪器预约明细确认详情")
    public ResponseEntity<ReturnResultDTO<InstrumentOrderDetailConfirmDetailDTO>> detail(@NotNull(message = "id不能为空") Integer id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderDetailConfirmService.detail(id));
    }

    @GetMapping("")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm", methodDescription = "仪器预约明细确认列表", httpMethod = "GET")
    @ApiOperation(value = "仪器预约明细确认列表")
    public ResponseEntity<ReturnResultDTO<Page<InstrumentOrderDetailConfirmListDTO>>> page(Page page, InstrumentOrderDetailConfirmQueryDTO dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), instrumentOrderDetailConfirmService.page(page, dto));
    }

    @GetMapping("/delete")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm/delete", methodDescription = "仪器预约明细确认删除", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仪器预约明细确认ID", required = true, dataType = "Integer", paramType = "query")
    })
    @ApiOperation(value = "仪器预约明细确认删除")
    public ResponseEntity<ReturnResultDTO> delete(@NotNull(message = "id不能为空") Integer id) {
        instrumentOrderDetailConfirmService.delete(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/resend")
    @CustomBaseException
    @AuditLog(method = "/api/manager/instrument-order-detail-confirm/resend", methodDescription = "重新发送", httpMethod = "POST")
    @ApiOperation(value = "重新发送")
    public ResponseEntity<ReturnResultDTO> create(@NotBlank(message = "ids不能为空") String ids,@RequestParam(required = false) String type) {
        instrumentOrderDetailConfirmService.resend(ids,type);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }


}
