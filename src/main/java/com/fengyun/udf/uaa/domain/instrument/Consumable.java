package com.fengyun.udf.uaa.domain.instrument;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 耗材
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
@TableName("in_consumable")
public class Consumable extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 耗材名称
     */
    @TableField("name")
    private String name;

    /**
     * 耗材数量(个）
     */
    @TableField("num")
    private Integer num;

    /**
     * 耗材剩余数量（个）
     */
    @TableField("num_residue")
    private Integer numResidue;

    /**
     * 单价
     */
    @TableField("unit_price")
    private Double unitPrice;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    private Boolean deleted;


}
