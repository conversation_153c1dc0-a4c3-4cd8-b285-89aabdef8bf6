package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * Created by Administrator on 2021/3/11.
 */
@TableName("t_declare_project")
@Data
public class DeclareProject extends BaseDomain {

    private String id;

    @TableField("flow_id")
    private String flowId;

    @TableField("declare_name")
    private String declareName;

    @TableField("declare_begin_time")
    private String declareBeginTime;

    @TableField("declare_end_time")
    private String declareEndTime;

    @TableField("content")
    private String content;

    @TableField("file_content")
    private String fileContent;

    @TableField("attachments")
    private String attachments;

    @TableField("status")
    private String status;

    @TableField("top")
    private String top;

    @TableField("submit_status")
    private String submitStatus;

    @TableField("public_flag")
    private String publicFlag;

    @TableField("expert_score_template_id")
    private String expertScoreTemplateId;

    /**
     * 评审模板
     */
    @TableField("review_template")
    private String reviewTemplate;

    @TableField("project_status")
    private String projectStatus;

    @TableField("project_no_prefix")
    private String prefix;

    @TableField("second_expert_score_template_id")
    private String secondExpertScoreTemplateId;

    @TableField("declare_no")
    private String declareNo;

    /**
     * 是否下发年度总结
     */
    @TableField("yearly_summary")
    private Boolean yearlySummary;

}
