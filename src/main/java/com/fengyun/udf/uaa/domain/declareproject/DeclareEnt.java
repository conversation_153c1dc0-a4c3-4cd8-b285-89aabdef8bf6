package com.fengyun.udf.uaa.domain.declareproject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Created by Administrator on 2021/3/11.
 */
@TableName("t_declare_ent")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DeclareEnt extends BaseDomain {


    private String id;

    @TableField("declare_id")
    private String declareId;

    @TableField("entprise_name")
    private String entpriseName;

    @TableField("credit_code")
    private String creditCode;

    @TableField("pre_role")
    private String preRole;

    @TableField("current_role")
    private String currentRole;

    @TableField("status")
    private String status;

    @TableField("ent_status")
    private String entStatus;

    @TableField("submit_status")
    private String submitStatus;

    @TableField("declare_type")
    private String declareType;

    @TableField("flow")
    private String flow;

    @TableField("expert_panel_id")
    private String expertPanelId;

    @TableField("project_no")
    private String projectNo;

    @TableField("project_status")
    private String projectStatus;

    @TableField("close_debate_ppt")
    private String closeDebatePPT;

    @TableField("close_debate_video")
    private String closeDebateVideo;

    @TableField("second_expert_panel_id")
    private String secondExpertPanelId;

    /**
     *  项目进度
     */
    @TableField(value = "project_progress")
    private String projectProgress;
}
