package com.fengyun.udf.uaa.web.rest.flow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.flowform.QueryShenbaoDto;
import com.fengyun.udf.uaa.dto.response.flowform.ShenbaoDetailDto;
import com.fengyun.udf.uaa.service.flowform.ShenBaoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "项目申报-管理-简版", tags = "项目申报-管理-简版")
@RestController
@RequestMapping({"/api/shenbao/manage"})
@Validated
@PreAuthorize("@pms.hasPermission('management')")
public class ManageShenbaoResource extends BaseResource {

    @Autowired
    private ShenBaoService shenBaoService;

    @GetMapping("/page")
    @CustomBaseException
    @AuditLog(method = "/api/shenbao/manage/page", methodDescription = "列表", httpMethod = "GET")
    @ApiOperation(value = "列表")
    public ResponseEntity<ReturnResultDTO<Page<ShenbaoDetailDto>>> page(Page page, QueryShenbaoDto dto) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), shenBaoService.page(page, dto));
    }

    @GetMapping("/detail/{id}")
    @CustomBaseException
    @AuditLog(method = "/api/shenbao/manage/detail/{id}", methodDescription = "详情", httpMethod = "GET")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String", paramType = "path")
    })
    public ResponseEntity<ReturnResultDTO<Page<ShenbaoDetailDto>>> detail(@PathVariable String id) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), shenBaoService.ShenbaoDetail(id));
    }

}
