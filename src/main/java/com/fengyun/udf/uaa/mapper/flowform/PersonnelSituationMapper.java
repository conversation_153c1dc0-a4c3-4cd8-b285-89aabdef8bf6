package com.fengyun.udf.uaa.mapper.flowform;

import com.fengyun.udf.uaa.domain.flowform.PersonnelSituation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目人员表单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@Repository
public interface PersonnelSituationMapper extends BaseMapper<PersonnelSituation> {

        List<Map<String, String>> selectPersonList(@Param("projectId") String projectId);

}
