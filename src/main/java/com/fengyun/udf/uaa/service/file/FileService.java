package com.fengyun.udf.uaa.service.file;

import com.alibaba.fastjson.JSON;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.fastdfs.ClientGlobal;
import com.fengyun.udf.uaa.fastdfs.client.UploadManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 2024/7/25
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class FileService {

    /**
     * 异步删除文件
     * @param attachmentStr AttachmentDTO Str
     */
    @Async
    public void asyncDeleteFile(String attachmentStr) {
        if(StringUtils.isNotBlank(attachmentStr)) {
            AttachmentDTO attachmentDTO = JSON.parseObject(attachmentStr, AttachmentDTO.class);
            String remoteFileName = attachmentDTO.getUrl();
            remoteFileName = remoteFileName.substring(
                    remoteFileName.lastIndexOf(ClientGlobal.default_group) + ClientGlobal.default_group.length() + 1);
            try {
                log.info("异步删除文件: {}", remoteFileName);
                UploadManager.deleteFile(ClientGlobal.default_group, remoteFileName);
            } catch (Exception ignored) {}
        }
    }
}
