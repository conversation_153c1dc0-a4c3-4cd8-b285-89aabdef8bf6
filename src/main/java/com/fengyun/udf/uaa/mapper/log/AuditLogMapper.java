package com.fengyun.udf.uaa.mapper.log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.uaa.domain.log.AuditLog;
import com.fengyun.udf.uaa.dto.request.statistics.QueryStatisticsOverallDTO;
import com.fengyun.udf.uaa.dto.response.log.AuditLogDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by wangg on 2019/7/15.
 */
public interface AuditLogMapper extends BaseMapper<AuditLog> {

    List<AuditLogDTO> selectLogs(Page<AuditLogDTO> page,
                                 @Param("status") final Integer status,
                                 @Param("methodDescription") final String methodDescription,
                                 @Param("tenantId") final String tenantId);

    List<Map<String,Object>> selectLoginStatistics(QueryStatisticsOverallDTO dto);
}
