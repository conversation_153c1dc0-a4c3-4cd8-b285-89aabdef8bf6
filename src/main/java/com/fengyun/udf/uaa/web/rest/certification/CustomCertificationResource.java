package com.fengyun.udf.uaa.web.rest.certification;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.certification.CreateCertificationDto;
import com.fengyun.udf.uaa.dto.request.certification.EnUserAddInfoDto;
import com.fengyun.udf.uaa.service.certification.CertificationInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/1/25 15:00
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/certification")
@Api(value = "用户企业信息完善", tags = "用户企业信息完善")
@Validated
public class CustomCertificationResource extends BaseResource {

    @Autowired
    private CertificationInfoService certificationInfoService;

    @PostMapping("/save")
    @CustomBaseException
    @AuditLog(method = "/api/certification/save", methodDescription = "保存/编辑用户企业信息", httpMethod = "POST")
    @ApiOperation(value = "保存/编辑用户企业信息")
    public ResponseEntity<ReturnResultDTO> save(@RequestBody CreateCertificationDto dto) {
        certificationInfoService.save(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @PostMapping("/addInfo")
    @CustomBaseException
    @AuditLog(method = "/api/certification/addInfo", methodDescription = "补全企业用户信息", httpMethod = "POST")
    @ApiOperation(value = "补全企业用户信息")
    public ResponseEntity<ReturnResultDTO> addInfo(@RequestBody EnUserAddInfoDto dto) {
        certificationInfoService.addInfo(dto);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
}
