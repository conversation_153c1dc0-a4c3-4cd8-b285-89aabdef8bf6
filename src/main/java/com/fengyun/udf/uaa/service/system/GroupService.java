package com.fengyun.udf.uaa.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.uaa.constant.Tables;
import com.fengyun.udf.uaa.domain.system.Group;
import com.fengyun.udf.uaa.dto.request.system.CreateGroupDTO;
import com.fengyun.udf.uaa.dto.request.system.UpdateGroupDTO;
import com.fengyun.udf.uaa.dto.response.system.GroupDTO;
import com.fengyun.udf.uaa.dto.response.system.GroupTreeDTO;
import com.fengyun.udf.uaa.mapper.system.GroupMapper;
import com.fengyun.udf.util.TreeBuilder;
import com.fengyun.udf.util.UUIDGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by wangg on 2019/7/5.
 */
@Service
@Transactional
public class GroupService {
    @Autowired
    private GroupMapper groupMapper;

    /**
     * 验证父级节点是否存在
     *
     * @param groupId 部门主键ID
     * @return boolean
     */
    public boolean groupIsExisting(String groupId) {
        long count = groupMapper.selectCount(new QueryWrapper<Group>().eq(Tables.T_GROUP.ID.name(), groupId));
        return count == 1;
    }

    /**
     * 验证父级节点是否存在
     *
     * @param groupIds 部门主键ID
     * @return boolean
     */
    public boolean groupIsExisting(List<String> groupIds) {
        long count = groupMapper.selectCount(new QueryWrapper<Group>().in(Tables.T_GROUP.ID.name(), groupIds));
        return count == groupIds.size();
    }


    /**
     * 添加部门
     *
     * @param createGroupDTO 添加部门对象参数
     */
    public void addGroup(CreateGroupDTO createGroupDTO) {
        Group group = new Group();
        group.setId(UUIDGenerator.getUUID());
        group.setParentId(createGroupDTO.getParentId());
        group.setGroupName(createGroupDTO.getGroupName());
        group.setGroupDesc(createGroupDTO.getGroupDesc());
        group.setGroupSort(createGroupDTO.getGroupSort());
        group.create(SecurityUtils.getUserId());
        groupMapper.insert(group);
    }


    /**
     * 查询部门列表
     *
     * @param
     */
    public List<GroupTreeDTO> searchGroup() {
        List<GroupTreeDTO> treeList = groupMapper.selectListOrderBySeq();
        treeList = (List<GroupTreeDTO>) TreeBuilder.buildListToTree(treeList);
        return treeList;
    }

    /**
     * 获得部门详情
     *
     * @param id
     */
    public GroupDTO getGroup(String id) {
        Group group = groupMapper.selectById(id);
        GroupDTO groupDTO = new GroupDTO();
        BeanUtils.copyProperties(group, groupDTO);
        return groupDTO;
    }

    /**
     * 更新部门
     *
     * @param updateGroupDTO
     */
    public void updateGroup(UpdateGroupDTO updateGroupDTO) {
        Group group = groupMapper.selectById(updateGroupDTO.getId());
        group.setParentId(updateGroupDTO.getParentId());
        group.setGroupName(updateGroupDTO.getGroupName());
        group.setGroupDesc(updateGroupDTO.getGroupDesc());
        group.setGroupSort(updateGroupDTO.getGroupSort());
        group.update(SecurityUtils.getUserId());
        groupMapper.updateById(group);
    }

    /**
     * 删除部门
     *
     * @param id
     */
    public void deleteGroup(String id) {
        groupMapper.deleteById(id);

        List<Group> children = groupMapper.selectList(new QueryWrapper<Group>().eq(Tables.T_GROUP.PARENT_ID.name(), id));
        if(children != null && children.size() > 0){
            for (Group child : children) {
                deleteGroup(child.getId());
            }
        }
    }
}
