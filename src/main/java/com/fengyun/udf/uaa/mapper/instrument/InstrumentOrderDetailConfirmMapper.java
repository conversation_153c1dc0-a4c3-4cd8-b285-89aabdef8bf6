package com.fengyun.udf.uaa.mapper.instrument;

import com.fengyun.udf.uaa.domain.instrument.InstrumentOrderDetailConfirm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fengyun.udf.uaa.dto.request.instrument.InstrumentOrderDetailConfirmQueryDTO;
import com.fengyun.udf.uaa.dto.response.instrument.InstrumentOrderDetailConfirmListDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 仪器预约明细确认 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
public interface InstrumentOrderDetailConfirmMapper extends BaseMapper<InstrumentOrderDetailConfirm> {

    List<InstrumentOrderDetailConfirmListDTO> selectPage(Page page, @Param("condition")InstrumentOrderDetailConfirmQueryDTO dto);

}
