package com.fengyun.udf.uaa.web.rest.inspection.end;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.inspection.end.*;
import com.fengyun.udf.uaa.dto.response.inspection.end.*;
import com.fengyun.udf.uaa.service.inspection.end.DeclareEntEndInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 2024/7/22
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Validated
@RestController
@SuppressWarnings("all")
@RequestMapping("/api/customer/declare/inspection/end")
@Api(value = "项目申报-结题验收-企业端", tags = "项目申报-结题验收-企业端")
public class DeclareEntEndInspectionController extends BaseResource {

    @Autowired
    private DeclareEntEndInspectionService middleInspectionService;

    @PostMapping("/basic/save")
    @AuditLog(
            method = "/api/declare/inspection/middle/basic/save",
            methodDescription = "保存单位基本信息",
            httpMethod = "POST")
    @ApiOperation(value = "保存单位基本信息")
    public ResponseEntity<ReturnResultDTO<?>> saveUnitBasicInfo(
            @Valid @RequestBody EndUnitBasicInfoSaveDTO dto) {
        middleInspectionService.saveUnitBasicInfo(dto);
        return prepareReturnResult();
    }

    @GetMapping("/basic/detail")
    @AuditLog(
            method = "/api/declare/inspection/middle/basic/detail",
            methodDescription = "获取单位基本信息",
            httpMethod = "GET")
    @ApiOperation(value = "获取单位基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<EndUnitBasicInfoDetailDTO>> getUnitBasicInfoDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getUnitBasicInfoDetail(projectId)
        );
    }

    @PostMapping("/completion/save")
    @AuditLog(
            method = "/api/declare/inspection/middle/completion/save",
            methodDescription = "保存项目完成情况",
            httpMethod = "POST")
    @ApiOperation(value = "保存项目完成情况")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectCompletion(
            @Valid @RequestBody ProjectEndCompletionSaveDTO dto) {
        middleInspectionService.saveProjectCompletion(dto);
        return prepareReturnResult();
    }

    @GetMapping("/completion/detail")
    @AuditLog(
            method = "/api/declare/inspection/middle/completion/detail",
            methodDescription = "获取项目完成情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目完成情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectEndCompletionDetailDTO>> getProjectCompletionDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectCompletionDetail(projectId)
        );
    }

    @PostMapping("/achievement/save")
    @AuditLog(
            method = "/api/declare/inspection/middle/achievement/save",
            methodDescription = "保存阶段性成果",
            httpMethod = "POST")
    @ApiOperation(value = "保存阶段性成果")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectAchievement(
            @Valid @RequestBody EndStageAchievementSaveDTO dto) {
        middleInspectionService.saveProjectAchievement(dto);
        return prepareReturnResult();
    }

    @GetMapping("/achievement/detail")
    @AuditLog(
            method = "/api/declare/inspection/middle/achievement/detail",
            methodDescription = "获取阶段性成果",
            httpMethod = "GET")
    @ApiOperation(value = "获取阶段性成果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<EndStageAchievementDetailDTO>> getStageAchievementDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getStageAchievementDetail(projectId)
        );
    }

    @PostMapping("/participant/save")
    @AuditLog(
            method = "/api/declare/inspection/middle/participant/save",
            methodDescription = "保存项目人员情况",
            httpMethod = "POST")
    @ApiOperation(value = "保存项目人员情况")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectParticipant(
            @Valid @RequestBody ProjectEndParticipantSaveDTO dto) {
        middleInspectionService.saveProjectParticipant(dto);
        return prepareReturnResult();
    }

    @GetMapping("/participant/detail")
    @AuditLog(
            method = "/api/declare/inspection/middle/participant/detail",
            methodDescription = "获取项目人员情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目人员情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectEndParticipantDetailDTO>> getProjectParticipantDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectParticipantDetail(projectId)
        );
    }

    @PostMapping("/funding/save")
    @AuditLog(
            method = "/api/declare/inspection/middle/funding/save",
            methodDescription = "保存项目经费到位与使用情况",
            httpMethod = "POST")
    @ApiOperation(value = "保存项目经费到位与使用情况")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectFunding(
            @Valid @RequestBody ProjectEndFundingSaveDTO dto) {
        middleInspectionService.saveProjectFunding(dto);
        return prepareReturnResult();
    }

    @GetMapping("/funding/detail")
    @AuditLog(
            method = "/api/declare/inspection/middle/funding/detail",
            methodDescription = "获取项目经费到位与使用情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目经费到位与使用情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectEndFundingDetailDTO>> getProjectFundingDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectFundingDetail(projectId)
        );
    }

    @PostMapping("/attachment/save")
    @AuditLog(
            method = "/api/declare/inspection/middle/attachment/save",
            methodDescription = "保存附件",
            httpMethod = "POST")
    @ApiOperation(value = "保存附件")
    public ResponseEntity<ReturnResultDTO<?>> saveProjectAttachment(
            @Valid @RequestBody DeclareEntEndAttachmentSaveDTO dto) {
        middleInspectionService.saveProjectAttachment(dto);
        return prepareReturnResult();
    }

    @GetMapping("/attachment/detail")
    @AuditLog(
            method = "/api/declare/inspection/middle/attachment/detail",
            methodDescription = "获取附件",
            httpMethod = "GET")
    @ApiOperation(value = "获取附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<DeclareEntEndAttachmentDetailDTO>> getAttachmentDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getAttachmentDetail(projectId)
        );
    }

    @GetMapping("/submit")
    @AuditLog(
            method = "/api/declare/inspection/middle/submit",
            methodDescription = "提交结题验收",
            httpMethod = "GET")
    @ApiOperation(value = "提交结题验收")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<?>> submitEndInspection(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        middleInspectionService.submitEndInspection(projectId);
        return prepareReturnResult();
    }

}
