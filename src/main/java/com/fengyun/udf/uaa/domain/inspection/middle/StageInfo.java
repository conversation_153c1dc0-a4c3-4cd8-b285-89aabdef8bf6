package com.fengyun.udf.uaa.domain.inspection.middle;

import com.alibaba.fastjson.TypeReference;
import com.fengyun.udf.uaa.handler.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "阶段信息")
public class StageInfo {

    /**
     * 阶段进度安排
     */
    @ApiModelProperty("阶段进度安排")
    private String schedule;

    /**
     * 阶段完成情况
     */
    @ApiModelProperty("阶段完成情况")
    private String completion;

    public static class StageInfoListTypeHandler extends ListTypeHandler<StageInfo> {

        @Override
        protected TypeReference<List<StageInfo>> specificType() {
            return new TypeReference<List<StageInfo>>() {
            };
        }

    }
} 
