package com.fengyun.udf.uaa.dto.response.inspection.middle;

import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.uaa.dto.response.declareproject.RuNodeTaskHistoryShowDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UnitBasicInfoDetailDTO", description = "UnitBasicInfoDetailDTO")
public class UnitBasicInfoDetailDTO extends AbstractDTO {

    @ApiModelProperty(value = "基本信息")
    private DeclareEntMiddleBasicInfoDetailDTO basicInfo;

    @ApiModelProperty(value = "承担单位基本信息")
    private DeclareEntMiddleResponsibleUnitDetailDTO responsibleUnit;

    @ApiModelProperty(value = "苏州基本公司信息")
    private DeclareEntMiddleSuzhouUnitDetailDTO suzhouUnit;

    @ApiModelProperty(value = "审核记录")
    private List<RuNodeTaskHistoryShowDTO> auditHistory;

}
