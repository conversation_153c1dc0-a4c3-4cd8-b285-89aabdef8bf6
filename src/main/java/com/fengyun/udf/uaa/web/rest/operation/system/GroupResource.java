package com.fengyun.udf.uaa.web.rest.operation.system;

import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.aspect.annotation.CustomBaseException;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.system.CreateGroupDTO;
import com.fengyun.udf.uaa.dto.request.system.UpdateGroupDTO;
import com.fengyun.udf.uaa.dto.response.system.GroupDTO;
import com.fengyun.udf.uaa.dto.response.system.GroupTreeDTO;
import com.fengyun.udf.uaa.service.system.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "运营部门管理", tags = "运营平台-系统管理")
@Validated
public class GroupResource extends BaseResource {
    @Autowired
    private GroupService groupService;

    @PostMapping("/group")
    @CustomBaseException
    @AuditLog(method = "/api/group", methodDescription = "新增部门", httpMethod = "POST")
    @ApiOperation(value = "新增部门")
    //@PreAuthorize("@pms.hasPermission('groupAdd')")
    public ResponseEntity<ReturnResultDTO> addGroup(@RequestBody @Validated CreateGroupDTO createGroupDTO){
        if(StringUtils.isNotBlank(createGroupDTO.getParentId())
               && !groupService.groupIsExisting(createGroupDTO.getParentId())){
            throw new ValidationException("parentId", "error.groupId.notExisting");
        }
        groupService.addGroup(createGroupDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/group/list")
    @CustomBaseException
    @AuditLog(method = "/api/group/list", methodDescription = "查询部门列表", httpMethod = "GET")
    @ApiOperation(value = "查询部门列表")
    //@PreAuthorize("@pms.hasPermission('groupList')")
    public ResponseEntity<ReturnResultDTO<List<GroupTreeDTO>>> searchGroup() {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), groupService.searchGroup());
    }

    @GetMapping("/group")
    @CustomBaseException
    @AuditLog(method = "/api/group", methodDescription = "查询部门详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "查询部门详情")
    //@PreAuthorize("@pms.hasPermission('groupDetail')")
    public ResponseEntity<ReturnResultDTO<GroupDTO>> getTenantMenu(@NotBlank(message = "{error.groupId.blank}") String id) {
        if(!groupService.groupIsExisting(id)){
            throw new ValidationException("groupId", "error.groupId.notExisting");
        }
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), groupService.getGroup(id));
    }

    @PostMapping("/group/update")
    @CustomBaseException
    @AuditLog(method = "/api/group/update", methodDescription = "编辑部门", httpMethod = "POST")
    @ApiOperation(value = "编辑部门")
    //@PreAuthorize("@pms.hasPermission('groupUpdate')")
    public ResponseEntity<ReturnResultDTO> updateGroup(@RequestBody @Validated UpdateGroupDTO updateGroupDTO) {
        if( !groupService.groupIsExisting(updateGroupDTO.getId())){
            throw new ValidationException("groupId", "error.groupId.notExisting");
        }
        if(StringUtils.isNotBlank(updateGroupDTO.getParentId())
                && !groupService.groupIsExisting(updateGroupDTO.getParentId())){
            throw new ValidationException("parentId", "error.groupId.notExisting");
        }
        groupService.updateGroup(updateGroupDTO);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }

    @GetMapping("/group/delete")
    @CustomBaseException
    @AuditLog(method = "/api/group/delete", methodDescription = "删除部门", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "删除部门")
    //@PreAuthorize("@pms.hasPermission('groupDelete')")
    public ResponseEntity<ReturnResultDTO> deleteGroup(@NotBlank(message = "{error.groupId.blank}") String id) {
        if( !groupService.groupIsExisting(id)){
            throw new ValidationException("groupId", "error.groupId.notExisting");
        }
        groupService.deleteGroup(id);
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(), null);
    }
}
