package com.fengyun.udf.uaa.config;

import com.fengyun.udf.uaa.service.area.AreaService;
import com.fengyun.udf.uaa.service.area.AreaWorldService;
import com.fengyun.udf.uaa.service.holiday.HolidayService;
import com.fengyun.udf.uaa.service.module.ModuleListService;
import com.fengyun.udf.uaa.service.system.DictService;
import com.fengyun.udf.uaa.service.system.PlatformService;
import com.fengyun.udf.uaa.service.system.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DataCacheInitComponent implements CommandLineRunner {// CommandLineRunner表示在所有的bean以及applicationContenxt完成后的操作

    @Autowired
    private AreaService areaService;

    @Autowired
    private AreaWorldService worldService;

    @Autowired
    private DictService dictService;

    @Autowired
    private UserService userService;

    @Autowired
    private ModuleListService moduleListService;

    @Autowired
    private PlatformService platformService;

    @Autowired
    private HolidayService holidayService;

    @Value("${cache.isInit}")
    private boolean isInit;

    @Override
    public void run(String... args) {
        if (isInit) {
            log.info("初始化World数据到缓存");
            worldService.initCacheData();

            log.info("初始化地区数据到缓存");
            areaService.initCacheData();
            areaService.initNameCacheData();

            log.info("初始化字典数据到缓存");
            dictService.refreshDictCache();

            log.info("初始化用户数据到缓存");
            userService.initNicknameCache();

            log.info("初始化平台信息数据到缓存");
            platformService.getPlatformInfo(true);

            log.info("初始化节假日");
            holidayService.init();
        }
        moduleListService.initModuleNameCache();
    }
}
