package com.fengyun.udf.uaa.domain.inspection.middle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "declare_ent_middle_completion")
@TableName(value = "declare_ent_middle_completion", autoResultMap = true)
public class DeclareEntMiddleCompletion extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField("project_id")
    private String projectId;

    /**
     * 第一阶段时间
     */
    @ApiModelProperty("第一阶段时间")
    @TableField("first_stage_date")
    private String firstStageDate;

    @ApiModelProperty("第一阶段信息列表")
    @TableField(value = "first_stage_info", typeHandler = StageInfo.StageInfoListTypeHandler.class)
    private List<StageInfo> firstStageInfo;

    @ApiModelProperty("第一阶段全部完成")
    @TableField("first_all_completed")
    private Boolean firstAllCompleted;

    @ApiModelProperty("第一阶段没有全部完成的原因")
    @TableField("first_incomplete_reason")
    private String firstIncompleteReason;

    @ApiModelProperty("第二阶段时间")
    @TableField("second_stage_date")
    private String secondStageDate;

    @ApiModelProperty("第二阶段信息列表")
    @TableField(value = "second_stage_info", typeHandler = StageInfo.StageInfoListTypeHandler.class)
    private List<StageInfo> secondStageInfo;

    @ApiModelProperty("第二阶段全部完成")
    @TableField("second_all_completed")
    private Boolean secondAllCompleted;

    @ApiModelProperty("第二阶段没有全部完成的原因")
    @TableField("second_incomplete_reason")
    private String secondIncompleteReason;

    @ApiModelProperty("重大进展")
    @TableField("major_progress")
    private String majorProgress;

    @ApiModelProperty("重大奖项")
    @TableField("major_awards")
    private String majorAwards;

    @TableField("industry_leader_cooperation")
    @ApiModelProperty("与行业龙头企业建立合作情况")
    private String industryLeaderCooperation;

    @ApiModelProperty("知识产权交易或转化" +
            "(如产生知识产权交易或转化，请补充说明转让技术、交易对象、交易金额等)")
    @TableField("ip_transaction")
    private String ipTransaction;

    @ApiModelProperty("未标明受到国创中心资助原因")
    @TableField("no_nctib_support_reason")
    private String noNctibSupportReason;

}
