<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fengyun.udf.uaa.mapper.area.AreaMapper" >
    <resultMap id="BaseResultMap" type="com.fengyun.udf.uaa.domain.area.Area">
        <id column="SN" property="sn" jdbcType="VARCHAR" />
        <result column="CODE" property="code" jdbcType="VARCHAR" />
        <result column="NAME" property="name" jdbcType="VARCHAR" />
        <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR" />
        <result column="LEVEL" property="level" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="VARCHAR" />
        <result column="SEQUENCE" property="sequence" jdbcType="INTEGER" />
        <result column="STATUS" property="status" jdbcType="VARCHAR" />
        <result column="PARENT_SN" property="parentSn" jdbcType="VARCHAR" />
        <result column="WORLD_ID" property="worldId" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Area_Special_Column_List" >
        SN, CODE, NAME, SHORT_NAME, LEVEL, TYPE, SEQUENCE, STATUS, PARENT_SN, WORLD_ID
    </sql>
    <sql id="Base_Column_List" >
        <include refid="Area_Special_Column_List" />
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select <include refid="Base_Column_List" ></include>
        from t_area t
        where t.SN = #{sn,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.fengyun.udf.uaa.domain.area.Area" >
        delete from t_area
        where SN = #{sn,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.fengyun.udf.uaa.domain.area.Area" >
        insert into t_area (SN, CODE, NAME,
            SHORT_NAME, LEVEL, TYPE,
            SEQUENCE, STATUS, PARENT_SN,
            WORLD_ID, VERSION, CREATED_ID,
            CREATED_DATE, UPDATED_ID, UPDATED_DATE
            )
        values (#{sn,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
          #{shortName,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR},
          #{sequence,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{parentSn,jdbcType=VARCHAR},
          #{worldId,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdId,jdbcType=VARCHAR},
          #{createdDate,jdbcType=TIMESTAMP}, #{updatedId,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}
          )
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.fengyun.udf.uaa.domain.area.Area" >
        update t_area
        set CODE = #{code,jdbcType=VARCHAR},
            NAME = #{name,jdbcType=VARCHAR},
            SHORT_NAME = #{shortName,jdbcType=VARCHAR},
            LEVEL = #{level,jdbcType=INTEGER},
            TYPE = #{type,jdbcType=VARCHAR},
            SEQUENCE = #{sequence,jdbcType=INTEGER},
            STATUS = #{status,jdbcType=VARCHAR},
            PARENT_SN = #{parentSn,jdbcType=VARCHAR},
            WORLD_ID = #{worldId,jdbcType=VARCHAR},
            VERSION = #{version,jdbcType=INTEGER},
            CREATED_ID = #{createdId,jdbcType=VARCHAR},
            CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            UPDATED_ID = #{updatedId,jdbcType=VARCHAR},
            UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP}
        where SN = #{sn,jdbcType=VARCHAR}
    </update>

    <select id="findByParentSnOrderBySequence" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" ></include>
        from t_area t
        where t.PARENT_SN = #{parentSn}
        order by SEQUENCE
    </select>
    <select id="findByLevelAndWorldIdOrderBySequence" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from t_area t
        where t.LEVEL = #{level}
        AND t.WORLD_ID = #{worldId}
        order by SEQUENCE
    </select>
    <select id="selectByPrimaryKeys" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" ></include>
        from t_area t
        WHERE t.SN in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" ></include>
        from t_area t
    </select>

    <select id="findTenantProvince" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from t_area t
        INNER JOIN t_tenant_area ta ON t.sn = ta.area_sn AND ta.tenant_id = #{tenantId}
        where t.LEVEL = #{level}
        AND t.WORLD_ID = #{worldId}
        order by SEQUENCE
    </select>

    <select id="findTenantChildren" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" ></include>
        from t_area t
        INNER JOIN t_tenant_area ta ON t.sn = ta.area_sn AND ta.tenant_id = #{tenantId}
        where t.PARENT_SN = #{parentSn}
        order by SEQUENCE
    </select>

</mapper>