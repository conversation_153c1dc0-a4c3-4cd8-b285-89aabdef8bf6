package com.fengyun.udf.uaa.service.login;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.config.OAuth2Properties;
import com.fengyun.udf.security.dto.AuthUser;
import com.fengyun.udf.security.util.AuthUtils;
import com.fengyun.udf.uaa.constant.Constants;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.mapper.system.UserMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class LoginService {

    private static final String BASIC_ = "Basic ";
    private static final String DEFAULT_CLIENT_TYPE = "pc";

    @Autowired
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Autowired
    private ClientDetailsService clientDetailsService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private OAuth2Properties oAuth2Properties;

    @Autowired
    private UserMapper userMapper;


    private String clientAuthorization(String clientType) {
        if (StringUtils.isBlank(clientType)) {
            clientType = DEFAULT_CLIENT_TYPE;
        }
        List<OAuth2Properties.WebClientConfiguration> clients = oAuth2Properties.getWebClientConfigurations();
        if (clients == null) {
            throw new ValidationException("client", "请求头中无client信息");
        }
        String authorization = "";
        for (OAuth2Properties.WebClientConfiguration webClientConfiguration : clients) {
            if (clientType.equals(webClientConfiguration.getClientType())) {
                String clientId = webClientConfiguration.getClientId();
                String secret = webClientConfiguration.getSecret();
                byte[] bytes = (clientId + ":" + secret).getBytes();
                authorization = Base64.getEncoder().encodeToString(bytes);
                break;
            }
        }
        return BASIC_ + authorization;
    }

    public ClientDetails getClient(String clientType) {
        String authorization = clientAuthorization(clientType);
        if (StringUtils.isBlank(authorization) || !authorization.startsWith(BASIC_)) {
            throw new ValidationException("client", "请求头中无client信息");
        }
        String[] tokens = AuthUtils.extractAndDecodeHeader(authorization);
        if (tokens.length != 2) {
            throw new ValidationException("client", "请求头错误");
        }
        String clientId = tokens[0];
        String clientSecret = tokens[1];

        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        if (!passwordEncoder.matches(clientSecret, clientDetails.getClientSecret())) {
            throw new ValidationException("client", "请求头错误");
        }
        return clientDetails;
    }

    public OAuth2AccessToken getAccessToken(Authentication authentication, String clientType) {
        ClientDetails clientDetails = getClient(clientType);
        TokenRequest tokenRequest = new TokenRequest(MapUtil.newHashMap(), clientDetails.getClientId(), clientDetails.getScope(), "password");

        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);
        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);
        OAuth2AccessToken token = authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
        return token;
    }

    public Map<String, Object> getAccessTokenMp(Authentication authentication, String clientType, String openId) {
        ClientDetails clientDetails = getClient(clientType);
        TokenRequest tokenRequest = new TokenRequest(MapUtil.newHashMap(), clientDetails.getClientId(), clientDetails.getScope(), "password");

        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);
        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);
        OAuth2AccessToken token = authorizationServerTokenServices.createAccessToken(oAuth2Authentication);

        Map<String, Object> map = new HashMap<>();
        map.put("openId", openId);
        map.put("token", token);
        return map;
    }

    public boolean isExistByTel(String tel) {
        LambdaQueryWrapper<User> query = new LambdaQueryWrapper<>();
        query.select(User::getTel)
                .eq(User::getTel, tel).eq(User::getType, UserType.member.name());
        long integer = userMapper.selectCount(query);
        return integer >= 1;
    }

    // 用户登录检查
    public boolean checkUserIsEn(AuthUser user) {
        Object o = userMapper.selectObjs(new LambdaQueryWrapper<User>().select(User::getUserType).eq(User::getId, user.getId())).get(0);
        return o != null && (int) o == 1;
    }

    public void checkEnUserCanLogin(AuthUser user) {
        User checkUser = userMapper.selectById(user.getId());
        if (StringUtils.isEmpty(checkUser.getAuditStatus())) {
            return;
        }
        if (checkUser.getAuditStatus().equals(Constants.EN_REG_STATUS_PEN)) {
            throw new ValidationException("error", "该账号正在审核, 审核通过后会以短信方式通知");
        }
        if (checkUser.getAuditStatus().equals(Constants.EN_REG_STATUS_DEAD)) {
            throw new ValidationException("error", "该账号审核不通过，请确认信息后重新注册");
        }
    }
}
