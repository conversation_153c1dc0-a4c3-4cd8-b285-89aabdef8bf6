package com.fengyun.udf.uaa.domain.flowform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 项目情况介绍
* </p>
*
* <AUTHOR>
* @since 2022-03-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("flow_form_condition")
@Api(hidden = true)
public class Condition extends BaseDomain implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "立项依据")
    @TableField("project_basis")
    private String projectBasis;

    @ApiModelProperty(value = "课题名称")
    @TableField("subject_name")
    private String subjectName;

    @ApiModelProperty(value = "研究内容")
    @TableField("research_content")
    private String researchContent;

    @ApiModelProperty(value = "研究方法")
    @TableField("research_method")
    private String researchMethod;

    @ApiModelProperty(value = "可行性分析")
    @TableField("feasibility_analysis")
    private String feasibilityAnalysis;

    @ApiModelProperty(value = "专利情况")
    @TableField("patent_situation")
    private String patentSituation;

    @ApiModelProperty(value = "补充文件说明")
    @TableField("attachments")
    private String attachments;
}
