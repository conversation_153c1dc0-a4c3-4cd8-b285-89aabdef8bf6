package com.fengyun.udf.uaa.dto;

import com.alibaba.fastjson.TypeReference;
import com.fengyun.udf.uaa.handler.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@ApiModel("附件存储")
@Data
public class AttachmentDTO implements Serializable {

    private static final long serialVersionUID = -8104020394413685228L;

    @ApiModelProperty(value = "附件文件名", required = false)
    private String name;

    @ApiModelProperty(value = "附件路径", required = true)
    private String url;

    @ApiModelProperty(value = "文件大小", required = false)
    private Long size;

    /**
     * json处理器
     */
    public static class AttachmentListTypeHandler extends ListTypeHandler<AttachmentDTO> {

        @Override
        protected TypeReference<List<AttachmentDTO>> specificType() {
            return new TypeReference<List<AttachmentDTO>>() {
            };
        }

    }
}
