package com.fengyun.udf.uaa.web.rest.inspection.end;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.aspect.annotation.AuditLog;
import com.fengyun.udf.constant.ReturnCode;
import com.fengyun.udf.dto.ReturnResultDTO;
import com.fengyun.udf.resource.BaseResource;
import com.fengyun.udf.uaa.dto.request.declareproject.DeclareEntImportErrorDTO;
import com.fengyun.udf.uaa.dto.request.inspection.end.DeclareEntEndInspectionAuditDTO;
import com.fengyun.udf.uaa.dto.request.inspection.end.DeclareEntEndInspectionQueryDTO;
import com.fengyun.udf.uaa.dto.response.inspection.end.DeclareEntEndAttachmentDetailDTO;
import com.fengyun.udf.uaa.dto.response.inspection.middle.*;
import com.fengyun.udf.uaa.service.declareproject.DeclareProjectService;
import com.fengyun.udf.uaa.service.inspection.end.DeclareEntEndInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * 2024/7/22
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Validated
@RestController
@SuppressWarnings("all")
@RequestMapping("/api/platform/declare/inspection/end")
@Api(value = "项目申报-结题验收-管理端", tags = "项目申报-结题验收-管理端")
@PreAuthorize("@pms.hasPermission('management')")
public class DeclareEntEndInspectionPlatformController extends BaseResource {

    @Autowired
    private DeclareEntEndInspectionService middleInspectionService;

    @Autowired
    private DeclareProjectService declareProjectService;

    @GetMapping("/basic/detail")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/basic/detail",
            methodDescription = "获取单位基本信息",
            httpMethod = "GET")
    @ApiOperation(value = "获取单位基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<UnitBasicInfoDetailDTO>> getUnitBasicInfoDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getUnitBasicInfoDetail(projectId)
        );
    }

    @GetMapping("/completion/detail")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/completion/detail",
            methodDescription = "获取项目完成情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目完成情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectCompletionDetailDTO>> getProjectCompletionDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectCompletionDetail(projectId)
        );
    }

    @GetMapping("/participant/detail")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/participant/detail",
            methodDescription = "获取项目人员情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目人员情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectParticipantDetailDTO>> getProjectParticipantDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectParticipantDetail(projectId)
        );
    }

    @GetMapping("/funding/detail")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/funding/detail",
            methodDescription = "获取项目经费到位与使用情况",
            httpMethod = "GET")
    @ApiOperation(value = "获取项目经费到位与使用情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<ProjectFundingDetailDTO>> getProjectFundingDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getProjectFundingDetail(projectId)
        );
    }

    @GetMapping("/attachment/detail")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/attachment/detail",
            methodDescription = "获取附件",
            httpMethod = "GET")
    @ApiOperation(value = "获取附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "子项目id", required = true, dataType = "String", paramType = "query")
    })
    public ResponseEntity<ReturnResultDTO<DeclareEntEndAttachmentDetailDTO>> getAttachmentDetail(
            @Valid @NotBlank(message = "项目id不能为空") String projectId) {
        return prepareReturnResult(ReturnCode.SUCCESS.getCode(),
                middleInspectionService.getAttachmentDetail(projectId)
        );
    }

    @PostMapping("/audit")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/audit",
            methodDescription = "审核结题验收",
            httpMethod = "POST")
    @ApiOperation(value = "审核结题验收")
    public ResponseEntity<ReturnResultDTO<?>> auditEndInspection(
            @Valid @RequestBody DeclareEntEndInspectionAuditDTO dto) {
        middleInspectionService.auditEndInspection(dto);
        return prepareReturnResult();
    }

    @GetMapping("/page/audit")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/page/audit",
            methodDescription = "结题验收分页列表-审核",
            httpMethod = "GET")
    @ApiOperation(value = "结题验收分页列表-审核")
    public ResponseEntity<ReturnResultDTO<?>> auditEndInspection(
            Page page, @ModelAttribute DeclareEntEndInspectionQueryDTO query) {
        middleInspectionService.pageAudit(page, query);
        return prepareReturnResult(page);
    }

    @GetMapping("/initiate")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/initiate",
            methodDescription = "结题验收-发起",
            httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "父项目id", required = true, dataType = "String", paramType = "query"),
    })
    @ApiOperation(value = "结题验收-发起")
    public ResponseEntity<ReturnResultDTO<?>> toEndInspectionStage(String id) {
        declareProjectService.toEndInspectionStage(id);
        return prepareReturnResult();
    }

    @PostMapping("/result/import")
    @AuditLog(
            method = "/api/platform/declare/inspection/middle/result/import",
            methodDescription = "结题验收-结果导入",
            httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "父项目id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "file", value = "父项目id", required = true, dataType = "file", paramType = "form"),
    })
    @ApiOperation(value = "结题验收-通过名单结果导入")
    public ResponseEntity<ReturnResultDTO<?>> importReviewRecord(
            @Valid @NotBlank(message = "申报信息id不能为空") String id,
            @Valid @NotNull(message = "申报信息id不能为空") MultipartFile file)
            throws IOException {
        List<DeclareEntImportErrorDTO> errorList = declareProjectService.importEndInspectionEntList(id, file);
        if(CollUtil.isNotEmpty(errorList)) {
            return prepareReturnResult(ReturnCode.VALIDATION_ERROR.getCode(), errorList);
        }
        return prepareReturnResult();
    }

}
