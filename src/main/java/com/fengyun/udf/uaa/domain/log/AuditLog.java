package com.fengyun.udf.uaa.domain.log;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * Created by wangg on 2019/7/15.
 */
@Data
@TableName("t_audit_log")
public class AuditLog {

    @TableId("ID")
    private String id;

    @TableField("METHOD_API")
    private String methodApi;

    @TableField("METHOD_DESCRIPTION")
    private String methodDescription;

    @TableField("HTTP_METHOD")
    private String httpMethod;

    @TableField("STATUS")
    private boolean status;

    @TableField("REQUEST_IP")
    private String requestIp;

    @TableField("REQUEST_DURATION")
    private Long requestDuration;

    @TableField("EXCEPTION_CONTENT")
    private String exceptionContent;

    @TableField("CREATED_ID")
    private String createdId;

    @TableField("CREATED_DATE")
    private ZonedDateTime createdDate;
}
