package com.fengyun.udf.uaa.domain.inspection.middle;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fengyun.udf.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "declare_ent_middle_achievement_paper")
@TableName(value = "declare_ent_middle_achievement_paper", autoResultMap = true)
public class DeclareEntMiddleAchievementPaper extends BaseDomain {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @TableField("project_id")
    private String projectId;

    /**
     * 论文标题
     */
    @ApiModelProperty("论文标题")
    @TableField("paper_title")
    private String paperTitle;

    /**
     * 第一作者单位
     */
    @ApiModelProperty("第一作者单位")
    @TableField("first_author_affiliation")
    private String firstAuthorAffiliation;

    /**
     * 期刊名称
     */
    @ApiModelProperty("期刊名称")
    @TableField("journal_name")
    private String journalName;

    /**
     * 影响因子
     */
    @ApiModelProperty("影响因子")
    @TableField("impact_factor")
    private String impactFactor;

    /**
     * 是否受到资助
     */
    @ApiModelProperty("是否受到资助")
    @TableField("nctib_supported")
    private Boolean nctibSupported;

    @ApiModelProperty("排序")
    @TableField("sort_num")
    private Integer sortNum;

}
