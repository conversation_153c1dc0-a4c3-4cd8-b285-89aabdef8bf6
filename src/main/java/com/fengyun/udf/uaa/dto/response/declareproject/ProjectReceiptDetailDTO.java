package com.fengyun.udf.uaa.dto.response.declareproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fengyun.udf.dto.AbstractDTO;
import com.fengyun.udf.interceptor.annotation.DictData;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 2022/10/20
 * 合同详情
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProjectReceiptDetailDTO extends AbstractDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "收据")
    private AttachmentDTO receipt;
    @ApiModelProperty(hidden = true)
    private String receiptStr;

    @ApiModelProperty(value = "收据模板")
    private AttachmentDTO template;
    @ApiModelProperty(hidden = true)
    private String templateStr;

    @ApiModelProperty(value = "审核状态")
    @DictData(target = "auditStatusStr", codeType = "PROJECT_MATERIAL_STATUS_BACK")
    private String auditStatus;
    private String auditStatusStr;

    @ApiModelProperty(value = "申报类别")
    @DictData(target = "declareTypeStr", codeType = "DECLARE_TYPE_BAK")
    private String declareType;
    private String declareTypeStr;

    @ApiModelProperty(value = "审核意见")
    private String auditRemark;

    @ApiModelProperty(value = "提交日期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm" ,timezone="GMT+8")
    private ZonedDateTime submitDate;

    @ApiModelProperty("项目名称")
    private String subjectName;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty("承担单位")
    private String responsibleName;

    @ApiModelProperty(value = "项目状态")
    @DictData(target = "projectStatusStr",codeType = "NEW_PROJECT_STATUS")
    private String projectStatus;
    private String projectStatusStr;

    @ApiModelProperty(value = "标题")
    private String declareName;

    @ApiModelProperty(value = "审核记录")
    private List<RuNodeTaskHistoryShowDTO> history;

    @ApiModelProperty(value = "收据分类,第一次-1,第二次-2,第三次-3")
    @DictData(target = "categoryName", codeType = "RECEIPT_CATEGORY")
    private String category;

    @ApiModelProperty(value = "收据分类,第一次-1,第二次-2,第三次-3")
    private String categoryName;

    @ApiModelProperty(value = "交款单位")
    private String company;

    @ApiModelProperty(value = "收款事由")
    private String reason;

    @ApiModelProperty(value = "收款时间")
    private LocalDate date;

    @ApiModelProperty(value = "收款金额(元)")
    private Double amount;

    @ApiModelProperty(value = "联系人")
    private String contacts;

    @ApiModelProperty(value = "联系电话")
    private String contactNum;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "开户名")
    private String name;

    @ApiModelProperty(value = "开户行")
    private String bank;

    @ApiModelProperty(value = "银行账号")
    private String account;

    @ApiModelProperty(value = "账户类型")
    private String type;

    @ApiModelProperty(value = "收据编号")
    private Long receiptNo;

}
