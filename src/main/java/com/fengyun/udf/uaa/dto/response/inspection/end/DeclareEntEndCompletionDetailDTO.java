package com.fengyun.udf.uaa.dto.response.inspection.end;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 2024/7/19
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "DeclareEntEndCompletionDetailDTO", description = "DeclareEntEndCompletionDetailDTO")
public class DeclareEntEndCompletionDetailDTO{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 第一阶段时间
     */
    @ApiModelProperty("第一阶段时间")
    private String firstStageDate;

    /**
     * 第一阶段进度安排
     */
    @ApiModelProperty("第一阶段进度安排")
    private String firstSchedule;

    /**
     * 第一阶段完成情况
     */
    @ApiModelProperty("第一阶段完成情况")
    private String firstCompletion;

    /**
     * 第二阶段时间
     */
    @ApiModelProperty("第二阶段时间")
    private String secondStageDate;

    /**
     * 第二阶段进度安排
     */
    @ApiModelProperty("第二阶段进度安排")
    private String secondSchedule;

    /**
     * 第二阶段完成情况
     */
    @ApiModelProperty("第二阶段完成情况")
    private String secondCompletion;

    /**
     * 第三阶段时间
     */
    @ApiModelProperty("第三阶段时间")
    private String thirdStageDate;

    /**
     * 第三阶段进度安排
     */
    @ApiModelProperty("第三阶段进度安排")
    private String thirdSchedule;

    /**
     * 第三阶段完成情况
     */
    @ApiModelProperty("第三阶段完成情况")
    private String thirdCompletion;

}
