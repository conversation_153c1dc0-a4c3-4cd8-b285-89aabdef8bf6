package com.fengyun.udf.uaa.service.register;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fengyun.udf.cache.redis.service.RedisClient;
import com.fengyun.udf.domain.BaseDomain;
import com.fengyun.udf.dto.RedisObjectDTO;
import com.fengyun.udf.exception.ValidationException;
import com.fengyun.udf.security.util.SecurityUtils;
import com.fengyun.udf.sms.*;
import com.fengyun.udf.sms.util.SendUtil;
import com.fengyun.udf.sms.util.SmsTemplate;
import com.fengyun.udf.uaa.config.SmsProperties;
import com.fengyun.udf.uaa.constant.UserType;
import com.fengyun.udf.uaa.domain.system.EnRegister;
import com.fengyun.udf.uaa.domain.system.User;
import com.fengyun.udf.uaa.domain.system.UserThird;
import com.fengyun.udf.uaa.dto.AttachmentDTO;
import com.fengyun.udf.uaa.dto.request.register.EnRegisterDto;
import com.fengyun.udf.uaa.dto.request.register.SignInDTO;
import com.fengyun.udf.uaa.dto.request.system.QueryRegisterUserDto;
import com.fengyun.udf.uaa.dto.response.system.EnRegisterListDto;
import com.fengyun.udf.uaa.dto.response.system.PerRegisterListDto;
import com.fengyun.udf.uaa.mapper.system.UserAuditMapper;
import com.fengyun.udf.uaa.mapper.system.UserMapper;
import com.fengyun.udf.uaa.mapper.system.UserThirdMapper;
import com.fengyun.udf.uaa.service.BaseService;
import com.fengyun.udf.uaa.service.system.UserService;
import com.fengyun.udf.uaa.util.BeanUtils;
import com.fengyun.udf.util.CaptchaGenerator;
import com.fengyun.udf.util.UUIDGenerator;
import com.fengyun.udf.util.Validators;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.fengyun.udf.uaa.constant.Constants.*;


/**
 * <AUTHOR>
 * @version version 1.0
 */
@Service
@Transactional
@Slf4j
public class RegisterService extends BaseService {
    public static final String SMS_CAPTCHA_CODE = "smsCaptchaCode";
    @Autowired
    protected PasswordEncoder passwordEncoder;

    @Autowired
    private HuyiSMSClient huyiSMSClient;

    @Autowired
    private AlicomSMSClient alicomSMSClient;

    @Autowired
    private SMSClient smsClient;

    @Autowired
    private SzxxzxSMSClient szxxzxSMSClient;

    @Autowired
    private JjxySMSClient jjxySMSClient;

    @Autowired
    private SzzdwdSMSClient szzdwdSMSClient;

    @Autowired
    private FykjSMSClient fykjSMSClient;

    @Autowired
    private SmsProperties smsProperties;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private UserService userService;

    @Autowired
    private UserThirdMapper userThirdMapper;

    @Autowired
    private UserAuditMapper auditMapper;

    public String getUserSignUpChannel(HttpServletRequest request) {
        String agent = request.getHeader("user-agent");
        if (agent != null && agent.toLowerCase().contains("micromessenger")) {
            return SIGN_UP_CHANNEL.mini_app.name();
        }

        return SIGN_UP_CHANNEL.pc.name();
    }

    public boolean checkSmsCaptchaCode(final String tel, final String smsCaptchaCode) {
        String redis_smsCaptchaCode = redisTemplate.opsForValue().get(SMS_CAPTCHA_CODE + SEPARATOR_COLON + tel);
        if (StringUtils.equals(smsCaptchaCode, redis_smsCaptchaCode)) {
            return true;
        }
        return false;
    }

    public void removeSmsCaptchaCodeFromCache(final String tel) {
        //删除缓存中的验证码
        redisTemplate.delete(SMS_CAPTCHA_CODE + SEPARATOR_COLON + tel);
    }

    public void sendSmsCaptchaCode(String tel, String type) throws Exception {
        //生成手机动态码
        String smsCaptchaCode = CaptchaGenerator.generateSmsCaptchaCode(6);
        log.info("短信类型为: {}", type);
        log.info("生成的短信验证码为: {}", smsCaptchaCode);

        SmsTemplate template = null;
        switch (type) {
            case SMS_TYPE_REGISTER:
                template = smsProperties.getRegister();
                break;
            case SMS_TYPE_LOGIN:
                template = smsProperties.getLogin();
                break;
            case SMS_TYPE_REST_PASSWORD:
                template = smsProperties.getRestPassword();
                break;
        }
        if (template != null) {
            //验证码存入缓存
            redisTemplate.opsForValue().set(SMS_CAPTCHA_CODE + SEPARATOR_COLON + tel, smsCaptchaCode, 1800, TimeUnit.SECONDS);
            //发送短信
            Map<String, String> paramMap = new HashMap();
            paramMap.put(SMS_CAPTCHA_CODE, smsCaptchaCode);
            switch (smsProperties.getSmsClient()) {
                case FY:
                    SendUtil.sendSms(tel, template, paramMap, smsClient);
                    break;
                case ALI:
                    SendUtil.sendSms(tel, template, paramMap, alicomSMSClient);
                    break;
                case HUYI:
                    SendUtil.sendSms(tel, template, paramMap, huyiSMSClient);
                    break;
                case SZXXZX:
                    SendUtil.sendSms(tel, template, paramMap, szxxzxSMSClient);
                    break;
                case JJXY:
                    SendUtil.sendSms(tel, template, paramMap, jjxySMSClient);
                    break;
                case SZZDWD:
                    SendUtil.sendSms(tel, template, paramMap, szzdwdSMSClient);
                    break;
                case FYKJ:
                    SendUtil.sendSms(tel, template, paramMap, fykjSMSClient);
                    break;
            }

        }
    }

    private void sendRegisterMessage(String tel, String type, String name) throws Exception {
        SmsTemplate template = null;
        switch (type) {
            case SMS_TYPE_REST_REG_FAIL:
                template = smsProperties.getRegisterFail();
                break;
            case SMS_TYPE_REST_REG_SUC:
                template = smsProperties.getRegisterSuccess();
                break;
        }
        Map<String, String> paramMap = new HashMap();
        if (StringUtils.isNotBlank(name)) {
            paramMap.put("name", name);
        }
        switch (smsProperties.getSmsClient()) {
            case FY:
                SendUtil.sendSms(tel, template, paramMap, smsClient);
                break;
            case ALI:
                SendUtil.sendSms(tel, template, paramMap, alicomSMSClient);
                break;
            case HUYI:
                SendUtil.sendSms(tel, template, paramMap, huyiSMSClient);
                break;
            case SZXXZX:
                SendUtil.sendSms(tel, template, paramMap, szxxzxSMSClient);
                break;
            case JJXY:
                SendUtil.sendSms(tel, template, paramMap, jjxySMSClient);
                break;
            case SZZDWD:
                SendUtil.sendSms(tel, template, paramMap, szzdwdSMSClient);
                break;
            case FYKJ:
                SendUtil.sendSms(tel, template, paramMap, fykjSMSClient);
                break;
        }
    }

    /**
     * 手机号注册，已选择身份就给用户分配身份
     *
     * @param dto
     * @return
     */
    public String[] signInUser(SignInDTO dto) {
        return signInUser(dto, false);
    }

    // 企业用户注册
    public String[] signInUserByEnterprise(EnRegisterDto dto) {
        User user = new User();
        EnRegister info = new EnRegister();
        boolean isCreate = true;
        if (StringUtils.isEmpty(dto.getCreditCode())) {
            throw new ValidationException("error", "统一信用代码不能为空");
        }
        if (dto.getCreditCode().length() <= 17) {
            throw new ValidationException("error", "请输入正确的统一社会信用代码!");
        }
        if (isWebUserExistsByCode(dto.getCreditCode())) {
            User oldUser = getRegisteredEnUserByCode(dto.getCreditCode());
            if (oldUser.getAuditStatus().equals(EN_REG_STATUS_DEAD)) {
                user = oldUser;
                info = getRegisteredEnInfoByOldUserId(oldUser.getId());
                isCreate = false;
            } else {
                throw new ValidationException("error", "该企业已注册");
            }
        }
        if (StringUtils.isEmpty(dto.getTel())) {
            throw new ValidationException("error", "注册手机号不能为空");
        }
        if (isWebEnUserExistsByTel(dto.getTel(), dto.getCreditCode())) {
            throw new ValidationException("tel", "手机号已存在，不可注册！");
        }
        if (StringUtils.isEmpty(dto.getPassword())) {
            throw new ValidationException("error", "密码不能为空");
        }

        dto.setLoginName(dto.getCreditCode());

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        //添加用户

        // 是否生成userId
        if (isCreate) {
            user.setId(UUIDGenerator.getUUID());
            info.setId(UUIDGenerator.getUUID());
        }

        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.setLoginName(dto.getLoginName());
        user.setTel(dto.getTel());
        user.setNickName(dto.getNickName());
        user.setEmail(dto.getEmail());
        user.setStatus(STATUS_ACTIVE);
        user.setIsSys(NO);
        user.setType(UserType.member.name());
        user.setChannel(dto.getSignupChannel());
        user.setInviter(dto.getInviter());
        user.setAttachmentLicense(toJSONString(dto.getAttachmentLicense()));
        user.setUserType(dto.getUserType());
        user.setAuditStatus(EN_REG_STATUS_PEN);

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());

        info.setUserId(user.getId());
        BeanUtils.copyProperties(dto, info);

        // 创建新用户或覆盖原用户
        if (isCreate) {
            user.create("0");
            info.create("0");
            userMapper.insert(user);
            enRegisterMapper.insert(info);
        } else {
            user.update("0");
            info.update("0");
            userMapper.updateById(user);
            enRegisterMapper.updateById(info);
        }

        return new String[]{user.getId(), null};
    }


    // 个人用户注册
    public String[] signInUser(SignInDTO dto, boolean assignRoles) {
        String tel = dto.getTel();
        if (!checkSmsCaptchaCode(tel, dto.getSmsCaptchaCode())) {
            throw new ValidationException("smsCaptchaCode", "短信验证码不正确");
        }

        if (isWebUserExistsByTel(tel)) {
            throw new ValidationException("tel", "手机号已存在，不可注册！");
        }

        if (isWebUserExistsByLoginName(dto.getLoginName())) {
            throw new ValidationException("loginName", "用户名已存在，不可注册！");
        }

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        //添加用户
        User user = new User();
        user.setId(UUIDGenerator.getUUID());
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.setLoginName(dto.getLoginName());
        user.setTel(dto.getTel());
        user.setNickName(dto.getNickName());
        user.setEmail(dto.getEmail());
        user.setStatus(STATUS_ACTIVE);
        user.setIsSys(NO);
        user.setType(UserType.member.name());
        user.create("0");
        user.setChannel(dto.getSignupChannel());
        user.setInviter(dto.getInviter());
        user.setUserType(dto.getUserType());
        user.setAttachmentLicense(toJSONString(dto.getAttachmentLicense()));
        user.setCompanyName(dto.getCompanyName());
        user.setAuditStatus(EN_REG_STATUS_PASS);
        userMapper.insert(user);
        //删除缓存中的验证码
        removeSmsCaptchaCodeFromCache(dto.getTel());

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
        return new String[]{user.getId(), null};
    }

    public String[] signInByThirdWH(SignInDTO dto, String userId) {
        String tel = dto.getTel();
        if (isWebUserExistsByTel(tel)) {
            throw new ValidationException("tel", "手机号已存在，不可注册！");
        }

        if (isWebUserExistsByLoginName(dto.getLoginName())) {
            throw new ValidationException("loginName", "用户名已存在，不可注册！");
        }

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        //添加用户
        User user = new User();
        user.setId(userId);
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.setLoginName(dto.getLoginName());
        user.setTel(dto.getTel());
        user.setNickName(dto.getNickName());
        user.setEmail(dto.getEmail());
        user.setStatus(STATUS_ACTIVE);
        user.setIsSys(NO);
        user.setType(UserType.member.name());
        user.create("0");
        user.setChannel(dto.getSignupChannel());
        user.setInviter(dto.getInviter());
        userMapper.insert(user);

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
        return new String[]{user.getId(), null};
    }

    public String[] signInUserForPhone(SignInDTO dto) {
        String tel = dto.getTel();

        if (isWebUserExistsByTel(tel)) {
            throw new ValidationException("tel", "手机号已存在，不可注册！");
        }

        if (isWebUserExistsByLoginName(dto.getLoginName())) {
            throw new ValidationException("loginName", "用户名已存在，不可注册！");
        }

        List<String> userTypes = new ArrayList<>();
        userTypes.add(UserType.member.name());

        //添加用户
        User user = new User();
        user.setId(UUIDGenerator.getUUID());
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.setLoginName(dto.getLoginName());
        user.setTel(dto.getTel());
        user.setNickName(dto.getNickName());
        user.setEmail(dto.getEmail());
        user.setStatus(STATUS_ACTIVE);
        user.setIsSys(NO);
        user.setType(UserType.member.name());
        user.create("0");
        user.setChannel(dto.getSignupChannel());
        user.setInviter(dto.getInviter());
        userMapper.insert(user);

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
        return new String[]{user.getId(), null};
    }

    /**
     * 微信手机号注册登录
     *
     * @param tel
     * @param openId
     * @return
     */
    public User registerUser(String tel, String openId, String openType) {
        User user = new User();
        user.setId(UUIDGenerator.getUUID());
        user.setLoginName(tel);
        user.setTel(tel);
        user.setPassword(passwordEncoder.encode(getDefaultPassword()));
        user.setNickName(tel);
        user.setStatus(STATUS_ACTIVE);
        user.setIsSys(NO);
        user.setType(UserType.member.name());
        user.create("0");
        user.setChannel(SIGN_UP_CHANNEL.mini_app.name());
        userMapper.insert(user);

        addUserThird(openId, user.getId(), openType);

        cacheNickname(user.getId(), user.getLoginName(), user.getNickName());
        return user;
    }

    private void addUserThird(String openId, String userId, String thirdType) {
        if (StringUtils.isNotEmpty(openId)) {
            UserThird ut = new UserThird();
            ut.setId(UUIDGenerator.getUUID());
            ut.setUserId(userId);
            ut.setThirdType(thirdType);
            ut.setThirdKey(openId);
            userThirdMapper.insert(ut);
        }
    }

    public String getDefaultPassword() {
        return UUIDGenerator.getUUID().substring(5, 15);
    }

    private String getState(String type, String urlType) {
        String uuid = UUIDGenerator.getUUID();
        return uuid + SEPARATOR_HORIZONTAL + type + SEPARATOR_HORIZONTAL + urlType;
    }

    public void graphCaptchaCodeVerification(final String id, final String captchaCode) {
        //获取图形验证码
        RedisObjectDTO redisObjectDTO = new RedisObjectDTO("", "captcha", id);
        String captchaCode_cache = redisClient.getObject(redisObjectDTO);
        if (Validators.fieldBlank(captchaCode_cache)) {
            throw new com.fengyun.udf.exception.ValidationException("error", "验证码不存在");
        } else {
            //验证码比对
            if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(captchaCode, captchaCode_cache)) {
                throw new com.fengyun.udf.exception.ValidationException("error", "验证码不正确");
            }
        }
    }

    public void graphCaptchaCodeDelete(final String id) {
        RedisObjectDTO redisObjectDTO = new RedisObjectDTO("", "captcha", id);
        redisClient.deleteKey(redisObjectDTO);
    }


    // 注册用户列表

    /**
     * 个人用户
     *
     * @param dto
     */
    public Page<PerRegisterListDto> getPerRegisterPage(Page page, QueryRegisterUserDto dto) {
        IPage<User> iPage = userMapper.selectPage(page, new LambdaQueryWrapper<User>()
                .select(User::getId,User::getLoginName, User::getTel, User::getCompanyName, User::getEmail, User::getAttachmentLicense, User::getOrderState)
                .eq(User::getUserType, 2)
                .ge(notNull(dto.getFrom()), BaseDomain::getCreatedDate, dto.getFrom())
                .le(notNull(dto.getTo()), BaseDomain::getCreatedDate, dto.getTo())
                .like(notNull(dto.getName()), User::getLoginName, dto.getName()));
        List<PerRegisterListDto> list = new ArrayList<>();
        for (User user : iPage.getRecords()) {
            PerRegisterListDto info = new PerRegisterListDto();
            BeanUtils.copyProperties(user, info);
            info.setAttachmentLicense(parseArray(user.getAttachmentLicense(), AttachmentDTO.class));
            list.add(info);
        }
        page.setRecords(list);
        return page;
    }

    public Page<EnRegisterListDto> getEnRegisterPage(Page page, QueryRegisterUserDto dto) {
        List<EnRegisterListDto> list = auditMapper.selectAuditRegisterPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public void processEnRegister(String id, String status, String auditDesc) throws Exception {
        User user = userMapper.selectById(id);
        user.setAuditDesc(auditDesc);
        if (status.equals(EN_REG_STATUS_PASS)) {
            user.setAuditStatus(EN_REG_STATUS_PASS);
            userMapper.updateById(user);
            sendRegisterMessage(user.getTel(), SMS_TYPE_REST_REG_SUC, "");
        }
        if (status.equals(EN_REG_STATUS_DEAD)) {
            user.setAuditStatus(EN_REG_STATUS_DEAD);
            userMapper.updateById(user);
            sendRegisterMessage(user.getTel(), SMS_TYPE_REST_REG_FAIL, auditDesc);
        }
    }

    public void editOrderState(String id) {
        User user = userMapper.selectById(id);
        if (user != null) {
            if (user.getOrderState()) {
                user.setOrderState(false);
            } else {
                user.setOrderState(true);
            }
            user.update(SecurityUtils.getUserId());
            userMapper.updateById(user);
        }
    }

    private User getRegisteredEnUserByCode(String code) {
        return userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getLoginName, code));
    }

    private EnRegister getRegisteredEnInfoByOldUserId(String userId) {
        return enRegisterMapper.selectOne(new LambdaQueryWrapper<EnRegister>().eq(EnRegister::getUserId, userId));
    }
}
